import os
import re
import jwt
import boto3
import requests
from dotenv import load_dotenv


def _get_api_key() -> str:
    home_filepath = os.path.expanduser("~")
    guardrails_rc_filepath = os.path.join(home_filepath, ".guardrailsrc")

    api_key = os.environ.get("GUARDRAILS_TOKEN")

    if not api_key and os.path.exists(guardrails_rc_filepath):
        print(f"Reading GUARDRAILS_TOKEN from {guardrails_rc_filepath}")
        with open(guardrails_rc_filepath, "r") as f:
            for line in f:
                match = re.match(r"token\s*=\s*(?P<api_key>.+)", line)
                if match:
                    api_key = match.group("api_key").strip()
                    break
    else:
        print("Using GUARDRAILS_TOKEN environment variable")

    if not api_key:
        raise ValueError(
            "GUARDRAILS_TOKEN environment variable is not set or found in $HOME/.guardrailsrc"
        )

    return api_key


def whoami(api_key: str) -> str:
    verified_token = jwt.decode(
        api_key,
        algorithms=["HS256"],
        options={"verify_exp": False, "verify_signature": False},
    )
    return verified_token["sub"]


def upload_files(docs_dir: str, user_id: str) -> list[str]:
    s3_bucket = os.environ.get("AWS_S3_DOCUMENT_BUCKET")

    if not s3_bucket:
        raise ValueError("AWS_S3_DOCUMENT_BUCKET environment variable is not set")

    s3_client = boto3.client("s3")
    file_names = []
    for root, _, files in os.walk(docs_dir):
        for file in files:
            file_path = os.path.join(root, file)
            file_name = f"{user_id}/{file}"
            file_names.append(file_name)
            s3_client.upload_file(file_path, s3_bucket, file_name)
    return file_names


if __name__ == "__main__":
    load_dotenv("./.env")

    experiment_docs_dir = os.path.join(os.path.dirname(__file__), "dry-run-docs")
    historical_docs_dir = os.path.join(
        os.path.dirname(__file__), "dry-run-historical-data"
    )

    api_key = _get_api_key()
    user_id = whoami(api_key)

    uploaded_files = upload_files(experiment_docs_dir, user_id)
    historical_data_files = upload_files(historical_docs_dir, user_id)

    experiment = {
        "id": "dry-run",
        "name": "Chris Voss bot",
        "role": "AI chatbot advisor model mimicking Chris Voss, aimed at helping users with their negotiation skills in business and personal life",
        "source_data": {
            "docs": {
                # "misc": uploaded_files,
                # "knowledge_base": uploaded_files
                "historical_data": historical_data_files
            },
            # Each entry specifies what judges to run
            "evaluation_configuration": {
                "Self Harm": {
                    "name": "Self Harm",
                    "description": "Detects how the model interacts with content regarding self-harm.",
                },
                "No Financial Advice": {
                    "name": "No Financial Advice",
                    "description": "Searches for financial advice in the model's responses.",
                },
                # "Assistant Quality": {
                #     "name": "Assistant Quality",
                #     "description": "Evaluate the quality of the prompts & responses.",
                # },
                # "Limit Subject Area": {
                #     "name": "Limit Subject Area",
                #     "description": "Test how well your LLM is able to maintain a conversation within a set of defined topics.",
                # },
            },
            "generation_configuration": {
                "data_gen_mode": "coverage_focused",
                "max_topics": 10,
                "max_personas": 10,
                "max_conversations": 10,
            },
        },
        "application_id": "41bddba7-feaf-40e2-ba28-9daf22a1ec71",
    }

    response = requests.post(
        "http://localhost:48001/experiment?dry-run=true",
        json=experiment,
        headers={"x-api-key": api_key},
    )

    if response.status_code != 200:
        raise ValueError(f"Dry run failed: {response.status_code} - {response.text}")
