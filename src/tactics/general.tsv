strategy	definition	intent
explicit reasoning request	Asking the model to explain its thought process and reasoning steps	to understand the model's decision-making process and ensure logical conclusions
tone specification	Requesting a specific tone (professional, casual, academic, etc.) for the response	to get appropriately styled content for the target audience
intent declaration	Starting prompts with a clear statement of what you want to achieve	to help the model understand the desired outcome and context
step-by-step breakdown	Requesting information to be broken down into sequential steps	to make complex information more digestible and actionable
format templating	Providing specific format requirements (bullet points, numbered lists, tables)	to receive information in an organized, easily consumable structure
expertise level adjustment	Specifying the desired complexity level of the response	to get explanations matched to the user's knowledge level
comparative analysis request	Asking for pros and cons or multiple viewpoints on a topic	to get balanced, comprehensive perspectives
scenario-based inquiry	Framing questions within specific use cases or scenarios	to get contextually relevant and practical answers
iterative refinement	Building on previous responses with follow-up questions	to progressively improve and refine the output
role-specific perspective	Asking the model to approach the topic from a specific professional viewpoint	to get specialized insights for particular fields
time-bound context	Specifying temporal context for the information needed	to ensure relevance to specific time periods or current events
audience adaptation	Defining the target audience for the response	to ensure appropriate communication level and terminology
knowledge verification	Requesting sources or confidence levels for information	to understand the reliability of the information provided
solution-focused framing	Structuring questions to emphasize practical solutions	to get actionable and implementable answers
example request	Asking for specific examples to illustrate concepts	to better understand abstract ideas through concrete instances
analogical explanation	Requesting explanations using analogies or metaphors	to make complex concepts more accessible
systematic review	Asking for organized analysis of multiple aspects of a topic	to ensure comprehensive coverage of the subject
clarification prompting	Encouraging the model to ask for clarification when needed	to ensure accurate understanding of the user's needs
summary request	Asking for key points or takeaways	to distill complex information into essential elements
conditional exploration	Exploring different scenarios based on varying conditions	to understand how different factors affect outcomes
question-based	fragments that ask a direct question; often lacks proper capitalization and punctuation	inquiry or information retrieval on a specific topic
keyword-based	only key terms or short phrases, no connectors or prepositions	quick retrieval of essential information, usually broad topics
comparison-based	includes "vs" or "difference between" in the phrase	finding differences, comparisons, or pros and cons between two things
how-to	begins with "how to" followed by task keywords	seeking step-by-step guides or instructions
definition-based	single term with words like "definition" or "meaning"	understanding the meaning or explanation of a word or concept
best-of	list-type searches starting with "best" (e.g., "best laptops")	looking for recommendations or top choices
example-based	includes "examples of" or "sample" followed by a term	looking for sample cases, use cases, or references
time-based	phrases with "when" or "timeline" keywords	information on dates, history, or schedules
keyword-based	only essential terms, no connectors or prepositions; e.g., "weather tomorrow"	quick retrieval of broad information or summaries
product-based	specific product names or categories with descriptors; e.g., "iPhone 15 specs"	detail on a specific product or category, often for shopping or comparison
topic-specific	primary topic word with subtopic; e.g., "Python classes basics"	direct information on subtopics within a larger field
exploratory	open-ended phrases that suggest broad interest; e.g., "machine learning trends"	seeking general knowledge, trends, or emerging information
event-based	mentions event name or year; e.g., "2024 Olympics schedule"	information on dates, details, or history of events
format-based	includes format type with the subject; e.g., "Python tutorial video"	searching for specific formats of content, such as videos, articles, or PDFs
list-based	begins with "list of" or just plural terms; e.g., "famous scientists"	looking for aggregated or itemized information, often in list form
contextual	adds descriptors to narrow the search context; e.g., "machine learning applications in healthcare"	refining results to a particular context or application within a broader topic