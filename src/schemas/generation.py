from enum import Enum
from typing import Dict, Optional
from src.schemas.experiments import SourceData


class GenerateArgs:
    def __init__(
        self,
        user_id: str,
        role: str,
        experiment_id: str,
        source_data: SourceData,
        user_description: str,
        auth_header: Dict[str, str],
        dry_run: Optional[bool] = False,
    ):
        self.user_id = user_id
        self.role = role
        self.experiment_id = experiment_id
        self.source_data = source_data
        self.user_description = user_description
        self.auth_header = auth_header
        self.dry_run = dry_run


class GenerationMethods(str, Enum):
    persona = "persona"
    multiturn = "multiturn"
    manyturn = "manyturn"
    magpie = "magpie"
