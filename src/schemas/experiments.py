from pydantic import BaseModel, Field
from typing import Dict, List, Optional


class DocumentReferences(BaseModel):
    misc: Optional[List[str]] = Field(
        default_factory=list,
        deprecated=True,
    )
    knowledge_base: List[str] = Field(
        default_factory=list,
    )
    historical_data: List[str] = Field(
        default_factory=list,
    )


class SourceData(BaseModel):
    topics: Optional[List[str]] = Field(default_factory=list)
    personas: Optional[List[str]] = Field(default_factory=list)
    docs: DocumentReferences = Field(
        default_factory=DocumentReferences,
    )
    evaluation_configuration: Optional[dict] = Field(default=None)
    generation_configuration: Optional[dict] = Field(default=None)

    def get_risks(self) -> List[dict[str, str]]:
        """
        Returns a list of selected risk types from the evaluation configuration.

        Returns:
            List[str]: A list of risk type names that are selected for evaluation.
        """
        if not self.evaluation_configuration:
            return []

        return list(self.evaluation_configuration.values())


class CreateExperiment(BaseModel):
    role: str
    id: str
    source_data: SourceData = Field(
        default_factory=SourceData,
    )
    user_description: str = Field(default="")


class CreateTopicRequest(BaseModel):
    new_topic: str
    is_adapted_conversation: bool


class CreatePersonaRequest(BaseModel):
    new_persona: str
    is_adapted_conversation: bool


class CreateAdaptabilityMessageRequest(BaseModel):
    test_id: Optional[str] = ""
    experiment_id: str
    action: str
    dry_run: Optional[bool] = False


class CreateTopicPersonaRequest(BaseModel):
    personas: List[str]
    topics: List[str]
    is_adapted_conversation: bool


class CreateTestRequest(BaseModel):
    role: str
    persona: str
    topic: str
    response: str
    tactics: Optional[List[str]] = Field(default_factory=list)
    parent_test_id: Optional[str] = Field(default=None)
    conversation_id: Optional[str] = Field(default=None)
    parent_test_prompt: Optional[str] = Field(default=None)
    generation_method: Optional[str] = Field(default="multiturn")
    risk_type: Optional[str] = Field(default=None)
    styles: Optional[List[Dict[str, str]]] = Field(default=None)
    current_depth: int
    max_depth: int
    is_original: Optional[bool] = Field(default=None)
    original_test_id: Optional[str] = Field(default=None)
    source_tactics: Optional[List[str]] = Field(default_factory=list)
    is_adapted_conversation: bool
