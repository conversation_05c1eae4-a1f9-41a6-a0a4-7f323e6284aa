import httpx
import jwt
from schemas.generation import GenerateArgs
from utils.auth import verify_api_key, verify_user_token
from schemas.experiments import (
    CreateExperiment,
    CreatePersonaRequest,
    CreateTopicRequest,
    CreateTopicPersonaRequest,
)
from fastapi import FastAP<PERSON>, Header, BackgroundTasks, HTTPException, Request, Query
from fastapi.responses import JSONResponse
import boto3
from botocore.config import Config
import json

from settings import settings

from persona_topic_generator import persona_topic_generator
from utils.logger import logger
import os

CONTROL_PLANE_URL = settings.control_plane_url

app = FastAPI()

s3_client = boto3.client(
    "s3", config=Config(signature_version="s3v4", region_name="us-west-1")
)


def get_verified_token(request: Request):
    verified_token = {}

    user_token = request.headers.get("Authorization")
    api_key = request.headers.get("x-api-key")
    if user_token:
        verified_token = verify_user_token(user_token)
    elif api_key:
        verified_token = verify_api_key(api_key)

    return verified_token


def get_user(request) -> str:
    verified_token = get_verified_token(request)

    if not verified_token:
        raise HTTPException(status_code=401, detail="Unauthorized")

    user_id = verified_token.get("sub")

    if not user_id:
        raise HTTPException(status_code=403, detail="Forbidden")

    return user_id


@app.middleware("http")
async def authenticate(request, call_next):
    try:
        if request.url.path == "/health" or settings.disable_auth_bool:
            return await call_next(request)

        verified_token = get_verified_token(request)

        if not verified_token:
            raise HTTPException(status_code=401, detail="Unauthorized")
        permissions = verified_token.get("permissions", [])
        if "threat_tester_invite:true" in permissions:
            return await call_next(request)
        else:
            raise HTTPException(status_code=403, detail="Insufficient permissions")
    except jwt.ExpiredSignatureError:
        return JSONResponse(
            content={"detail": "Unauthorized", "status_code": 401}, status_code=401
        )
    except jwt.InvalidSignatureError:
        return JSONResponse(
            content={"detail": "Unauthorized", "status_code": 401}, status_code=401
        )
    except jwt.InvalidTokenError:
        return JSONResponse(
            content={"detail": "Unauthorized", "status_code": 401}, status_code=401
        )
    except HTTPException as e:
        return JSONResponse(
            content={"detail": e.detail, "status_code": e.status_code},
            status_code=e.status_code,
        )


@app.get("/health")
async def health_check():
    return {"status": "ok"}


OUTPUT_DIR = "cache"


@app.get("/cache/{path:path}")
async def get_cached_data(request: Request, path: str):
    """Returns whatever file is at .json of a path"""
    logger.info(f"==> GET /cache/{path}.json")
    get_user(request)
    safe_path = os.path.normpath(os.path.join(OUTPUT_DIR, f"{path}.json"))

    if not safe_path.startswith(OUTPUT_DIR):
        logger.error(f"Invalid path: {safe_path}")
        raise HTTPException(status_code=400, detail="Invalid path")

    if not os.path.exists(safe_path):
        logger.error(f"File not found: {safe_path}")
        raise HTTPException(status_code=404, detail="File not found")

    try:
        with open(safe_path, "r", encoding="utf-8") as f:
            data = json.load(f)
        return JSONResponse(data, status_code=200)
    except Exception as e:
        logger.error(f"Unexpected error: {e} for path: {path}")
        raise HTTPException(status_code=500, detail=f"An unexpected error occured: {e}")


@app.post("/cache/{path:path}")
async def save_cached_data(request: Request, path: str):  # noqa: F811
    """Saves data to a file in the output directory."""
    logger.info(f"==> POST /cache/{path}")
    user_id = get_user(request)
    logger.info(f"==> POST /cache/{path} for user {user_id}")
    if not os.path.exists(OUTPUT_DIR):
        os.makedirs(OUTPUT_DIR)

    safe_path = os.path.normpath(os.path.join(OUTPUT_DIR, f"{path}.json"))

    if not safe_path.startswith(OUTPUT_DIR):
        raise HTTPException(status_code=400, detail="Invalid path")

    try:
        data = await request.json()  # Get JSON data from the request
    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="Invalid JSON data")

    try:
        # create the directory if it doesn't exist
        os.makedirs(os.path.dirname(safe_path), exist_ok=True)
        # read the content of the files if it exists and merge it with the new data
        if os.path.exists(safe_path):
            with open(safe_path, "r", encoding="utf-8") as f:
                existing_data = json.load(f)
            if isinstance(existing_data, dict) and isinstance(data, dict):
                data = {**existing_data, **data}
            elif isinstance(existing_data, list) and isinstance(data, list):
                data = existing_data + data
        with open(safe_path, "w", encoding="utf-8") as f:
            json.dump(
                data, f, indent=4, ensure_ascii=False
            )  # Save JSON data to the file
        return JSONResponse({"message": "Data saved successfully"}, status_code=200)
    except Exception as e:  # Catch any other potential errors
        logger.error(f"Unexpected error: {e} for path: {path} for body {data}")
        raise HTTPException(status_code=500, detail=f"An unexpected error occured: {e}")


@app.patch("/cache/{path:path}")
async def update_cached_data(request: Request, path: str):  # noqa: F811
    """Saves data to a file in the output directory."""
    logger.info(f"==> PATCH /cache/{path}")
    user_id = get_user(request)
    logger.info(f"==> PATCH /cache/{path} for user {user_id}")
    if not os.path.exists(OUTPUT_DIR):
        os.makedirs(OUTPUT_DIR)

    safe_path = os.path.normpath(os.path.join(OUTPUT_DIR, f"{path}.json"))

    if not safe_path.startswith(OUTPUT_DIR):
        raise HTTPException(status_code=400, detail="Invalid path")

    try:
        data = await request.json()  # Get JSON data from the request
    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="Invalid JSON data")

    try:
        # create the directory if it doesn't exist
        os.makedirs(os.path.dirname(safe_path), exist_ok=True)
        # read the content of the files if it exists and merge it with the new data
        if os.path.exists(safe_path):
            with open(safe_path, "r", encoding="utf-8") as f:
                existing_data = json.load(f)
            if isinstance(existing_data, dict) and isinstance(data, dict):
                data = {**existing_data, **data}
            elif isinstance(existing_data, list) and isinstance(data, list):
                data = existing_data + data
        with open(safe_path, "w", encoding="utf-8") as f:
            json.dump(
                data, f, indent=4, ensure_ascii=False
            )  # Save JSON data to the file
        return JSONResponse({"message": "Data saved successfully"}, status_code=200)
    except Exception as e:  # Catch any other potential errors
        logger.error(f"Unexpected error: {e} for path: {path} for body {data}")
        raise HTTPException(status_code=500, detail=f"An unexpected error occured: {e}")


@app.post("/experiments/{experiment_id}/topics")
async def create_topic(
    experiment_id: str,
    request: Request,
    body: CreateTopicRequest,
    background_tasks: BackgroundTasks,
    authorization: str = Header(None),
    x_api_key: str = Header(None),
):
    auth_header = {}
    if authorization:
        auth_header = {"Authorization": authorization}
    elif x_api_key:
        auth_header = {"x-api-key": x_api_key}

    async with httpx.AsyncClient() as client:
        experiment_response = await client.get(
            f"{CONTROL_PLANE_URL}/api/experiments/{experiment_id}?exclude-calculated-status=true",
            headers=auth_header,
        )

    experiment = experiment_response.json()

    role = experiment.get("role", "")
    source_data = experiment.get("source_data", {})
    personas = source_data.get("personas", [])
    topics = [body.new_topic]
    evaluation_configuration = source_data.get("evaluation_configuration", {})
    risks = evaluation_configuration.keys()
    generation_configuration = source_data.get("generation_configuration", {}) or {}
    branching_factor = generation_configuration.get("branching_factor")
    max_conversation_length = generation_configuration.get("max_conversation_length", 4)

    background_tasks.add_task(
        persona_topic_generator.queue_prompt_group_generation,
        experiment_id=experiment_id,
        role=role,
        risks=risks,
        branching_factor=branching_factor,
        max_depth=max_conversation_length,
        min_depth=generation_configuration.get("min_conversation_length", 1),
        personas=personas,
        topics=topics,
        auth_header=auth_header,
        disable_probability=True,
        is_adapted_conversation=body.is_adapted_conversation,
    )


@app.post("/experiments/{experiment_id}/personas")
async def create_personas(
    experiment_id: str,
    request: Request,
    body: CreatePersonaRequest,
    background_tasks: BackgroundTasks,
    authorization: str = Header(None),
    x_api_key: str = Header(None),
):
    auth_header = {}
    if authorization:
        auth_header = {"Authorization": authorization}
    elif x_api_key:
        auth_header = {"x-api-key": x_api_key}

    async with httpx.AsyncClient() as client:
        experiment_response = await client.get(
            f"{CONTROL_PLANE_URL}/api/experiments/{experiment_id}?exclude-calculated-status=true",
            headers=auth_header,
        )

    experiment = experiment_response.json()

    role = experiment.get("role", "")
    source_data = experiment.get("source_data", {})
    topics = source_data.get("topics", [])
    personas = [body.new_persona]
    evaluation_configuration = source_data.get("evaluation_configuration", {})
    risks = evaluation_configuration.keys()
    generation_configuration = source_data.get("generation_configuration", {}) or {}
    branching_factor = generation_configuration.get("branching_factor")
    max_conversation_length = generation_configuration.get("max_conversation_length", 4)

    background_tasks.add_task(
        persona_topic_generator.queue_prompt_group_generation,
        experiment_id=experiment_id,
        role=role,
        risks=risks,
        branching_factor=branching_factor,
        max_depth=max_conversation_length,
        min_depth=generation_configuration.get("min_conversation_length", 1),
        personas=personas,
        topics=topics,
        auth_header=auth_header,
        disable_probability=True,
        is_adapted_conversation=body.is_adapted_conversation,
    )


@app.post("/experiments/{experiment_id}/topics-and-personas")
async def create_tests(
    experiment_id: str,
    request: Request,
    body: CreateTopicPersonaRequest,
    background_tasks: BackgroundTasks,
    authorization: str = Header(None),
    x_api_key: str = Header(None),
):
    print(f"==> POST /experiments/{experiment_id}/topics-and-personas", body)
    auth_header = {}
    if authorization:
        auth_header = {"Authorization": authorization}
    elif x_api_key:
        auth_header = {"x-api-key": x_api_key}

    async with httpx.AsyncClient() as client:
        experiment_response = await client.get(
            f"{CONTROL_PLANE_URL}/api/experiments/{experiment_id}?exclude-calculated-status=true",
            headers=auth_header,
        )

    experiment = experiment_response.json()

    personas = body.personas
    topics = body.topics
    role = experiment.get("role", "")
    source_data = experiment.get("source_data", {})
    evaluation_configuration = source_data.get("evaluation_configuration", {})
    risks = evaluation_configuration.keys()

    generation_configuration = source_data.get("generation_configuration", {}) or {}
    branching_factor = generation_configuration.get("branching_factor") or 1

    logger.info(
        f"====> sending messages to queue for {experiment_id} {len(personas)} personas and {len(topics)} topics and {role} role and {len(risks)} risks"
    )
    persona_topic_generator.queue_prompt_group_generation(
        experiment_id=experiment_id,
        role=role,
        risks=risks,
        branching_factor=branching_factor,
        personas=personas,
        topics=topics,
        auth_header=auth_header,
        disable_probability=True,
        min_depth=generation_configuration.get("min_conversation_length", 1),
        max_depth=generation_configuration.get("max_conversation_length", 7),
        max_conversations=generation_configuration.get("max_conversations", 1000),
        is_adapted_conversation=body.is_adapted_conversation,
    )


@app.post("/experiment")
async def create_experiment(
    request: Request,
    background_tasks: BackgroundTasks,
    experiment: CreateExperiment,
    authorization: str = Header(None),
    x_api_key: str = Header(None),
    dry_run: bool = Query(False, alias="dry-run"),
):
    user_id = get_user(request)

    auth_header = {}
    if authorization:
        auth_header = {"Authorization": authorization}
    elif x_api_key:
        auth_header = {"x-api-key": x_api_key}

    args = GenerateArgs(
        user_id=user_id,
        role=experiment.role,
        experiment_id=experiment.id,
        source_data=experiment.source_data,
        user_description=experiment.user_description or "",
        auth_header=auth_header,
        dry_run=dry_run,
    )

    background_tasks.add_task(persona_topic_generator.generate, args)
    return {"status": "ok"}


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(
        app,
        host="0.0.0.0",
        port=48001,
        timeout_keep_alive=300,  # Increase keep-alive timeout to 120 seconds
        timeout_graceful_shutdown=300,  # Increase graceful shutdown timeout to 60
    )

# 1. /ws register -> put connection in dict
# 2. We generate a prompt, put in queue (in case client is disconnected). We consisntely check if the queue is not empty and try to find clients to send the prompt to
# 3. Once we finish generating we close the connection by user id
