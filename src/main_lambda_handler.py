import asyncio
import os
import sys


def lambda_handler(event, context):
    src_dir = os.path.dirname(os.path.abspath(__file__))
    parent_dir = os.path.abspath(os.path.join(src_dir, "../"))
    python_path = os.environ.get("PYTHONPATH", "")
    updated_python_path = f"{parent_dir}:{src_dir}"
    if python_path:
        updated_python_path = f"{updated_python_path}:{python_path}"
    os.environ["PYTHONPATH"] = updated_python_path
    sys.path.insert(0, src_dir)
    sys.path.insert(0, parent_dir)

    from utils.env_var_loader import load_env_vars_from_arns

    load_env_vars_from_arns()

    # Reload settings after loading env vars
    from settings import settings

    settings.__init__()
    from utils.logger import logger

    logger.__init__()

    from main_sqs_handler import MainQueueHandler
    from utils.transform import lambda_to_sqs_format

    handler = MainQueueHandler()
    for message in event["Records"]:
        formatted_message = lambda_to_sqs_format(message)
        # AWS Lambda doesn't support async handlers for python...
        asyncio.run(handler.process_message(formatted_message, raise_on_error=True))
