You are an expert in user experience design and market research. Your task is to create realistic, diverse, and detailed personas for potential users of a specific application. These personas will help in understanding the target audience and improving the product design.

First, carefully read the following application description:

<application_description>
{{APPLICATION_DESCRIPTION}}
</application_description>

You will generate <number_of_personas>{{NUMBER_OF_PERSONAS}}</number_of_personas> unique and detailed personas based on this application description. Each persona should be a vivid representation of a potential user, without using specific names or exact ages.

For each persona, wrap the following steps in <persona_brainstorming> tags:

1. Demographics:
   a. List 3-4 options for age range (e.g., "early 20s", "mid-30s", "late 40s")
   b. List 3-4 gender options, including non-binary identities
   c. List 3-4 location options (city/country)
   d. List 3-4 occupation options relevant to the application
   e. Describe 2-3 different life situations (e.g., "recent graduate", "new parent", "career changer")

2. Technical proficiency:
   a. Consider beginner, intermediate, and advanced levels
   b. For each level, describe how it might manifest in relation to the application (e.g., "struggles with new apps", "comfortable with basic features", "power user of similar tools")

3. Motivations:
   a. List 3-4 potential reasons for using the application
   b. For each motivation, note how it relates to the application's features

4. Pain points:
   a. Identify 2-3 possible challenges the application might solve
   b. For each pain point, describe how the application could address it

5. Usage patterns:
   a. Suggest 2-3 different ways the persona might use the app (e.g., "daily quick check", "weekly deep dive", "occasional but intensive use")
   b. For each pattern, note potential features they might prioritize

6. Alignment check:
   a. Review the application description
   b. Note how each element of the persona aligns with the application's features and target audience

7. Persona comparison:
   a. Briefly compare this persona to any previously created ones
   b. Ensure diversity in age, gender, technical proficiency, and circumstances
   c. If too similar to an existing persona, adjust elements to increase diversity

After brainstorming, select the most compelling and diverse options to create a cohesive persona. Check for any potential contradictions or inconsistencies between the chosen elements and resolve them, explaining your reasoning.

Create a concise 1-2 sentence description that captures the essence of the person, their situation, and their relation to the application. Use generic descriptors instead of specific names or exact ages. Ensure this description is brief and focused.

Add the final persona description to the list of personas.

Repeat this process for each persona, ensuring that the set represents a diverse range of potential users, varying in age ranges, gender, technical proficiency, and personal circumstances.

After generating all personas, provide a brief summary (2-3 sentences) of how these personas represent a diverse set of potential users for the application and how they collectively showcase different use cases and needs.

Present your final output in this format:

<personas>
Persona 1 description
Persona 2 description
...
Persona N description
</personas>

[Summary of personas generated]

Remember:
- Each persona description should be brief, typically one sentence and rarely two sentences.
- Ensure each persona is on a new line within the <personas> tags.
- Make each persona vivid, realistic, and specific to the application, while keeping the final descriptions concise and focused on the key characteristics and needs of each potential user.
- For each persona, do not prefix the description with numbers or bullet points. Keep the format clean and simple.
