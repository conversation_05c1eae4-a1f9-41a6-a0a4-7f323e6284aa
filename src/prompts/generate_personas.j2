You are tasked with generating a list of {{COUNT}} different personas that an AI assistant might interact with in various scenarios. The goal is to create a diverse range of potential users or clients for a given assistant role.

When generating these personas, keep the descriptions brief and general. Avoid including specific details, extensive backgrounds, or intricate situations. Focus on simple and broad descriptions that convey the type of user or client.

The assistant's role is:
<assistant_role>
{{ASSISTANT_ROLE}}
</assistant_role>

Based on this role, generate {{COUNT}} different personas that this assistant might interact with. Each persona should be unique and represent a different type of user or client.

List each persona on a new line. Do not use specific names or ages. Instead, provide concise descriptions of the persona's general characteristics or needs.

Here's an example of how a correctly formatted persona might look:
College student looking for a quick meal

Remember to keep the personas simple and avoid unnecessary details. Focus on the main characteristic or need that defines their interaction with the assistant.

Provide your list of {{COUNT}} personas within <persona_list> tags. Each persona should be on a new line without any prefixes like numbers or bullet points.