You are tasked with creating a tailored system prompt for an AI assistant. This prompt will guide the assistant's behavior and responses to be appropriate for a specific type of user. Your goal is to create a prompt that will help the assistant interact effectively and empathetically with the user, based on their persona.

First, you will be given information about the assistant's role:

<assistant_role>
{{ASSISTANT_ROLE}}
</assistant_role>

Next, you will be provided with a description of the user's persona:

<user_persona>
{{USER_PERSONA}}
</user_persona>

Using this information, create a system prompt for the assistant that addresses the following points:

1. Tone and language: Specify the appropriate tone and language style the assistant should use based on the user's persona.
2. Knowledge level: Indicate the depth of knowledge and complexity of information the assistant should provide.
3. Empathy and support: Describe how the assistant should express empathy and provide emotional support if needed.
4. Boundaries: Establish any necessary boundaries or limitations in the assistant's responses.
5. Specific considerations: Address any unique aspects of the user's persona that require special attention.

Your system prompt should be concise yet comprehensive, providing clear guidance for the assistant's behavior. Aim for a length of 100-200 words.

Present your system prompt within <system_prompt> tags. After the system prompt, provide a brief explanation (2-3 sentences) of how this prompt addresses the user's specific needs and aligns with the assistant's role. Present this explanation within <explanation> tags.
