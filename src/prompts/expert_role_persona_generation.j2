You are tasked with generating diverse synthetic data representing user personas who might seek advice or help from a specific expert. This data will be used to simulate or prepare for various user interactions with the expert.

Here are the key variables for this task:

1. Expert role:
<expert_role>
{{EXPERT_ROLE}}
</expert_role>

2. Number of personas to generate:
<number_of_personas>
{{NUMBER_OF_PERSONAS}}
</number_of_personas>

Your task is to generate {{NUMBER_OF_PERSONAS}} unique personas who would seek advice or help from the {{EXPERT_ROLE}}. Each persona should have a specific situation or challenge that would lead them to consult with this expert.

Before creating the final list of personas, use the <persona_planning> tags to brainstorm and ensure diversity. Consider the following aspects for each persona:
- Demographics (age, gender, cultural background, etc.)
- Level of familiarity with the expert's work
- Severity of the matter on which they're seeking advice
- Mental state and level of frustration
- Socioeconomic background
- Geographic location
- Educational level
- Professional background
- Family situation

In your persona planning process:
1. List out diverse characteristics for each aspect mentioned above.
2. Create a brief outline for each persona, ensuring diversity across the aspects.
3. Review the outlines and make adjustments if needed to ensure maximum diversity.
4. It's okay for this section to be quite long, as we want thorough planning.

This will help ensure a well-rounded and representative set of synthetic data.

After your brainstorming, create the final list of personas. Each persona description should:
- Be written in the third person
- Explicitly mention their specific situation or challenge
- Be a natural, realistic scenario
- Not include a name
- Be concise but informative

Format your final output as a JSON object with a single "personas" property containing an array of persona descriptions. Adhere strictly to this format and do not include additional properties.

Remember:
- Do NOT use escape characters around the JSON block in the response.
- Do NOT surround the JSON block with quotes or backticks.
- Only return the exact number of personas specified in <number_of_personas>.
- Make sure that you surround the JSON block with <json> and </json> tags.

Here's an example of the expected output format (using generic content):

<json>
{
    "personas": [
        "A middle-aged professional seeking advice on career transition",
        "A young student struggling with exam anxiety",
        "An elderly individual concerned about retirement planning"
    ]
}
</json>
Now, proceed with your persona planning process and then generate the requested personas.