import asyncio
import json
import os
import boto3
from typing import Optional
from schemas.auth import AuthInfo
from schemas.generation import GenerateArgs
from utils.auth import verify_api_key, verify_user_token
from persona_topic_generator import persona_topic_generator
from utils.logger import logger


def try_to_parse(obj: Optional[str] = None):
    try:
        return json.loads(obj)  # type: ignore
    except json.JSONDecodeError:
        return None


class MainQueueHandler:
    def __init__(self):
        options = {}
        sqs_region = os.environ.get(
            "SQS_REGION", os.environ.get("AWS_REGION", "us-east-1")
        )
        if sqs_region == "elasticmq":
            sqs_endpoint = os.environ.get("SQS_ENDPOINT", None)

            options["endpoint_url"] = sqs_endpoint
            options["region_name"] = sqs_region
            options["aws_secret_access_key"] = "x"
            options["aws_access_key_id"] = "x"
            options["use_ssl"] = False

        session = boto3.Session()
        self.sqs = session.client("sqs", **options)
        self.queue_url = os.environ.get("GEN_MAIN_QUEUE_URL")
        if not self.queue_url:
            raise ValueError("GEN_MAIN_QUEUE_URL is not set!")

        self.dlq_url = os.environ.get("GEN_MAIN_DL_QUEUE_URL")
        if not self.queue_url:
            raise ValueError("GEN_MAIN_DL_QUEUE_URL is not set!")

    def delete_message(self, message):
        logger.info(f"Deleting message with id {message['MessageId']}")
        self.sqs.delete_message(
            QueueUrl=self.queue_url, ReceiptHandle=message["ReceiptHandle"]
        )

    def send_to_dead_letter(self, message, reason, detail):
        self.sqs.send_message(
            QueueUrl=self.dlq_url,
            MessageBody=message["Body"],
            MessageAttributes={
                **message["MessageAttributes"],
                "Reason": {"DataType": "String", "StringValue": reason},
                "Detail": {"DataType": "String", "StringValue": detail},
            },
        )
        self.delete_message(message)

    def _valid_auth(self, message) -> AuthInfo | None:
        if "MessageAttributes" not in message:
            logger.error(
                "Message does not have attributes! Sending to dead letter queue..."
            )
            return self.send_to_dead_letter(
                message, reason="Malformed", detail="No MessageAttribues"
            )

        message_attributes = message["MessageAttributes"]
        if "Auth" not in message_attributes:
            logger.error(
                "Message does not have an auth attribute! Sending to dead letter queue..."
            )
            return self.send_to_dead_letter(
                message, reason="Malformed", detail="No Auth in MessageAttributes"
            )

        auth = json.loads(message_attributes["Auth"]["StringValue"])
        verified_token = {}
        auth_header = {}
        user_token = auth.get("Authorization")
        api_key = auth.get("x-api-key")
        if user_token:
            verified_token = verify_user_token(user_token)
            auth_header = {"Authorization": user_token}
        elif api_key:
            verified_token = verify_api_key(api_key)
            auth_header = {"x-api-key": api_key}

        if not verified_token:
            logger.error(
                "Message does not have a valid token! Sending to dead letter queue..."
            )
            return self.send_to_dead_letter(
                message, reason="Unauthorized", detail="Invalid token"
            )

        user_id = verified_token.get("sub", "")

        return AuthInfo(user_id, auth_header)

    def _validate_message_body_structure(self, message, auth_info: AuthInfo):
        message_body = json.loads(message["Body"])

        missing_fields = []
        for attribute in ["role", "id", "source_data", "user_description"]:
            if attribute not in message_body:
                missing_fields.append(attribute)
        if missing_fields:
            logger.error(
                f"Message is missing required fields: {missing_fields}! Sending to dead letter queue..."
            )
            return self.send_to_dead_letter(
                message,
                reason="Malformed",
                detail=f"Missing required fields in message body: {missing_fields}",
            )

        role = message_body["role"]
        experiment_id = message_body["id"]
        source_data = message_body["source_data"]
        user_description = message_body["user_description"]

        return GenerateArgs(
            auth_info.user_id,
            role,
            experiment_id,
            source_data,
            user_description,
            auth_info.auth_header,
        )

    def validate_message(self, message):
        auth_info = self._valid_auth(message)
        if not auth_info:
            return

        if "Body" not in message:
            logger.error("Message does not have a body! Deleting message...")
            return self.delete_message(message)

        return self._validate_message_body_structure(message, auth_info)

    async def process_message(self, message, raise_on_error=True):
        try:
            logger.dev(
                f"Main SQS handler Processing message: {message.get('MessageId', 'no MessageId')}"
            )
            logger.dev(f"Full Message: {message}")
            payload = self.validate_message(message)
            if not payload:
                return

            logger.info(f"Processing experiment: {payload.experiment_id}")
            await persona_topic_generator.generate(payload)

            self.delete_message(message)
            return message
        except Exception as e:
            experiment_id = "unknown"
            message_body = try_to_parse(message.get("Body"))
            if message_body:
                experiment_id = message_body.get("id", "unknown")
            logger.error(
                f"Error processing message: {message.get('MessageId', 'no MessageId')} for experiment: {experiment_id}"
            )
            logger.debug(f"Full Message: {message}")
            logger.error(e)
            import traceback

            traceback.print_exception(e)
            if raise_on_error:
                raise e

    async def poll(self):
        while True:
            logger.dev("Waiting for messages...")
            response = self.sqs.receive_message(
                QueueUrl=self.queue_url,
                MaxNumberOfMessages=1,
                WaitTimeSeconds=10,
                MessageAttributeNames=["All"],
            )
            if "Messages" not in response:
                logger.dev("No messages in queue")
                continue
            for message in response["Messages"]:
                logger.dev("Processing for messages...")
                await self.process_message(message)


if __name__ == "__main__":
    handler = MainQueueHandler()
    asyncio.run(handler.poll())
