import networkx as nx
import pydot

from utils.logger import logger
from .utils import chat_completion

__all__ = ["TopicTree"]


class TopicTree:
    def __init__(self, dot_string: str):
        # Parse the graph using pydot
        pydot_graph = pydot.graph_from_dot_data(dot_string)[0]  # type: ignore
        graph = nx.DiGraph(nx.drawing.nx_pydot.from_pydot(pydot_graph))

        # Identify root node (node with no incoming edges)
        root_node = None
        for node in graph.nodes():
            if graph.in_degree(node) == 0:
                root_node = node
                break
        self.digraph = graph
        self.root_node = root_node

    @classmethod
    def from_documents(cls, documents: list[str], metadata={}) -> "TopicTree":
        """
        Create a TopicTree from a list of documents by concatenating them and using an LLM
        to generate a topic tree structure.

        Args:
            documents: List of document texts to analyze

        Returns:
            A TopicTree instance generated from the documents
        """
        # Concatenate documents
        combined_text = "\n\n".join(documents)

        # Create prompt for the LLM
        prompt = (
            "Based on the following documents, create a hierarchical topic tree. "
            "Format your response as a DOT graph with parent-child relationships. "
            "Each node should represent a topic or subtopic found in the documents.\n\n"
            f"Documents:\n{combined_text}\n\n"
            "Do not use backticks/codeblocks. Just write the DOT graph directly"
            "Return only the DOT graph with format: 'parent' -> 'child';"
        )

        # Get completion from LLM
        dot_string = chat_completion(
            model="gemini/gemini-2.0-flash",
            messages=[{"role": "user", "content": prompt}],
            metadata=metadata,
        ).strip()

        # Clean up the DOT string by removing backticks if present
        if dot_string.startswith("```") and dot_string.endswith("```"):
            # Remove the first and last lines if they contain backticks
            dot_string_lines = dot_string.split("\n")
            dot_string = "\n".join(dot_string_lines[1:-1])
        elif dot_string.startswith("`") and dot_string.endswith("`"):
            # Remove single backticks at the beginning and end
            dot_string = dot_string[1:-1]

        # Ensure the DOT string is properly formatted
        if not dot_string.strip():
            logger.warning("Empty DOT string received from LLM")
            dot_string = "digraph G { 'root' }"

        logger.info(f"Processed DOT string: {dot_string}")

        # Create TopicTree from DOT string
        return cls(dot_string)

    def get_branches_with_levels(self) -> list[tuple[str, str, int]]:
        branches = []
        node_levels = {self.root_node: 0}

        # Count number of nodes in the graph
        if len(self.digraph.nodes()) == 0:  # type: ignore
            logger.dev("No nodes in the graph")
            return []

        for parent, child in nx.bfs_edges(self.digraph, source=self.root_node):
            node_levels[child] = node_levels[parent] + 1
            branches.append((parent, child, node_levels[child]))

        return branches

    def get_unique_topics(self) -> list[str]:
        # # Doesn't maintain order
        # unique_topics = list(set(self.digraph.nodes()))
        unique_topics = []
        for node in self.digraph.nodes():
            if node not in unique_topics and node != self.root_node:
                unique_topics.append(node)
        return unique_topics

    def to_dot_string(self) -> str:
        """Convert the topic tree to DOT format string, returning only the edge definitions"""
        pydot_graph = nx.nx_pydot.to_pydot(self.digraph)
        full_dot = pydot_graph.to_string()

        # Extract just the edge definitions (lines containing "->")
        lines = full_dot.split("\n")
        edge_lines = [line.strip() for line in lines if "->" in line]
        return "\n".join(edge_lines)

    def save(self, filename: str) -> None:
        """
        Save the topic tree to a file in DOT format.

        Args:
            filename: Path to save the DOT representation of the tree
        """
        dot_string = self.to_dot_string()
        with open(filename, "w") as f:
            f.write(dot_string)
        logger.dev(f"Topic tree saved to {filename}")

    def save_as_png(self, filename: str) -> None:
        """
        Render the topic tree as a PNG image and save it.

        Args:
            filename: Path to save the PNG image
        """
        pydot_graph = nx.nx_pydot.to_pydot(self.digraph)
        pydot_graph.write_png(filename)
        logger.dev(f"Topic tree rendered and saved as PNG: {filename}")

    @classmethod
    def load(cls, filename: str, max_topics: int) -> "TopicTree":
        """
        Load a topic tree from a DOT format file.

        Args:
            filename: Path to the DOT file
            max_topics: Maximum number of topics to include

        Returns:
            A new TopicTree instance
        """
        with open(filename, "r") as f:
            dot_string = f.read()
        logger.dev(f"Topic tree loaded from {filename}")
        return cls(dot_string)

    def add_topics(self, topics: list[str] | str) -> None:
        """
        Add one or multiple topics to the topic tree, connecting each to the root node.

        Args:
            topics: Either a single topic string or a list of topic strings to add to the tree
        """
        if isinstance(topics, str):
            # Handle single topic case
            topic = topics
            # Add topic node if it doesn't exist
            if not self.digraph.has_node(topic):
                self.digraph.add_node(topic)

            # Add edge from root to topic
            self.digraph.add_edge(self.root_node, topic)
            logger.dev(f"Added topic '{topic}' connected to root")
        else:
            # Handle list of topics
            for topic in topics:
                # Add topic node if it doesn't exist
                if not self.digraph.has_node(topic):
                    self.digraph.add_node(topic)

                # Add edge from root to topic
                self.digraph.add_edge(self.root_node, topic)

            logger.dev(f"Added {len(topics)} topics to the topic tree")
