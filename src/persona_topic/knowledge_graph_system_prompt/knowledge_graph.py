from typing import List, Optional, Dict, Any
import logging
import json
import os

from langchain_experimental.graph_transformers import LLMGraphTransformer
from langchain_openai import ChatOpenAI
from langchain_core.documents import Document
from langchain_community.graphs.graph_document import Node
from .entity_resolution import merge_entities, update_relationships
from .models import Enti<PERSON>, Relationship, Triplet, KnowledgeGraph
from ..constants import GPT_4_1_MINI
from ..utils import format_prompt_from_template

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def graph_to_triplets(nodes: List[Node], relationships: List[Relationship]) -> List[Triplet]:
    """
    Convert graph structure to a list of Triplet objects.
    """
    triplets = []
    
    # Convert relationships to triplets
    for rel in relationships:
        triplet = Triplet(
            subject=rel.source_id,
            predicate=rel.type,
            object=rel.target_id
        )
        triplets.append(triplet)
    
    # Also include node properties as triplets
    for node in nodes:
        if node.properties:
            for prop_key, prop_value in node.properties.items():
                triplet = Triplet(
                    subject=node.id,
                    predicate=f"has_{prop_key}",
                    object=str(prop_value)
                )
                triplets.append(triplet)
    
    return triplets



async def process_documents(documents: List[Document], role: str, metadata: Optional[Dict[str, Any]] = None):
    """
    Process the documents to extract knowledge graph and relationships.
    Returns merged nodes and relationships.
    """
    llm = ChatOpenAI(temperature=0, model_name=GPT_4_1_MINI)

    # Format the transformation instructions using the template
    transformation_instructions = format_prompt_from_template(
        "knowledge_graph/graph_transformation.jinja2",
        role=role 
    )

    llm_transformer = LLMGraphTransformer(llm=llm, additional_instructions=transformation_instructions)
    transformation_instructions = format_prompt_from_template(
        "knowledge_graph/graph_transformation.jinja2",
        role=role
    )
    
    # Update transformer with role-specific instructions
    llm_transformer.additional_instructions = transformation_instructions
    
    # Process the documents
    graph_documents = await llm_transformer.aconvert_to_graph_documents(documents)
    
    # Print document details
    all_nodes = []
    all_relationships = []
    for i, doc in enumerate(graph_documents):
        logger.info(f"Document {i}:")
        logger.info(f"  Source: {documents[i].metadata.get('source', 'Unknown')}")
        logger.info(f"  Nodes: {len(doc.nodes)}")
        logger.info(f"  Relationships: {len(doc.relationships)}")
        all_nodes.extend(doc.nodes)
        all_relationships.extend(doc.relationships)

    logger.info(f"Total nodes before resolution: {len(all_nodes)}")
    logger.info(f"Total relationships before resolution: {len(all_relationships)}")
    
    # Perform entity resolution
    merged_nodes, resolution_result = await merge_entities(all_nodes, metadata=metadata)
    
    # Update relationships with merged node IDs
    updated_relationships = update_relationships(all_relationships, resolution_result.mapping)
    
    # Convert nodes to entities for better tracking
    entities = [
        Entity(
            id=node.id,
            type=node.properties.get("type"),
            properties=node.properties
        )
        for node in merged_nodes
    ]
    
    return merged_nodes, updated_relationships

async def generate_knowledge_graph(document_contents: List[str], role: str, output_path: Optional[str] = None, metadata: Optional[Dict[str, Any]] = None) -> KnowledgeGraph:
    """
    Process documents and generate a knowledge graph.
    Returns a KnowledgeGraph object and optionally saves to file.
    """
    # Process documents
    documents = [Document(page_content=content) for content in document_contents]
    merged_nodes, updated_relationships = await process_documents(documents, role, metadata=metadata)
    
    # Convert nodes to our Entity dataclass
    entities = [
        Entity(
            id=node.id,
            type=node.properties.get("type"),
            properties=node.properties
        )
        for node in merged_nodes
    ]
    
    # Create triplets
    triplets = graph_to_triplets(merged_nodes, updated_relationships)
    
    # Create KnowledgeGraph object
    knowledge_graph = KnowledgeGraph(
        entities=entities,
        relationships=updated_relationships,
        triplets=triplets,
        metadata={
            "role": role,
            "document_count": len(documents),
            **({} if metadata is None else metadata)
        }
    )
    
    # Save graph if output path provided
    if output_path:
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(knowledge_graph.to_json())
        
        logger.info(f"Graph saved to {output_path}")
        logger.info(f"  - Entities: {knowledge_graph.entity_count}")
        logger.info(f"  - Relationships: {knowledge_graph.relationship_count}")
        logger.info(f"  - Total triplets: {knowledge_graph.triplet_count}")
        
        # Also save just the triplets in a simple format
        triplets_path = output_path.replace('.json', '_triplets.txt')
        with open(triplets_path, 'w', encoding='utf-8') as f:
            for triplet in triplets:
                f.write(f"{triplet}\n")
        logger.info(f"Triplets also saved to {triplets_path}")
    
    return knowledge_graph


def load_knowledge_graph_from_file(file_path: str) -> KnowledgeGraph:
    """
    Load a knowledge graph from a JSON file.
    
    Args:
        file_path: Path to the JSON file containing the knowledge graph.
        
    Returns:
        KnowledgeGraph: The loaded knowledge graph object.
        
    Raises:
        FileNotFoundError: If the file does not exist.
        json.JSONDecodeError: If the file contains invalid JSON.
        KeyError: If the JSON structure is invalid for a knowledge graph.
    """
    logger.info(f"Loading knowledge graph from {file_path}")
    
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"Knowledge graph file not found: {file_path}")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        knowledge_graph = KnowledgeGraph.from_dict(data)
        
        logger.info(f"Successfully loaded knowledge graph from {file_path}")
        logger.info(f"  - Entities: {knowledge_graph.entity_count}")
        logger.info(f"  - Relationships: {knowledge_graph.relationship_count}")
        logger.info(f"  - Total triplets: {knowledge_graph.triplet_count}")
        
        return knowledge_graph
    
    except json.JSONDecodeError as e:
        logger.error(f"Failed to parse JSON from {file_path}: {str(e)}")
        raise
    
    except KeyError as e:
        logger.error(f"Invalid knowledge graph structure in {file_path}: {str(e)}")
        raise