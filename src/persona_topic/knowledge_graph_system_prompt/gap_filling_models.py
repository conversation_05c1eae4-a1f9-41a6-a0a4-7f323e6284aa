from dataclasses import dataclass, field
from typing import Dict, List, Set, Optional, Any
from datetime import datetime

from .models import <PERSON><PERSON><PERSON>, Relationship, Triplet, SubGraph


@dataclass
class Island:
    """Represents a disconnected component (island) in the knowledge graph."""
    nodes: Set[str]  # Node IDs in this island
    core_nodes: Set[str] = field(default_factory=set)  # Core node IDs
    context_nodes: Set[str] = field(default_factory=set)  # Context node IDs
    triplets: List[Triplet] = field(default_factory=list)  # Triplets within this island
    coverage: float = 0.0  # Current topic coverage percentage
    index: int = 0  # Island index for tracking

    @property
    def size(self) -> int:
        """Number of nodes in the island."""
        return len(self.nodes)
    
    @property
    def core_node_count(self) -> int:
        """Number of core nodes in the island."""
        return len(self.core_nodes)
    
    @property
    def context_node_count(self) -> int:
        """Number of context nodes in the island."""
        return len(self.context_nodes)


@dataclass
class TopicAllocation:
    """Represents topic allocation decisions for islands."""
    island_index: int
    island_size: int
    allocated_topics: int
    coverage: float
    weight: float = 0.0  # The calculated weight used for allocation


@dataclass
class IslandProcessingParams:
    """Parameters for processing a single island."""
    island_idx: int
    island: Island
    num_topics: int
    node_topic_map: Dict[str, List[str]]
    experiment_intent: str
    product_description: Optional[str] = None


@dataclass
class IslandResult:
    """Result of processing a single island."""
    island_index: int
    core_nodes: List[str]
    all_nodes: List[str]
    new_topics: List[str]
    node_count: int
    core_node_count: int
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert island result to dictionary representation."""
        return {
            "island_index": self.island_index,
            "core_nodes": self.core_nodes,
            "all_nodes": self.all_nodes,
            "new_topics": self.new_topics,
            "node_count": self.node_count,
            "core_node_count": self.core_node_count
        }


@dataclass
class GapFillingResult:
    """Result of the gap filling process."""
    new_topics: List[str]
    islands: List[IslandResult]
    total_new_topics: int
    total_islands: int
    total_core_nodes: int
    creation_time: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert gap filling result to dictionary representation."""
        return {
            "new_topics": self.new_topics,
            "islands": [island.to_dict() for island in self.islands],
            "count": self.total_new_topics,
            "total_islands": self.total_islands,
            "total_core_nodes": self.total_core_nodes,
            "timestamp": self.creation_time.strftime("%Y%m%d_%H%M%S")
        }