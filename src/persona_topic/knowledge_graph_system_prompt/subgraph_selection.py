"""
Intent-driven subgraph selection for experiment intents.

This module implements a classification-based approach to selecting a relevant subgraph
from a knowledge graph based on an experiment intent. It uses:

1. Intent pattern identification to understand the type of experiment
2. Multi-stage node classification based on intent relevance
3. Relationship-aware refinement to improve classification
4. Final consolidation into core, context, and removed nodes

The subgraph selection process:
- First analyzes the intent to determine its pattern and key concepts
- Classifies nodes based on direct relevance to the intent
- Refines classification by considering relationship connections
- Creates a focused subgraph with properly categorized nodes

Output includes detailed reports and visualizations to explain the selection.
"""

import json
import os
from typing import Dict, List, Set, Tuple, Any
from collections import defaultdict
from datetime import datetime

from .models import (
    Entity, 
    Relationship, 
    Triplet, 
    KnowledgeGraph, 
    SubGraph, 
    IntentAnalysis, 
    ExtractionCriteria
)
from ..utils import format_prompt_from_template, achat_completion, batch_gather, extract_json_from_text
from ..constants import GPT_4_1_MINI, O3
from utils.logger import logger


async def analyze_intent_pattern(experiment_intent: str, product_description: str = None, metadata: Dict[str, Any] = None) -> IntentAnalysis:
    """
    Analyzes an experiment intent to determine its pattern, key concepts, and classification criteria.
    
    Args:
        experiment_intent: The experiment intent to analyze
        product_description: Optional description of the product/domain to provide context
        metadata: Optional metadata for telemetry
        
    Returns:
        IntentAnalysis object containing:
        - pattern: The identified pattern (process, emotional, hypothetical, etc.)
        - key_concepts: List of important concepts from the intent
        - classification_criteria: Criteria for node classification
        - important_relationships: Relationship types that matter for this intent
    """
    logger.info(f"Analyzing intent pattern: '{experiment_intent}'")
    
    # Format prompt using template
    prompt = format_prompt_from_template(
        "knowledge_graph/analyze_intent_pattern.jinja2",
        experiment_intent=experiment_intent,
        product_description=product_description
    )
    
    try:
        # Use our own utility function for completion
        response = await achat_completion(
            model=O3, 
            messages=[{"role": "user", "content": prompt}],
            metadata=metadata or {}
        )
        
        # Parse JSON from response
        result = extract_json_from_text(response)
        
        pattern = result.get('pattern', 'unknown')
        key_concepts = result.get('key_concepts', [])
        
        logger.info(f"Intent pattern identified: {pattern}")
        logger.info(f"Key concepts: {', '.join(key_concepts)}")
        
        # Create IntentAnalysis object
        intent_analysis = IntentAnalysis(
            pattern=pattern,
            key_concepts=key_concepts,
            classification_criteria=result.get('classification_criteria', {
                "core": "Directly mentioned in the intent",
                "context": "Related to core concepts",
                "irrelevant": "Unrelated to the intent"
            }),
            important_relationships=result.get('important_relationships', []),
            intent_specificity=result.get('intent_specificity', 'GENERAL')
        )
        
        return intent_analysis
        
    except Exception as e:
        logger.error(f"Error analyzing intent: {e}")
        # Return default analysis if error occurs
        return IntentAnalysis(
            pattern="unknown",
            key_concepts=[],
            classification_criteria={
                "core": "Directly mentioned in the intent",
                "context": "Related to core concepts",
                "irrelevant": "Unrelated to the intent"
            },
            important_relationships=[],
            intent_specificity="GENERAL"
        )


async def classify_nodes_batch(batch: List[Entity], intent_analysis: IntentAnalysis, metadata: Dict[str, Any] = None) -> Dict[str, List[str]]:
    """
    Classify a batch of nodes based on intent analysis
    
    Args:
        batch: List of Entity objects to classify
        intent_analysis: IntentAnalysis object with intent information
        metadata: Optional metadata for telemetry
        
    Returns:
        Dictionary with classification results mapping categories to node IDs
    """
    batch_nodes = [entity.id for entity in batch]
    
    # Format prompt using template
    prompt = format_prompt_from_template(
        "knowledge_graph/classify_nodes_batch.jinja2",
        intent_specificity=intent_analysis.intent_specificity,
        pattern=intent_analysis.pattern,
        key_concepts=intent_analysis.key_concepts,
        classification_criteria=intent_analysis.classification_criteria,
        batch_nodes=batch_nodes
    )
    
    try:
        # Use our own utility function for completion
        response = await achat_completion(
            model=GPT_4_1_MINI,  # Using the same model as before (gpt-4.1-mini)
            messages=[{"role": "user", "content": prompt}],
            metadata=metadata or {}
        )
        
        # Parse JSON from response
        result = extract_json_from_text(response)
        
        # Validate that all nodes were classified
        all_classified = set()
        for category in ['core', 'context', 'irrelevant']:
            category_nodes = set(result.get(category, []))
            all_classified.update(category_nodes)
        
        missing_nodes = set(batch_nodes) - all_classified
        if missing_nodes:
            logger.warning(f"Some nodes were not classified: {missing_nodes}")
            # Assign missing nodes to irrelevant by default
            if 'irrelevant' not in result:
                result['irrelevant'] = []
            result['irrelevant'].extend(list(missing_nodes))
        
        # Update entity objects with their classifications
        for entity in batch:
            if entity.id in result.get('core', []):
                entity.relevance = "core"
                entity.relevance_score = 1.0
            elif entity.id in result.get('context', []):
                entity.relevance = "context"
                entity.relevance_score = 0.5
            else:
                entity.relevance = "irrelevant"
                entity.relevance_score = 0.0
            
        return result
        
    except Exception as e:
        logger.error(f"Error classifying batch: {e}")
        # Return empty classifications on error
        return {
            "core": [],
            "context": [],
            "irrelevant": batch_nodes
        }


async def classify_nodes(entities: List[Entity], intent_analysis: IntentAnalysis, 
                     batch_size: int = 20, metadata: Dict[str, Any] = None) -> Dict[str, Set[str]]:
    """
    Classify entities based on their relevance to the experiment intent.
    Uses batch_gather to process batches concurrently.
    
    Args:
        entities: List of Entity objects to classify
        intent_analysis: IntentAnalysis object with intent information
        batch_size: Number of entities per batch
        metadata: Optional metadata for telemetry
        
    Returns:
        Dictionary with sets of entity IDs for each category:
        - core: Set of core entity IDs
        - context: Set of context entity IDs
        - irrelevant: Set of irrelevant entity IDs
    """
    logger.info(f"Classifying {len(entities)} entities based on intent analysis")
    
    # Initialize classification sets
    classifications = {
        "core": set(),
        "context": set(),
        "irrelevant": set()
    }
    
    # Prepare batches
    batches = []
    for i in range(0, len(entities), batch_size):
        batches.append(entities[i:i + batch_size])
    
    logger.info(f"Processing {len(batches)} batches with batch_gather")
    
    # Create tasks for each batch
    tasks = []
    for batch in batches:
        tasks.append(classify_nodes_batch(batch, intent_analysis, metadata))
    
    # Process batches in parallel using batch_gather
    batch_results = await batch_gather(tasks=tasks, batch_size=5)
    
    # Process results
    for i, result in enumerate(batch_results):
        for category in ['core', 'context', 'irrelevant']:
            for entity_id in result.get(category, []):
                classifications[category].add(entity_id)
        logger.info(f"Completed batch {i+1}/{len(batches)}")
    
    logger.info(f"Initial classification complete:")
    logger.info(f"  - Core entities: {len(classifications['core'])}")
    logger.info(f"  - Context entities: {len(classifications['context'])}")
    logger.info(f"  - Irrelevant entities: {len(classifications['irrelevant'])}")
    
    # Entity objects have already been updated in classify_nodes_batch
    
    return classifications


def get_node_connections(node_id: str, triplets: List[Triplet]) -> Dict[str, List[Tuple[str, str]]]:
    """
    Get all nodes connected to a given node, grouped by relationship type
    
    Args:
        node_id: The ID of the entity to get connections for
        triplets: List of Triplet objects representing relationships
        
    Returns:
        Dictionary mapping relationship types to lists of (connected_node_id, direction) tuples
    """
    connections = defaultdict(list)
    
    for triplet in triplets:
        if triplet.subject == node_id:
            connections[triplet.predicate].append((triplet.object, 'outgoing'))
        elif triplet.object == node_id:
            connections[triplet.predicate].append((triplet.subject, 'incoming'))
    
    return dict(connections)


def refine_classifications(initial_classifications: Dict[str, Set[str]], 
                          triplets: List[Triplet], 
                          intent_analysis: IntentAnalysis,
                          entities: List[Entity]) -> Dict[str, Set[str]]:
    """
    Refine entity classifications by considering relationship connections.
    
    Args:
        initial_classifications: Initial entity classifications
        triplets: List of Triplet objects in the knowledge graph
        intent_analysis: IntentAnalysis object with intent information
        entities: List of Entity objects to update relevance on
        
    Returns:
        Refined entity classifications
    """
    logger.info("Refining classifications based on relationships")
    
    # Extract important relationships and intent specificity
    important_relationships = set(intent_analysis.important_relationships)
    intent_specificity = intent_analysis.intent_specificity
    
    # Copy initial classifications
    refined = {
        "core": set(initial_classifications['core']),
        "context": set(initial_classifications['context']),
        "irrelevant": set(initial_classifications['irrelevant'])
    }
    
    # Get all relationships by type
    relationships_by_type = defaultdict(list)
    for triplet in triplets:
        relationships_by_type[triplet.predicate].append((triplet.subject, triplet.object))
    
    # Check for important relationships connecting to core nodes
    promotion_candidates = set()
    
    # First, consider nodes directly connected to core nodes through important relationships
    for core_node in initial_classifications['core']:
        connections = get_node_connections(core_node, triplets)
        
        for rel_type, connected_nodes in connections.items():
            # Adjust promotion criteria based on intent specificity
            should_promote = False
            
            if intent_specificity == "SPECIFIC":
                # For specific intents, only promote if it's an important relationship
                should_promote = rel_type in important_relationships
            elif intent_specificity == "GENERAL":
                # For general intents, be more inclusive
                should_promote = rel_type in important_relationships or not rel_type.startswith("has_")
            else:  # BEHAVIORAL or default
                # For behavioral intents, promote any relationships that might contribute
                should_promote = True
            
            if should_promote:
                for connected_node, direction in connected_nodes:
                    # If the connected node is in context or irrelevant, consider promoting it
                    if connected_node in initial_classifications['context'] or \
                       connected_node in initial_classifications['irrelevant']:
                        promotion_candidates.add(connected_node)
    
    # Promote candidates based on intent specificity
    for candidate in promotion_candidates:
        if intent_specificity == "SPECIFIC":
            # For specific intents, only promote irrelevant to context
            if candidate in refined['irrelevant']:
                refined['irrelevant'].remove(candidate)
                refined['context'].add(candidate)
                logger.debug(f"Promoted '{candidate}' from irrelevant to context")
                
                # Update entity object
                for entity in entities:
                    if entity.id == candidate:
                        entity.relevance = "context"
                        entity.relevance_score = 0.5
                        break
                
        else:
            # For general and behavioral intents, promote more aggressively
            if candidate in refined['irrelevant']:
                refined['irrelevant'].remove(candidate)
                refined['context'].add(candidate)
                logger.debug(f"Promoted '{candidate}' from irrelevant to context")
                
                # Update entity object
                for entity in entities:
                    if entity.id == candidate:
                        entity.relevance = "context"
                        entity.relevance_score = 0.5
                        break
                
            elif candidate in refined['context'] and intent_specificity == "BEHAVIORAL":
                # For behavioral intents, consider promoting context to core
                # if it might trigger the behavior
                refined['context'].remove(candidate)
                refined['core'].add(candidate)
                logger.debug(f"Promoted '{candidate}' from context to core")
                
                # Update entity object
                for entity in entities:
                    if entity.id == candidate:
                        entity.relevance = "core"
                        entity.relevance_score = 1.0
                        break
    
    # Record changes
    initial_counts = {k: len(v) for k, v in initial_classifications.items()}
    refined_counts = {k: len(v) for k, v in refined.items()}
    
    logger.info("Classification refinement complete:")
    logger.info(f"  - Core: {initial_counts['core']} → {refined_counts['core']}")
    logger.info(f"  - Context: {initial_counts['context']} → {refined_counts['context']}")
    logger.info(f"  - Irrelevant: {initial_counts['irrelevant']} → {refined_counts['irrelevant']}")
    
    return refined


def finalize_classifications(classifications: Dict[str, Set[str]]) -> Dict[str, List[str]]:
    """
    Convert classification sets to sorted lists for final output
    
    Args:
        classifications: Dictionary with sets of entity IDs by relevance category
        
    Returns:
        Dictionary with sorted lists of entity IDs by relevance category
    """
    return {
        "core": sorted(list(classifications['core'])),
        "context": sorted(list(classifications['context'])),
        "irrelevant": sorted(list(classifications['irrelevant']))
    }


async def create_subgraph(knowledge_graph: KnowledgeGraph, experiment_intent: str, product_description: str = None, metadata: Dict[str, Any] = None) -> SubGraph:
    """
    Create a subgraph using intent-driven classification.
    
    Args:
        knowledge_graph: The full KnowledgeGraph object
        experiment_intent: The experiment intent to filter by
        product_description: Optional description of the product/domain to provide context
        metadata: Optional metadata for telemetry
        
    Returns:
        SubGraph object representing the filtered subgraph
    """
    logger.info("="*80)
    logger.info(f"Creating subgraph for: '{experiment_intent}'")
    if product_description:
        logger.info(f"Product context: {product_description[:100]}..." if len(product_description) > 100 else product_description)
    logger.info("="*80)
    
    entities = knowledge_graph.entities
    triplets = knowledge_graph.triplets
    
    # Step 1: Analyze the intent pattern with product context
    intent_analysis = await analyze_intent_pattern(experiment_intent, product_description, metadata)
    
    # Log intent specificity for transparency
    specificity = intent_analysis.intent_specificity
    logger.info(f"Intent specificity determined as: {specificity}")
    
    # Step 2: Classify entities based on intent
    initial_classifications = await classify_nodes(entities, intent_analysis, metadata=metadata)
    
    # Step 3: Refine classifications using relationships
    final_classifications = refine_classifications(initial_classifications, triplets, intent_analysis, entities)
    
    # Step 4: Finalize classifications for output
    final_categorized_nodes = finalize_classifications(final_classifications)
    
    # Step 5: Filter entities and triplets
    core_nodes = final_classifications['core']
    context_nodes = final_classifications['context']
    relevant_nodes = core_nodes.union(context_nodes)
    
    all_entity_ids = {entity.id for entity in entities}
    removed_nodes = all_entity_ids - relevant_nodes
    
    filtered_entities = [entity for entity in entities if entity.id in relevant_nodes]
    filtered_triplets = filter_triplets(triplets, relevant_nodes)
    
    # Step 6: Create subgraph
    subgraph_metadata = {
        "experiment_intent": experiment_intent,
        "intent_specificity": intent_analysis.intent_specificity,
        "intent_pattern": intent_analysis.pattern,
        "total_nodes": len(filtered_entities),
        "core_nodes": len(core_nodes),
        "context_nodes": len(context_nodes),
        "total_triplets": len(filtered_triplets),
        "creation_time": datetime.now().isoformat(),
        "approach": "intent-classification",
        "original_graph_nodes": len(entities),
        "original_graph_triplets": len(triplets),
        "removed_nodes_count": len(removed_nodes),
        "reduction_percentage": f"{(len(removed_nodes)/len(entities))*100:.1f}%",
        "node_categories": {
            "core": final_categorized_nodes["core"],
            "context": final_categorized_nodes["context"]
        },
        "removed_nodes": list(removed_nodes),
        "intent_analysis": intent_analysis.to_dict()
    }
    
    # Create SubGraph object
    subgraph = SubGraph(
        entities=filtered_entities,
        relationships=[],  # Will be populated from triplets if needed
        relevance_score=1.0,  # Default high score since this is an intentionally selected subgraph
        focus_entities=list(core_nodes),
        metadata=subgraph_metadata
    )
    
    # Log summary
    logger.info("="*80)
    logger.info("Subgraph creation complete!")
    logger.info(f"Intent pattern: {intent_analysis.pattern}")
    logger.info(f"Core entities: {len(core_nodes)}")
    logger.info(f"Context entities: {len(context_nodes)}")
    logger.info(f"Total kept: {len(filtered_entities)}")
    logger.info(f"Removed: {len(removed_nodes)} ({subgraph_metadata['reduction_percentage']})")
    logger.info("="*80)
    
    return subgraph


def filter_triplets(triplets: List[Triplet], nodes_to_keep: Set[str]) -> List[Triplet]:
    """
    Filter triplets to only include those involving nodes we're keeping
    
    Args:
        triplets: List of Triplet objects to filter
        nodes_to_keep: Set of entity IDs to keep
        
    Returns:
        List of filtered Triplet objects
    """
    filtered = []
    
    for triplet in triplets:
        if triplet.predicate.startswith('has_'):
            # Property triplets - keep if subject is included
            if triplet.subject in nodes_to_keep:
                filtered.append(triplet)
        else:
            # Relationship triplets - keep if both nodes are included
            if triplet.subject in nodes_to_keep and triplet.object in nodes_to_keep:
                filtered.append(triplet)
    
    return filtered


def save_subgraph(subgraph: SubGraph, output_dir: str = "subgraphs", output_path: str = None):
    """
    Save the subgraph and related artifacts to a dedicated directory.
    
    Args:
        subgraph: The SubGraph object to save
        output_dir: The directory to store artifacts in
        output_path: Optional specific path for the output file
        
    Returns:
        Path to the saved JSON file
    """
    # Convert SubGraph to dictionary
    subgraph_dict = subgraph.to_dict()
    
    # Create a more readable slug from the intent
    intent = subgraph.metadata["experiment_intent"]
    intent_slug = intent.lower().replace(' ', '_')
    
    # Remove any special characters
    import re
    intent_slug = re.sub(r'[^a-z0-9_]', '', intent_slug)
    
    # Truncate if too long
    if len(intent_slug) > 50:
        intent_slug = intent_slug[:50]
    
    # Ensure it doesn't end with an underscore
    intent_slug = intent_slug.rstrip('_')
    
    # Create output directory if it doesn't exist
    if not os.path.exists(output_dir):
        logger.info(f"Creating output directory: {output_dir}")
        os.makedirs(output_dir)
    
    # Set output paths
    if output_path is None:
        output_path = os.path.join(output_dir, f"{intent_slug}.json")
    
    report_path = output_path.replace('.json', '_report.txt')
    
    # Create a timestamped directory for this run
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    run_dir = os.path.join(output_dir, f"{intent_slug}_{timestamp}")
    
    if not os.path.exists(run_dir):
        os.makedirs(run_dir)
    
    # Save JSON to both the main directory and the run directory
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(subgraph_dict, f, indent=2, ensure_ascii=False)
    
    run_json_path = os.path.join(run_dir, f"{intent_slug}.json")
    with open(run_json_path, 'w', encoding='utf-8') as f:
        json.dump(subgraph_dict, f, indent=2, ensure_ascii=False)
    
    logger.info(f"Subgraph saved to {output_path} and {run_json_path}")
    
    # Save report to both locations
    with open(report_path, 'w', encoding='utf-8') as f:
        write_report(f, subgraph_dict)
    
    run_report_path = os.path.join(run_dir, f"{intent_slug}_report.txt")
    with open(run_report_path, 'w', encoding='utf-8') as f:
        write_report(f, subgraph_dict)
    
    logger.info(f"Report saved to {report_path} and {run_report_path}")
    
    # Also save metadata for quick reference
    metadata_path = os.path.join(run_dir, "metadata.json")
    with open(metadata_path, 'w', encoding='utf-8') as f:
        json.dump(subgraph.metadata, f, indent=2, ensure_ascii=False)
    
    logger.info(f"All artifacts saved to {run_dir}")
    
    return output_path


def write_report(f, subgraph_dict: Dict[str, Any]):
    """
    Write a human-readable report about the subgraph
    
    Args:
        f: File object to write to
        subgraph_dict: Dictionary representation of the subgraph
    """
    f.write("Intent-Driven Classification Subgraph Report\n")
    f.write("="*50 + "\n\n")
    
    metadata = subgraph_dict['metadata']
    
    f.write(f"Experiment Intent: {metadata['experiment_intent']}\n")
    f.write(f"Intent Specificity: {metadata.get('intent_specificity', 'GENERAL')}\n")
    f.write(f"Intent Pattern: {metadata.get('intent_pattern', 'unknown')}\n")
    f.write(f"Approach: {metadata['approach']}\n")
    f.write(f"Creation Time: {metadata['creation_time']}\n\n")
    
    # Intent analysis
    intent_analysis = metadata.get('intent_analysis', {})
    if intent_analysis:
        f.write("INTENT ANALYSIS\n")
        f.write("-"*50 + "\n")
        
        f.write(f"Pattern: {intent_analysis.get('pattern', 'unknown')}\n")
        
        key_concepts = intent_analysis.get('key_concepts', [])
        if key_concepts:
            f.write("\nKey Concepts:\n")
            for concept in key_concepts:
                f.write(f"  - {concept}\n")
        
        classification_criteria = intent_analysis.get('classification_criteria', {})
        if classification_criteria:
            f.write("\nClassification Criteria:\n")
            for category, criteria in classification_criteria.items():
                f.write(f"  - {category.upper()}: {criteria}\n")
        
        important_relationships = intent_analysis.get('important_relationships', [])
        if important_relationships:
            f.write("\nImportant Relationships:\n")
            for rel in important_relationships:
                f.write(f"  - {rel}\n")
        
        f.write("\n")
    
    f.write("STATISTICS\n")
    f.write("-"*50 + "\n")
    f.write(f"  Original nodes: {metadata.get('original_graph_nodes', 'N/A')}\n")
    f.write(f"  Selected nodes: {metadata['entity_count']}\n")
    f.write(f"    - Core nodes: {metadata['core_entity_count']}\n")
    f.write(f"    - Context nodes: {metadata['context_entity_count']}\n")
    f.write(f"  Removed: {metadata.get('removed_nodes_count', 'N/A')} ({metadata.get('reduction_percentage', 'N/A')})\n\n")
    
    # Core nodes
    f.write("CORE NODES\n")
    f.write("-"*50 + "\n")
    
    if 'node_categories' in metadata:
        core_nodes = metadata['node_categories']['core']
        for node in core_nodes[:20]:
            f.write(f"  - {node}\n")
        if len(core_nodes) > 20:
            f.write(f"  ... and {len(core_nodes) - 20} more\n")
        
        # Context nodes
        f.write("\nCONTEXT NODES\n")
        f.write("-"*50 + "\n")
        context_nodes = metadata['node_categories']['context']
        for node in sorted(context_nodes)[:20]:
            f.write(f"  - {node}\n")
        if len(context_nodes) > 20:
            f.write(f"  ... and {len(context_nodes) - 20} more\n")
    else:
        # Alternative: Extract from entities if node_categories not available
        core_entities = [entity['id'] for entity in subgraph_dict.get('entities', []) 
                        if entity.get('relevance') == 'core']
        f.write(f"Core Entities: {', '.join(core_entities[:20])}")
        if len(core_entities) > 20:
            f.write(f" and {len(core_entities) - 20} more")
        f.write("\n")
    
    # Removed nodes summary
    f.write("\n" + "="*50 + "\n")
    f.write("REMOVED NODES SUMMARY\n")
    f.write("="*50 + "\n")
    
    removed_nodes = set(metadata.get('removed_nodes', []))
    if removed_nodes:
        f.write(f"\nTotal removed: {len(removed_nodes)}\n")
        f.write("\nSample of removed nodes:\n")
        for node in sorted(list(removed_nodes))[:50]:
            f.write(f"  - {node}\n")
        if len(removed_nodes) > 50:
            f.write(f"  ... and {len(removed_nodes) - 50} more\n")


