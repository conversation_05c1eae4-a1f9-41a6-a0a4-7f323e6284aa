from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Set
from datetime import datetime


@dataclass
class Entity:
    """Represents an entity in the knowledge graph."""
    id: str
    type: Optional[str] = None
    properties: Dict[str, Any] = field(default_factory=dict)
    relevance: Optional[str] = None  # "core", "context", "irrelevant"
    relevance_score: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert entity to dictionary representation."""
        return {
            "id": self.id,
            "type": self.type,
            "properties": self.properties,
            "relevance": self.relevance,
            "relevance_score": self.relevance_score
        }


@dataclass
class Relationship:
    """Represents a relationship between two entities in the knowledge graph."""
    source_id: str
    target_id: str
    type: str
    properties: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert relationship to dictionary representation."""
        return {
            "source": self.source_id,
            "target": self.target_id,
            "type": self.type,
            "properties": self.properties
        }


@dataclass
class Triplet:
    """Represents a knowledge graph triplet (subject-predicate-object)."""
    subject: str
    predicate: str
    object: str
    
    def to_dict(self) -> Dict[str, str]:
        """Convert triplet to dictionary representation."""
        return {
            "subject": self.subject,
            "predicate": self.predicate,
            "object": self.object
        }
    
    def __str__(self) -> str:
        """String representation of the triplet."""
        return f"({self.subject}) --[{self.predicate}]--> ({self.object})"


@dataclass
class EntityResolutionResult:
    """Results of entity resolution process."""
    original_count: int
    merged_count: int
    mapping: Dict[str, str] = field(default_factory=dict)
    
    @property
    def reduction_count(self) -> int:
        """Number of entities reduced."""
        return self.original_count - self.merged_count
    
    @property
    def reduction_percentage(self) -> float:
        """Percentage of entities reduced."""
        if self.original_count == 0:
            return 0.0
        return (self.reduction_count / self.original_count) * 100
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert result to dictionary representation."""
        return {
            "original_count": self.original_count,
            "merged_count": self.merged_count,
            "reduction_count": self.reduction_count,
            "reduction_percentage": self.reduction_percentage,
            "mapping": self.mapping
        }


@dataclass
class KnowledgeGraph:
    """Represents a complete knowledge graph."""
    entities: List[Entity] = field(default_factory=list)
    relationships: List[Relationship] = field(default_factory=list)
    triplets: List[Triplet] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    creation_time: datetime = field(default_factory=datetime.now)
    
    @property
    def entity_count(self) -> int:
        """Number of entities in the graph."""
        return len(self.entities)
    
    @property
    def relationship_count(self) -> int:
        """Number of relationships in the graph."""
        return len(self.relationships)
    
    @property
    def triplet_count(self) -> int:
        """Number of triplets in the graph."""
        return len(self.triplets)
    
    @property
    def entity_types(self) -> Set[str]:
        """Set of entity types in the graph."""
        return {entity.type for entity in self.entities if entity.type}
    
    @property
    def relationship_types(self) -> Set[str]:
        """Set of relationship types in the graph."""
        return {rel.type for rel in self.relationships}
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert knowledge graph to dictionary representation."""
        return {
            "metadata": {
                "entity_count": self.entity_count,
                "relationship_count": self.relationship_count,
                "triplet_count": self.triplet_count,
                "entity_types": list(self.entity_types),
                "relationship_types": list(self.relationship_types),
                "creation_time": self.creation_time.isoformat(),
                **self.metadata
            },
            "entities": [entity.to_dict() for entity in self.entities],
            "relationships": [rel.to_dict() for rel in self.relationships],
            "triplets": [triplet.to_dict() for triplet in self.triplets]
        }
    
    def to_json(self, indent: int = 2) -> str:
        """Convert knowledge graph to JSON string."""
        import json
        return json.dumps(self.to_dict(), indent=indent, ensure_ascii=False)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "KnowledgeGraph":
        """Create a KnowledgeGraph from dictionary representation."""
        metadata = data.get("metadata", {})
        
        # Create entities
        entities = []
        for entity_data in data.get("entities", []):
            entity = Entity(
                id=entity_data["id"],
                type=entity_data.get("type"),
                properties=entity_data.get("properties", {}),
                relevance=entity_data.get("relevance"),
                relevance_score=entity_data.get("relevance_score", 0.0)
            )
            entities.append(entity)
        
        # Create relationships
        relationships = []
        for rel_data in data.get("relationships", []):
            relationship = Relationship(
                source_id=rel_data["source"],
                target_id=rel_data["target"],
                type=rel_data["type"],
                properties=rel_data.get("properties", {})
            )
            relationships.append(relationship)
        
        # Create triplets
        triplets = []
        for triplet_data in data.get("triplets", []):
            triplet = Triplet(
                subject=triplet_data["subject"],
                predicate=triplet_data["predicate"],
                object=triplet_data["object"]
            )
            triplets.append(triplet)
        
        # Extract creation time
        creation_time = metadata.get("creation_time")
        if creation_time:
            try:
                creation_time = datetime.fromisoformat(creation_time)
            except (ValueError, TypeError):
                creation_time = datetime.now()
        else:
            creation_time = datetime.now()
        
        # Create knowledge graph
        return cls(
            entities=entities,
            relationships=relationships,
            triplets=triplets,
            metadata={k: v for k, v in metadata.items() 
                     if k not in ["entity_count", "relationship_count", "triplet_count", 
                                 "entity_types", "relationship_types", "creation_time"]},
            creation_time=creation_time
        )


@dataclass
class SubGraph:
    """Represents a selected subgraph from the main knowledge graph."""
    entities: List[Entity]
    relationships: List[Relationship]
    relevance_score: float = 0.0
    focus_entities: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def entity_count(self) -> int:
        """Number of entities in the subgraph."""
        return len(self.entities)
    
    @property
    def relationship_count(self) -> int:
        """Number of relationships in the subgraph."""
        return len(self.relationships)
    
    @property
    def core_entities(self) -> List[Entity]:
        """List of core entities in the subgraph."""
        return [e for e in self.entities if e.relevance == "core"]
    
    @property
    def context_entities(self) -> List[Entity]:
        """List of context entities in the subgraph."""
        return [e for e in self.entities if e.relevance == "context"]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert subgraph to dictionary representation."""
        return {
            "metadata": {
                "entity_count": self.entity_count,
                "relationship_count": self.relationship_count,
                "core_entity_count": len(self.core_entities),
                "context_entity_count": len(self.context_entities),
                "relevance_score": self.relevance_score,
                "focus_entities": self.focus_entities,
                **self.metadata
            },
            "entities": [entity.to_dict() for entity in self.entities],
            "relationships": [rel.to_dict() for rel in self.relationships]
        }


@dataclass
class IntentAnalysis:
    """Captures the analysis of an experiment intent."""
    pattern: str
    key_concepts: List[str]
    classification_criteria: Dict[str, str]
    important_relationships: List[str]
    intent_specificity: str
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert intent analysis to dictionary representation."""
        return {
            "pattern": self.pattern,
            "key_concepts": self.key_concepts,
            "classification_criteria": self.classification_criteria,
            "important_relationships": self.important_relationships,
            "intent_specificity": self.intent_specificity
        }


@dataclass
class NodeClassification:
    """For categorizing entities by relevance."""
    entity_id: str
    relevance: str  # "core", "context", "irrelevant"
    relevance_score: float
    justification: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert node classification to dictionary representation."""
        return {
            "entity_id": self.entity_id,
            "relevance": self.relevance,
            "relevance_score": self.relevance_score,
            "justification": self.justification
        }


@dataclass
class ExtractionCriteria:
    """Defines criteria for subgraph extraction."""
    core_entity_types: List[str]
    context_entity_types: List[str]
    relationship_types: List[str]
    max_distance: int = 2
    min_relevance_score: float = 0.5
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert extraction criteria to dictionary representation."""
        return {
            "core_entity_types": self.core_entity_types,
            "context_entity_types": self.context_entity_types,
            "relationship_types": self.relationship_types,
            "max_distance": self.max_distance,
            "min_relevance_score": self.min_relevance_score
        }