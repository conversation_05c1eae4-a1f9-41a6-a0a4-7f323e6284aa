import logging
import json
from typing import List, Dict, Tuple, Any, Optional
from collections import defaultdict

from langchain_community.graphs.graph_document import Node, Relationship as LCRelationship

from ..utils import format_prompt_from_template, achat_completion, batch_gather
from .models import Relationship, EntityResolutionResult
from ..constants import GPT_4_1_MINI

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def normalize_entity(entity_name: str) -> str:
    """
    Normalize entity names for comparison.
    - Convert to lowercase
    - Remove extra whitespace
    - Remove common punctuation
    - Expand common abbreviations
    """
    # Basic normalization
    normalized = entity_name.lower().strip()
    
    # Remove extra whitespace
    normalized = ' '.join(normalized.split())
    
    # Remove common punctuation (but keep dots in abbreviations)
    normalized = normalized.replace(',', '').replace(';', '').replace(':', '')
    
    # Expand common abbreviations (add more as needed)
    abbreviations = {
        'inc.': 'incorporated',
        'corp.': 'corporation',
        'ltd.': 'limited',
        'co.': 'company',
        'llc': 'limited liability company',
        'intl.': 'international',
        'govt.': 'government',
        'dept.': 'department',
        'univ.': 'university',
        'assoc.': 'association',
        'org.': 'organization'
    }
    
    for abbrev, full in abbreviations.items():
        normalized = normalized.replace(abbrev, full)
    
    return normalized


def exact_match_resolution(nodes: List[Node]) -> Dict[str, str]:
    """
    Find exact matches after normalization.
    Returns a mapping of node IDs to their canonical representative.
    """
    logger.info(f"Starting exact match resolution for {len(nodes)} nodes")
    
    # Create a mapping of normalized names to nodes
    normalized_to_nodes = defaultdict(list)
    
    for node in nodes:
        normalized = normalize_entity(node.id)
        normalized_to_nodes[normalized].append(node)
    
    # Create mapping from node ID to canonical ID
    id_mapping = {}
    exact_match_groups = []
    
    for normalized_name, node_list in normalized_to_nodes.items():
        if len(node_list) > 1:
            # Multiple nodes map to the same normalized name
            canonical_node = node_list[0]  # Use first as canonical
            group = []
            
            for node in node_list:
                id_mapping[node.id] = canonical_node.id
                group.append(node.id)
            
            exact_match_groups.append(group)
            logger.info(f"Exact match found: {group}")
    
    logger.info(f"Found {len(exact_match_groups)} exact match groups")
    return id_mapping


async def process_entity_batch(batch: List[Node], batch_num: int, metadata: Optional[Dict[str, Any]] = None) -> Dict[str, str]:
    """
    Process a batch of nodes for entity resolution.
    Returns a mapping of entity variants to canonical forms.
    """
    # Prepare entity list with properties for better context
    entity_data = []
    for node in batch:
        entity_info = {"name": node.id}
        if node.properties:
            entity_info["properties"] = node.properties
        entity_data.append(entity_info)
    
    prompt = format_prompt_from_template(
        "knowledge_graph/entity_resolution.jinja2",
        entity_data=json.dumps(entity_data, indent=2)
    )
    
    try:
        content = await achat_completion(
                model=GPT_4_1_MINI,
            messages=[{"role": "user", "content": prompt}],
            metadata=metadata or {}
        )
        
        # Handle potential markdown code blocks
        content = content.strip()
        if "```json" in content:
            content = content.split("```json")[1].split("```")[0].strip()
        elif "```" in content:
            content = content.split("```")[1].split("```")[0].strip()
            
        result = json.loads(content)
        batch_mapping = {}
        
        # Process the results
        if result:  # Only process if we have results
            logger.info(f"Batch {batch_num}: LLM found {len(result)} potential merges")
            
            # Create reverse mapping to find all variants for each canonical form
            canonical_to_variants = defaultdict(list)
            for variant, canonical in result.items():
                canonical_to_variants[canonical].append(variant)
            
            # Add canonical forms to the mapping too
            for canonical, variants in canonical_to_variants.items():
                # Ensure the canonical form exists in our batch
                canonical_node = next((n for n in batch if n.id == canonical), None)
                if not canonical_node and variants:
                    # Use the first variant as canonical if the suggested canonical doesn't exist
                    canonical = variants[0]
                    canonical_node = next((n for n in batch if n.id == canonical), None)
                
                if canonical_node:
                    # Map all variants to the canonical form
                    for variant in variants:
                        if variant != canonical and any(n.id == variant for n in batch):
                            batch_mapping[variant] = canonical
                            logger.info(f"  LLM resolved: '{variant}' -> '{canonical}'")
                    
                    # Also ensure canonical maps to itself
                    if canonical not in batch_mapping:
                        batch_mapping[canonical] = canonical
        
        return batch_mapping
        
    except json.JSONDecodeError as e:
        logger.error(f"Error parsing JSON in batch {batch_num}: {e}")
        return {}
    except Exception as e:
        logger.error(f"Error in LLM resolution for batch {batch_num}: {e}")
        return {}

async def batch_resolve_with_llm(nodes: List[Node], existing_mapping: Optional[Dict[str, str]] = None,
                          batch_size: int = 25, metadata: Optional[Dict[str, Any]] = None) -> Dict[str, str]:
    """
    Use LLM to intelligently resolve entity matches based on semantic meaning.
    Only processes nodes not already mapped.
    """
    if existing_mapping is None:
        existing_mapping = {}
    
    # Get unmapped nodes
    unmapped_nodes = [n for n in nodes if n.id not in existing_mapping]
    logger.info(f"Starting LLM resolution for {len(unmapped_nodes)} unmapped nodes")
    
    if len(unmapped_nodes) < 2:
        return {}
    
    llm_mapping = {}
    
    # Analyze the types of entities we're dealing with
    entity_types = set()
    for node in unmapped_nodes[:20]:  # Sample first 20 for context
        if node.properties and 'type' in node.properties:
            entity_types.add(node.properties['type'])
    
    # Process in batches using batch_gather for concurrency
    batch_tasks = []
    for batch_num, i in enumerate(range(0, len(unmapped_nodes), batch_size)):
        batch = unmapped_nodes[i:i + batch_size]
        task = process_entity_batch(batch, batch_num, metadata)
        batch_tasks.append(task)
    
    # Execute all batch processing tasks concurrently
    logger.info(f"Processing {len(batch_tasks)} batches of entities")
    batch_results = await batch_gather(tasks=batch_tasks, batch_size=3)
    
    # Combine all batch results
    for batch_mapping in batch_results:
        llm_mapping.update(batch_mapping)
    
    logger.info(f"LLM resolved {len(llm_mapping)} entity mappings")
    return llm_mapping


async def merge_entities(nodes: List[Node], metadata: Optional[Dict[str, Any]] = None) -> Tuple[List[Node], EntityResolutionResult]:
    """
    Main function to orchestrate entity resolution.
    Returns merged nodes and entity resolution results.
    """
    logger.info("="*60)
    logger.info("Starting entity resolution process")
    logger.info(f"Total nodes to process: {len(nodes)}")
    logger.info("="*60)
    
    # Step 1: Exact matching
    exact_mapping = exact_match_resolution(nodes)
    logger.info(f"After exact matching: {len(exact_mapping)} nodes mapped")
    
    # Step 2: LLM resolution on remaining nodes
    llm_mapping = await batch_resolve_with_llm(nodes, existing_mapping=exact_mapping, metadata=metadata)
    logger.info(f"After LLM resolution: {len(llm_mapping)} additional nodes mapped")
    
    # Combine all mappings
    final_mapping = {**exact_mapping, **llm_mapping}
    
    # Create deduplicated node list
    seen_canonical_ids = set()
    merged_nodes = []
    
    for node in nodes:
        canonical_id = final_mapping.get(node.id, node.id)
        
        if canonical_id not in seen_canonical_ids:
            # Keep the first occurrence with the canonical ID
            if canonical_id != node.id:
                # Create new node with canonical ID
                merged_node = Node(id=canonical_id, properties=node.properties)
            else:
                merged_node = node
            
            merged_nodes.append(merged_node)
            seen_canonical_ids.add(canonical_id)
    
    # Create resolution result
    resolution_result = EntityResolutionResult(
        original_count=len(nodes),
        merged_count=len(merged_nodes),
        mapping=final_mapping
    )
    
    logger.info("="*60)
    logger.info(f"Entity resolution complete:")
    logger.info(f"  Original nodes: {resolution_result.original_count}")
    logger.info(f"  Merged nodes: {resolution_result.merged_count}")
    logger.info(f"  Reduction: {resolution_result.reduction_count} nodes ({resolution_result.reduction_percentage:.1f}%)")
    logger.info("="*60)
    
    return merged_nodes, resolution_result


def update_relationships(relationships: List[LCRelationship], node_mapping: Dict[str, str]) -> List[Relationship]:
    """
    Update relationships to use the new merged node IDs.
    Returns our custom Relationship objects.
    """
    logger.info(f"Updating {len(relationships)} relationships with merged node IDs")
    
    updated_relationships = []
    update_count = 0
    
    for rel in relationships:
        # Get canonical IDs
        source_id = node_mapping.get(rel.source.id, rel.source.id)
        target_id = node_mapping.get(rel.target.id, rel.target.id)
        
        # Check if anything changed
        if source_id != rel.source.id or target_id != rel.target.id:
            update_count += 1
            logger.debug(f"Updating relationship: ({rel.source.id})-[{rel.type}]->({rel.target.id}) "
                        f"to ({source_id})-[{rel.type}]->({target_id})")
        
        # Create updated relationship using our custom Relationship class
        updated_rel = Relationship(
            source_id=source_id,
            target_id=target_id,
            type=rel.type,
            properties=rel.properties
        )
        updated_relationships.append(updated_rel)
    
    logger.info(f"Updated {update_count} relationships")
    return updated_relationships