from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any


class KnowledgeGraphSystemPromptGeneratorSettings(BaseModel):
    """Settings for the Knowledge Graph System Prompt Generator."""

    max_personas: int = Field(
        default=5,
        description="Maximum number of personas to generate from knowledge graph",
    )
    max_topics_per_persona: int = Field(
        default=5,
        description="Maximum number of topics to generate per persona",
    )
    max_tokens: int = Field(
        default=500,
        description="Maximum number of tokens to use in prompts",
    )
    temperature: float = Field(
        default=0.7,
        description="Temperature for persona and topic generation",
    )
    model: str = Field(
        default="gpt-4.1-mini",
        description="The model to use for generation",
    )
    include_context: bool = Field(
        default=True,
        description="Whether to include context in the generation",
    )
    additional_settings: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Additional settings for the generator",
    )