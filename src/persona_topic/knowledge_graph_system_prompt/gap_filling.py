import os
import json
from datetime import datetime
from collections import defaultdict, deque
from typing import Dict, List, Set, Any, Optional

from utils.logger import logger
from ..utils import format_prompt_from_template, achat_completion, batch_gather, extract_json_from_text
from .models import Entity, Relationship, Triplet, SubGraph
from .gap_filling_models import Island, TopicAllocation, IslandProcessingParams, IslandResult, GapFillingResult

def identify_islands(subgraph: SubGraph, core_node_ids: List[str]) -> List[Island]:
    """
    Identify disconnected islands (components) in the subgraph
    
    Args:
        subgraph: SubGraph object containing entities and relationships
        core_node_ids: List of core node IDs
        
    Returns:
        List of Island objects representing connected components
    """
    logger.info("Identifying islands in the subgraph")
    
    # Create adjacency list
    adjacency_list = defaultdict(set)
    
    # Convert entities to a map for quick lookup
    entity_map = {entity.id: entity for entity in subgraph.entities}
    
    # Create Triplet objects for relationships
    triplets = []
    for rel in subgraph.relationships:
        triplet = Triplet(
            subject=rel.source_id,
            predicate=rel.type,
            object=rel.target_id
        )
        triplets.append(triplet)
        
        # Skip property triplets if they exist (has_ predicates)
        if rel.type.startswith('has_'):
            continue
            
        # Add bidirectional connections
        adjacency_list[rel.source_id].add(rel.target_id)
        adjacency_list[rel.target_id].add(rel.source_id)
    
    # Get all node IDs
    all_node_ids = {entity.id for entity in subgraph.entities}
    
    # Find islands using BFS
    islands = []
    visited = set()
    
    for node_id in all_node_ids:
        if node_id in visited:
            continue
            
        # Start a new island
        island_nodes = set()
        queue = deque([node_id])
        
        while queue:
            current = queue.popleft()
            if current in visited:
                continue
                
            visited.add(current)
            island_nodes.add(current)
            
            # Add neighbors
            for neighbor in adjacency_list[current]:
                if neighbor not in visited and neighbor in all_node_ids:
                    queue.append(neighbor)
        
        # Create island island_triplets (relationships within this island)
        island_triplets = []
        for triplet in triplets:
            if triplet.subject in island_nodes and triplet.object in island_nodes:
                island_triplets.append(triplet)
        
        # Identify core and context nodes in this island
        island_core_nodes = {node_id for node_id in core_node_ids if node_id in island_nodes}
        island_context_nodes = island_nodes - island_core_nodes
        
        # Create Island object
        island = Island(
            nodes=island_nodes,
            core_nodes=island_core_nodes,
            context_nodes=island_context_nodes,
            triplets=island_triplets,
            index=len(islands)
        )
        
        islands.append(island)
    
    # Sort islands by size (largest first)
    islands.sort(key=lambda x: x.size, reverse=True)
    
    # Log island statistics
    logger.info(f"Found {len(islands)} islands in the subgraph")
    for i, island in enumerate(islands):
        islands[i].index = i  # Update index after sorting
        logger.info(f"Island {i+1}: {island.size} nodes, {island.core_node_count} core nodes")
    
    return islands


def allocate_topics(islands: List[Island], total_topics: int, 
                   node_topic_map: Optional[Dict[str, List[str]]] = None) -> List[TopicAllocation]:
    """
    Allocate topics to islands proportional to their size and current coverage
    
    Args:
        islands: List of Island objects
        total_topics: Total number of topics to allocate (K)
        node_topic_map: Optional mapping of nodes to existing topics for coverage calculation
        
    Returns:
        List of TopicAllocation objects representing the number of topics allocated to each island
    """
    logger.info(f"Allocating {total_topics} topics across {len(islands)} islands")
    
    # Calculate island sizes and total nodes
    island_sizes = [island.size for island in islands]
    total_nodes = sum(island_sizes)
    
    # Calculate coverage for each island if node_topic_map is provided
    for island in islands:
        if node_topic_map:
            covered_nodes = sum(1 for node_id in island.nodes if node_topic_map.get(node_id))
            island.coverage = covered_nodes / island.size if island.size else 0
        else:
            island.coverage = 0  # Default to 0 coverage if no map provided
    
    # Allocate topics based on size and inverse of coverage
    # This gives more topics to larger islands with less coverage
    allocations = []
    remaining = total_topics
    
    # Calculate adjusted weight for each island
    island_weights = []
    for island in islands:
        size_factor = island.size / total_nodes
        coverage_factor = 1 - island.coverage  # Inverse of coverage
        weight = size_factor * (1 + coverage_factor)  # Boost weight for islands with less coverage
        island_weights.append(weight)
    
    # Normalize weights
    total_weight = sum(island_weights)
    if total_weight > 0:
        normalized_weights = [w / total_weight for w in island_weights]
    else:
        # Fallback to size-based allocation if weights are all zero
        normalized_weights = [island.size / total_nodes for island in islands]
    
    # Allocate topics based on normalized weights
    for i, (island, weight) in enumerate(zip(islands, normalized_weights)):
        # Allocate topics, ensuring at least 1 topic per island if possible
        allocation = max(1, int(weight * total_topics)) if remaining > 0 else 0
        
        # Don't allocate more than remaining
        allocation = min(allocation, remaining)
        
        topic_allocation = TopicAllocation(
            island_index=island.index,
            island_size=island.size,
            allocated_topics=allocation,
            coverage=island.coverage,
            weight=weight
        )
        
        allocations.append(topic_allocation)
        remaining -= allocation
    
    # Distribute any remaining topics due to rounding
    # Prioritize islands with lowest coverage
    if remaining > 0:
        # Get indices sorted by coverage (lowest first)
        sorted_indices = sorted(range(len(islands)), key=lambda i: islands[i].coverage)
        
        # Allocate remaining topics to islands with lowest coverage
        for i in range(remaining):
            if i < len(sorted_indices):
                idx = sorted_indices[i]
                allocations[idx].allocated_topics += 1
    
    logger.info(f"Topic allocation: {[a.allocated_topics for a in allocations]}")
    for alloc in allocations:
        logger.info(f"Island {alloc.island_index+1}: {alloc.allocated_topics} topics allocated (coverage: {alloc.coverage:.1%})")
    
    return allocations


def match_topics_to_nodes(topics: List[str], subgraph: SubGraph) -> Dict[str, List[str]]:
    """
    Map existing topics to nodes they likely cover
    
    Args:
        topics: List of existing topics
        subgraph: SubGraph object containing entities
        
    Returns:
        Dictionary mapping node IDs to list of topics that cover them
    """
    logger.info("Mapping existing topics to subgraph nodes")
    
    # Create mapping of nodes to covering topics
    node_topic_map = defaultdict(list)
    node_ids = [entity.id for entity in subgraph.entities]
    
    # Simple exact matching and substring matching
    for topic in topics:
        topic_lower = topic.lower()
        matched = False
        
        for node_id in node_ids:
            node_lower = node_id.lower()
            
            # Check for exact match or substring match
            if (topic_lower == node_lower or 
                topic_lower in node_lower or 
                node_lower in topic_lower):
                node_topic_map[node_id].append(topic)
                matched = True
        
        if not matched:
            logger.debug(f"Topic '{topic}' didn't match any node directly")
    
    # Log coverage statistics
    covered_nodes = [node_id for node_id in node_ids if node_topic_map[node_id]]
    logger.info(f"Mapped topics to {len(covered_nodes)}/{len(node_ids)} nodes")
    
    return dict(node_topic_map)


async def generate_topics_for_island(island: Island, 
                              node_topic_map: Dict[str, List[str]], 
                              num_topics: int,
                              experiment_intent: str,
                              product_description: Optional[str] = None) -> List[str]:
    """
    Generate new topics for an island based on gaps in coverage
    
    Args:
        island: Island object containing node IDs and triplets
        node_topic_map: Mapping of nodes to existing topics
        num_topics: Number of topics to generate for this island
        experiment_intent: The experiment intent for context
        product_description: Optional product description for context
        
    Returns:
        List of newly generated topics
    """
    if num_topics <= 0:
        return []
        
    logger.info(f"Generating {num_topics} topics for island {island.index+1} with {island.size} nodes")
    
    # Find uncovered nodes in this island
    uncovered_nodes = [node_id for node_id in island.nodes if not node_topic_map.get(node_id)]
    
    # Calculate coverage percentage
    coverage = (island.size - len(uncovered_nodes)) / island.size if island.size else 0
    logger.info(f"Island has {len(uncovered_nodes)} uncovered nodes ({coverage:.1%} coverage)")
    
    # Get existing topics for this island
    existing_topics = []
    for node_id in island.nodes:
        existing_topics.extend(node_topic_map.get(node_id, []))
    existing_topics = list(set(existing_topics))  # Remove duplicates
    
    # Find uncovered core nodes (highest priority)
    uncovered_core_nodes = [node_id for node_id in island.core_nodes if not node_topic_map.get(node_id)]
    
    # Convert island data to lists for the template
    island_core_nodes_list = list(island.core_nodes)
    island_context_nodes_list = list(island.context_nodes)
    
    # Convert triplets to dictionaries for the template
    island_triplets_dict = [triplet.to_dict() for triplet in island.triplets]
    
    try:
        # Format prompt using Jinja template
        prompt = format_prompt_from_template(
            "knowledge_graph/gap_filling_topics.jinja2",
            experiment_intent=experiment_intent,
            product_description=product_description,
            num_topics=num_topics,
            existing_topics=existing_topics,
            island_core_nodes=island_core_nodes_list,
            uncovered_core_nodes=uncovered_core_nodes,
            island_context_nodes=island_context_nodes_list,
            island_triplets=island_triplets_dict
        )
        
        # Generate topics using LLM
        content = await achat_completion(
            model="o3",
            messages=[{"role": "user", "content": prompt}],
            metadata={"task": "gap_filling_topic_generation"},
            max_completion_tokens=1024
        )
        
        # Extract JSON from the response
        generated_topics = extract_json_from_text(content)
        
        # Validate the response
        if not isinstance(generated_topics, list):
            logger.error(f"LLM returned invalid format: {content}")
            generated_topics = []
        
        # Ensure we have exactly the right number of topics
        if len(generated_topics) != num_topics:
            logger.warning(f"LLM generated {len(generated_topics)} topics instead of {num_topics}")
            
            # Truncate if too many
            if len(generated_topics) > num_topics:
                generated_topics = generated_topics[:num_topics]
            
            # Fill with placeholders if too few
            while len(generated_topics) < num_topics:
                generated_topics.append(f"additional topic {len(generated_topics) + 1}")
        
        logger.info(f"Generated {len(generated_topics)} new topics for island {island.index+1}")
        for topic in generated_topics:
            logger.info(f"  - {topic}")
            
        return generated_topics
        
    except Exception as e:
        logger.error(f"Error generating topics for island {island.index+1}: {e}")
        # Return placeholder topics in case of error
        return [f"island topic {i+1}" for i in range(num_topics)]


async def process_island_parallel(params: IslandProcessingParams) -> IslandResult:
    """
    Process a single island (for parallel execution)
    
    Args:
        params: IslandProcessingParams containing island and processing parameters
        
    Returns:
        IslandResult object with generation results
    """
    island = params.island
    num_topics = params.num_topics
    
    logger.info(f"Processing island {island.index+1} with {num_topics} allocated topics")
    logger.info(f"Island {island.index+1} has {island.core_node_count} core nodes: {', '.join(list(island.core_nodes)[:5])}...")
    
    island_topics = await generate_topics_for_island(
        island, 
        params.node_topic_map, 
        num_topics,
        params.experiment_intent,
        params.product_description
    )
    
    # Create island result object
    result = IslandResult(
        island_index=island.index,
        core_nodes=list(island.core_nodes),
        all_nodes=sorted(list(island.nodes)),
        new_topics=island_topics,
        node_count=island.size,
        core_node_count=island.core_node_count
    )
    
    return result


async def generate_gap_filling_topics(subgraph: SubGraph, 
                              existing_topics: List[str], 
                              k: int,
                              product_description: Optional[str] = None,
                              max_workers: int = 4) -> GapFillingResult:
    """
    Main function to generate gap-filling topics
    
    Args:
        subgraph: The SubGraph object
        existing_topics: List of existing topics
        k: Total number of topics to generate
        product_description: Optional product description for context
        max_workers: Maximum number of concurrent workers for parallel processing
        
    Returns:
        GapFillingResult object containing new topics and island information
    """
    logger.info(f"Starting gap filling process for {k} new topics with {max_workers} parallel workers")
    
    # Extract experiment intent from subgraph metadata
    experiment_intent = subgraph.metadata.get('experiment_intent', "")
    
    # Get core nodes from subgraph
    core_node_ids = subgraph.focus_entities
    logger.info(f"Found {len(core_node_ids)} core nodes in the subgraph")
    
    # Step 1: Identify islands
    islands = identify_islands(subgraph, core_node_ids)
    
    # Step 2: Match existing topics to nodes
    node_topic_map = match_topics_to_nodes(existing_topics, subgraph)
    
    # Step 3: Allocate topics across islands based on size and coverage
    topic_allocations = allocate_topics(islands, k, node_topic_map)
    
    # Step 4: Generate topics for each island in parallel
    island_results = []
    
    # Only process islands that have topics allocated
    processing_params = []
    for island, allocation in zip(islands, topic_allocations):
        if allocation.allocated_topics > 0:
            params = IslandProcessingParams(
                island_idx=island.index,
                island=island,
                num_topics=allocation.allocated_topics,
                node_topic_map=node_topic_map,
                experiment_intent=experiment_intent,
                product_description=product_description
            )
            processing_params.append(params)
    
    if processing_params:
        logger.info(f"Processing {len(processing_params)} islands")
        
        # Create tasks for all islands
        tasks = [process_island_parallel(params) for params in processing_params]
        
        # Process islands in parallel batches
        island_results = await batch_gather(tasks=tasks, batch_size=max_workers)
        
        for result in island_results:
            logger.info(f"Completed processing for island {result.island_index+1}")
    
    # Flatten all generated topics
    all_topics = []
    for result in island_results:
        all_topics.extend(result.new_topics)
    
    logger.info(f"Generated {len(all_topics)} total new topics across {len(island_results)} islands")
    
    # Create final result object
    gap_filling_result = GapFillingResult(
        new_topics=all_topics,
        islands=island_results,
        total_new_topics=len(all_topics),
        total_islands=len(island_results),
        total_core_nodes=len(core_node_ids)
    )
    
    return gap_filling_result


def save_results(results: GapFillingResult, 
               subgraph_path: str, 
               existing_topics_path: str,
               output_dir: str = "gap_filled_topics") -> str:
    """
    Save the results to a JSON file
    
    Args:
        results: GapFillingResult object containing new topics and island information
        subgraph_path: Path to the subgraph file
        existing_topics_path: Path to the existing topics file
        output_dir: Directory to save results in
        
    Returns:
        Path to the output file
    """
    # Create output directory if it doesn't exist
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # Get base names without directories and extensions
    subgraph_base = os.path.splitext(os.path.basename(subgraph_path))[0]
    
    # Create output path using the timestamp from the results
    timestamp = results.creation_time.strftime("%Y%m%d_%H%M%S")
    output_path = os.path.join(output_dir, f"{subgraph_base}_gap_topics_{timestamp}.json")
    
    # Convert results to dictionary and add additional metadata
    output_data = results.to_dict()
    output_data.update({
        "subgraph_source": subgraph_path,
        "existing_topics_source": existing_topics_path,
    })
    
    # Save to file
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(output_data, f, indent=2)
    
    logger.info(f"Results saved to {output_path}")
    return output_path


