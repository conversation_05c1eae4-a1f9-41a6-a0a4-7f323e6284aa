from pydantic_settings import BaseSettings, SettingsConfigDict


class HistoricalDataGeneratorSettings(BaseSettings):
    max_personas: int = 5
    max_topics: int = 5
    extract_topic_prompt: str = "historical/historical_conversation_to_context.jinja2"
    extract_topic_model: str = "gpt-4o-mini"
    mine_topics_from_conversation_prompt: str = (
        "historical/tactics_from_conversation.jinja2"
    )
    mine_topics_from_conversation_model: str = "gpt-4o-mini"

    expand_topics_prompt: str = "historical/topic_item_type.jinja2"

    backfill_persona_from_topic_prompt: str = (
        "historical/persona_from_role_and_topic.jinja2"
    )
    backfill_persona_from_topic_model: str = "gpt-4o-mini"

    resize_list_prompt: str = "historical/mined_topic_item_type.jinja2"

    model_config = SettingsConfigDict(env_file=".env", extra="ignore")
