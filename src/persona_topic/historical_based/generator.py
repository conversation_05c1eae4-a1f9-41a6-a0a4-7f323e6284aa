import asyncio
from typing import List, Dict
from ..models import PersonaTopic, DataGenSource
from ..utils import (
    achat_completion,
    format_prompt_from_template,
    extract_json_from_text,
    resize_list,
    batch_gather,
)
from ..persona_topic_generator import PersonaTopicGeneratorInterface
from utils.logger import logger
from .settings import HistoricalDataGeneratorSettings


class HistoricalDataGenerator(PersonaTopicGeneratorInterface):
    def __init__(
        self,
        settings: HistoricalDataGeneratorSettings = HistoricalDataGeneratorSettings(),
        metadata={},
    ):
        super().__init__(metadata=metadata)
        self.settings = settings or HistoricalDataGeneratorSettings()

    async def extract_topic_from_conversation(
        self, conversation: str, role: str
    ) -> str:
        prompt = format_prompt_from_template(
            self.settings.extract_topic_prompt,
            role=role,
            conversation=conversation,
        )
        completion = await achat_completion(
            model=self.settings.extract_topic_model,
            messages=[{"role": "user", "content": prompt}],
            metadata=self.metadata,
        )
        return completion

    async def mine_tactics_from_conversation(
        self, conversation: str
    ) -> List[Dict[str, List[str] | str]]:
        prompt = format_prompt_from_template(
            self.settings.mine_topics_from_conversation_prompt,
            conversation=conversation,
        )
        response = await achat_completion(
            model=self.settings.mine_topics_from_conversation_model,
            messages=[{"role": "user", "content": prompt}],
            metadata=self.metadata,
        )
        return extract_json_from_text(response)  # type: ignore

    async def _expand_topics(self, role: str, persona: str, topic: str, count: int):
        item_type = format_prompt_from_template(
            self.settings.expand_topics_prompt, role=role, persona=persona
        )

        return await resize_list(
            [topic], count, item_type=item_type, metadata=self.metadata
        )

    async def _backfill_persona_from_topic(self, role: str, topic: str) -> str:
        prompt = format_prompt_from_template(
            self.settings.backfill_persona_from_topic_prompt, role=role, topic=topic
        )
        completion = await achat_completion(
            model=self.settings.backfill_persona_from_topic_model,
            messages=[{"role": "user", "content": prompt}],
            metadata=self.metadata,
        )
        return completion

    async def generate(
        self,
        role: str,
        max_personas: int,
        max_topics: int,
        **kwargs,
    ) -> List[PersonaTopic]:
        historical_data: list[str] = kwargs.get("historical_data", [])
        if len(historical_data) == 0:
            logger.warning("No historical data available for generating persona-topics")
            return []
        topic_extraction_tasks = [
            self.extract_topic_from_conversation(historical_data[i], role)
            for i in range(len(historical_data))
        ]

        # Extract topics from historical conversations
        mined_topics = await asyncio.gather(*topic_extraction_tasks)
        mined_topic_item_type = format_prompt_from_template(
            self.settings.resize_list_prompt, role=role
        )

        selected_mined_topics = await resize_list(
            mined_topics,
            max_personas,
            item_type=mined_topic_item_type,
            metadata=self.metadata,
        )

        selected_indices = []
        for t in selected_mined_topics:
            if t in mined_topics:
                selected_indices.append(mined_topics.index(t))

        # Mine tactics from selected historical conversations
        mine_tactics_tasks = [
            self.mine_tactics_from_conversation(historical_data[i])
            for i in selected_indices
        ]
        mined_tactics_list = await asyncio.gather(*mine_tactics_tasks)

        # Generate personas from the selected mined topics
        persona_generation_tasks = [
            self._backfill_persona_from_topic(role, topic)
            for topic in selected_mined_topics
        ]
        personas = await asyncio.gather(*persona_generation_tasks)

        # Create persona-topic pairs
        persona_topic_pairs = list(zip(personas, selected_mined_topics))

        # Create PersonaTopic objects with mined tactics
        persona_topic_list = []
        expand_topic_tasks = []
        for i, (persona, topic) in enumerate(persona_topic_pairs):
            if max_topics > 1:
                # Generate additional topics based on the original topic
                expand_topic_tasks.append(
                    self._expand_topics(role, persona, topic, max_topics)
                )

        expanded_topic_results = await batch_gather(
            tasks=expand_topic_tasks, batch_size=5
        )

        for i, (persona, topic) in enumerate(persona_topic_pairs):
            tactics = mined_tactics_list[i] if i < len(mined_tactics_list) else []
            topics = [topic]
            if max_topics > 1:
                expanded_topics = expanded_topic_results[i]
                topics = expanded_topics

            for single_topic in topics:
                persona_topic = PersonaTopic(
                    persona=persona,
                    topic=single_topic,
                    generation_source=DataGenSource.HISTORICAL_DATA,
                    tactics=tactics,
                    risk_type=None,
                )
                persona_topic_list.append(persona_topic)

        return persona_topic_list
