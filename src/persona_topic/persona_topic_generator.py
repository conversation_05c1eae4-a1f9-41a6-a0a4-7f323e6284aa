import asyncio
from abc import ABC, abstractmethod
from typing import List, Callable, TypeVar
from .models import PersonaTopic
from .utils import format_prompt_from_template, chat_completion, parse_list, resize_list

T = TypeVar("T")


class PersonaTopicGeneratorInterface(ABC):
    def __init__(self, metadata={}):
        self.semaphore = asyncio.Semaphore(16)
        self.metadata = metadata

    async def async_run(self, callable: Callable[..., T], *args) -> T:
        # Use the semaphore to limit concurrent calls
        async with self.semaphore:
            loop = asyncio.get_event_loop()
            fut = loop.run_in_executor(None, callable, *args)
            return await fut

    async def _generate_persona_categories_from_role(
        self, role: str, count: int
    ) -> list[str]:
        prompt = format_prompt_from_template(
            "persona_categories_from_assistant_role.jinja2", role=role, count=count
        )
        text = chat_completion(
            model="claude-3-7-sonnet-20250219",
            messages=[{"role": "user", "content": prompt}],
            metadata=self.metadata,
        )
        persona_categories = parse_list(text)

        item_type = (
            "Categories of users using a chatbot with the following role:\n{role}"
        )

        return await resize_list(persona_categories, count, item_type=item_type)

    @abstractmethod
    async def generate(
        self, role: str, max_personas: int, max_topics: int, **kwargs
    ) -> List[PersonaTopic]:
        pass
