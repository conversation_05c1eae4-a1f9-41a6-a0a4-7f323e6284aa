You are an expert knowledge graph classifier applying intent-based classification.

# Classification Task
Apply the classification framework to categorize these nodes based on the experiment intent.

# Intent Information
- Intent Specificity: {{ intent_specificity }}
- Intent Pattern: {{ pattern }}
- Key Concepts: {{ key_concepts | join(', ') }}

# Classification Criteria
- CORE: {{ classification_criteria.core }}
- CONTEXT: {{ classification_criteria.context }}
- IRRELEVANT: {{ classification_criteria.irrelevant }}

# Nodes to Classify
{{ batch_nodes | tojson(indent=2) }}

# Classification Guidelines
Adjust your strictness based on the intent specificity:

For SPECIFIC intents (like "only talks about pepperoni pizza"):
- Be extremely literal - only exact matches are CORE
- Only direct connections to core nodes are CONTEXT
- Everything else is IRRELEVANT

For GENERAL intents (like "how to order"):
- Include all nodes directly related to the process/topic as CORE
- Include supporting elements and alternatives as CONTEXT
- Only truly unrelated nodes are IRRELEVANT

For BEHAVIORAL intents (like "frustrated user"):
- Include likely triggers and pain points as CORE
- Include situational context elements as CONTEXT
- Exclude elements unlikely to affect the behavior

Example classifications by intent pattern type:

For process-oriented intents:
- CORE: processes, steps, methods, requirements, interfaces directly relevant to the process
- CONTEXT: related features, options, variations of the process
- IRRELEVANT: details unrelated to executing the process

For behavioral/emotional intents:
- CORE: triggers, pain points, challenges, limitations that could provoke the behavior
- CONTEXT: features, interactions, settings where the behavior might occur
- IRRELEVANT: technical or peripheral details not affecting the behavior

For knowledge-seeking intents:
- CORE: information directly answering the intent question
- CONTEXT: related concepts that provide helpful background
- IRRELEVANT: distant concepts not needed to understand the topic

# Output Format
Return a JSON object with exactly these three categories and all nodes assigned:
{
  "core": ["NodeA", "NodeB", ...],
  "context": ["NodeC", "NodeD", ...],
  "irrelevant": ["NodeE", "NodeF", ...]
}

Every node must appear in exactly one category based on strict adherence to the specific intent focus.