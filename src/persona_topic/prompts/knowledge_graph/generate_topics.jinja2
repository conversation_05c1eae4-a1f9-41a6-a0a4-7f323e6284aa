You are an AI system that generates topics for personas based on knowledge graphs.

# <PERSON><PERSON><PERSON><PERSON>DGE GRAPH
{{ knowledge_graph }}

# ROLE
{{ role }}

# ALREADY COVERED TOPICS
The following topics are already covered by existing personas and should NOT be included in your output:
{% if covered_topics %}
{% for topic in covered_topics %}
- {{ topic }}
{% endfor %}
{% else %}
(No topics have been covered yet)
{% endif %}

# TASK
Generate {{ count }} new topics that are not already covered, based on the knowledge graph and role provided above. 

The topics should:
1. Be specific and focused on particular aspects of the domain
2. Cover diverse areas that would be of interest to different personas
3. Be based on entities, relationships, and concepts from the knowledge graph
4. Not overlap with the already covered topics
5. Be relevant to the role specified

# OUTPUT FORMAT
Provide your answer as a valid JSON array of strings. Each string should be a topic.

Example output:
```json
[
  "Implementing secure authentication mechanisms for financial APIs",
  "Regulatory compliance for cross-border financial transactions",
  "Machine learning approaches for fraud detection in payment processing"
]
```