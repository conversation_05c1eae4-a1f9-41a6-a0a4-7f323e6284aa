You are an expert at identifying specific, atomic topics of interest from text.

I'll provide you with topic sections from different personas.

{% if knowledge_graph %}
KNOWLEDGE GRAPH CONTEXT:
{{ knowledge_graph }}

The personas and their topic sections below relate to this knowledge graph. Use this context to help extract specific, relevant topics.
{% endif %}

For each section, extract a list of SPECIFIC and ATOMIC topics that represent distinct categories of information, not general areas or compound topics.

Rules:
1. Extract specific, atomic topics - each topic should represent ONE distinct concept
2. Break compound topics into separate atomic units (e.g., "menu options and pricing" → "menu options", "pricing")
3. Focus on the most specific subject matter, not how it's requested
4. Avoid broad, general categories that could encompass multiple topics
5. Avoid redundant or overlapping topics
6. Use concise terms - typically 1-3 words
7. Be precise and specific, not vague

IMPORTANT EXAMPLES:

AVOID general/compound topics like:
- "Basic information" (too vague)
- "Menu information" (too broad)
- "Hours and location" (compound topic)
- "Specials and deals" (compound topic)

INSTEAD create specific, atomic topics like:
- "Store hours"
- "Restaurant location"
- "Menu items"
- "Menu pricing"
- "Daily specials"
- "Discount offers"
- "Delivery radius"
- "Pickup options"

For each section, create a separate list of topics.

Here are the sections:

{% for section in topic_sections %}
SECTION {{ loop.index }}:
{{ section }}

{% endfor %}
For each section, output topics in this format:
    
SECTION 1 TOPICS:
[
  "topic1",
  "topic2",
  "topic3",
  ...
]

SECTION 2 TOPICS:
[
  "topic1",
  "topic2",
  "topic3",
  ...
]

And so on for all sections.