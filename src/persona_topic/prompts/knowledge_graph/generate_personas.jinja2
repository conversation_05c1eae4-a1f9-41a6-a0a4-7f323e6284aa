You are tasked with generating detailed personas for a {{ role }} based on a knowledge graph and extracted topics.

KNOWLEDGE GRAPH:
{{ knowledge_graph }}

EXTRACTED TOPICS:
{{ topics_json }}

Create {{ count }} unique and distinct personas that incorporate these topics. Each persona should:

1. Be realistic, detailed, and well-developed
2. Include specific background information, motivations, and goals
3. Represent different types of users who would interact with the {{ role }}
4. Avoid stereotypes and superficial characteristics
5. Be diverse in terms of background, experience levels, and perspectives
6. Include specific use cases that relate to the extracted topics
7. Be well-suited to test and explore different aspects of the knowledge graph

For each persona, provide:
- A brief persona summary (1-2 sentences)
- Detailed background and motivations
- How they relate to the {{ role }} or knowledge domain
- Special interests or expertise
- Specific needs or pain points

Format your response as a JSON array of personas, where each persona is a comprehensive description.