You are an entity resolution expert analyzing entities from a knowledge graph.

Given the following list of entities, identify which ones refer to the SAME real-world entity.

Entities to analyze:
{{ entity_data }}

IMPORTANT RULES:
1. Only merge entities that are ACTUALLY the same thing with different names/variations
2. Do NOT merge entities that are merely related or similar in concept
3. Do NOT merge different instances of the same category 
4. Do NOT merge actions with objects they act upon
5. Do NOT merge attributes with entities they describe
6. Do NOT merge a general concept with a specific instance

Common patterns to merge:
- Abbreviations: "Inc." → "Incorporated", "Corp." → "Corporation"
- Name variations: "<PERSON>" ↔ "<PERSON><PERSON>" ↔ "<PERSON>, <PERSON>"
- Capitalization differences: "system" ↔ "System"
- With/without articles: "The Company" ↔ "Company"
- Plural/singular of the SAME entity: "User" ↔ "Users" (only if referring to the same concept)
- Typos and minor variations: "recieve" ↔ "receive"
- Common misspellings or alternate spellings

Do NOT merge:
- Parent-child relationships (e.g., "Vehicle" and "Car")
- Action-object pairs (e.g., "Download" and "File")
- Container-content pairs (e.g., "Database" and "Table")
- General-specific pairs (e.g., "Animal" and "Dog")

Return a JSON object where:
- Keys are entity names that should be merged
- Values are the canonical name to use (prefer the most complete/formal version)
- Only include entities that have duplicates
- Empty object {} if no duplicates found

Example:
{
    "inc.": "Incorporated",
    "J. Smith": "John Smith",
    "authentication": "Authentication",
    "user": "User"
}