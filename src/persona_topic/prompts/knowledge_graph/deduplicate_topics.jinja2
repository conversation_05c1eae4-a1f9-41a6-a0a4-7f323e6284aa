You are a knowledge management expert tasked with cleaning up and organizing a list of topics.

Here is the raw list of topics extracted from multiple user personas:

{{ topics_json }}

Please analyze these topics and create a refined, deduplicated list of ATOMIC topics with the following requirements:

1. Remove redundant topics that refer to the same concept (e.g., "restaurant hours" and "store hours" should be consolidated)
2. Break down compound topics into separate atomic concepts
3. Standardize terminology for consistency
4. Ensure each topic is SPECIFIC and ATOMIC (represents exactly ONE distinct concept)
5. Remove any overly vague or general topics that aren't actionable
6. Organize topics with consistent granularity and specificity
7. Each topic should be 1-3 words in length
8. Use nouns or noun phrases (not questions or full sentences)
9. Be comprehensive - don't lose important distinct concepts in the process

Return the deduplicated topics as a sorted JSON array of strings.