Extract specific, meaningful relationships between concepts that provide actionable insights for a {{ role }} support chatbot. Focus on relationships that:

1. Describe specific actions and their effects (e.g. 'requires_baking_at', 'pairs_well_with', 'causes_allergy_in')
2. Capture customer preferences and constraints (e.g. 'prefers_over', 'cannot_be_combined_with')
3. Express timing and process dependencies (e.g. 'must_be_prepared_before', 'takes_longer_than')
4. Indicate business rules and policies (e.g. 'qualifies_for_discount', 'requires_advance_notice')

Avoid generic relationships like 'has', 'includes', 'contains', 'is' that don't provide specific actionable information.