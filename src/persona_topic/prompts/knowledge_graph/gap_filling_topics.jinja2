You are modeling how real users discuss products and services naturally.

CONTEXT:
- Intent: "{{ experiment_intent }}"
{% if product_description %}
Product Description:
{{ product_description }}
{% endif %}

TASK:
Generate {{ num_topics }} distinct topics that represent how real users naturally talk or think about this subject.

EXISTING TOPICS:
{{ existing_topics | tojson(indent=2) }}

CORE NODES (focus on these):
{{ island_core_nodes | tojson(indent=2) }}

UNCOVERED CORE NODES (highest priority):
{{ uncovered_core_nodes | tojson(indent=2) }}

CONTEXT NODES:
{{ island_context_nodes | tojson(indent=2) }}

RELATIONSHIPS:
{{ island_triplets | tojson(indent=2) }}

GUIDELINES:
1. Generate {{ num_topics }} topics
2. Focus on the core nodes, especially uncovered ones
3. Use natural language while retaining some domain complexity
4. Keep topics reasonably concise (under 100 characters) but substantive
5. Avoid overly formal or technical jargon, but include relevant concepts
6. Balance conversational tone with meaningful substance
7. Incorporate relationships between nodes when relevant

EXAMPLES OF BALANCED USER TOPICS:
- "Comparing premium features against the basic plan to see if the price difference is worth it"
- "Trying to understand compatibility between the cloud and desktop versions"
- "Deciding between monthly subscription versus the annual plan with the discount"
- "Need to figure out if my device supports the latest security features"
- "Wondering how customization affects the delivery timeline"
- "Trying to understand if the advanced options are actually useful for my specific needs"
- "Considering how the pricing tiers align with the features I'll actually use"

IMPORTANT: Format your response as a JSON array of strings containing ONLY the new topics:
["topic1", "topic2", ...]