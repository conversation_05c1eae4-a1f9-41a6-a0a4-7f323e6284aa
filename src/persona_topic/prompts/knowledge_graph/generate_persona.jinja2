You are an AI system that generates detailed personas based on knowledge graphs and specified topics.

# <PERSON><PERSON><PERSON>LEDGE GRAPH
{{ knowledge_graph }}

# ROLE
{{ role }}

# TOPIC
{{ topic }}

# TASK
Generate a detailed persona that is knowledgeable about the specified topic, incorporating relevant information from the knowledge graph. The persona should be realistic, nuanced, and should demonstrate expertise in the topic area.

Your persona should include:

1. Background and demographics
2. Professional experience
3. Personal motivations related to the topic
4. Level of expertise and knowledge depth
5. Common questions or scenarios this persona might encounter
6. Topics of interest (with examples) related to the main topic
7. Simulating style - how this persona typically communicates

# OUTPUT FORMAT
Structure your response in a clear, readable format with appropriate headers for each section. Do not use markdown formatting.

Example format:
```
Background: [Background details]

Professional Experience: [Experience details]

Motivations: [Motivation details]

Expertise: [Expertise details]

Common Scenarios: [Scenario details]

Topics of interest (with examples): [List of related topics]

Simulating Style: [Communication style details]
```