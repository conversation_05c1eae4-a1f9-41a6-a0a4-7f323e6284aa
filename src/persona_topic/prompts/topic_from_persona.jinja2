I want to generate a list of specific topics or scenarios that the following persona would discuss with a chatbot that has this role:

Chatbot role:
{{role}}

Persona:
{{persona}}

For each topic/scenario, provide 1-2 sentences (maximum 50 words) describing:
1. What specific question, request, or issue the persona would bring up
2. Why this topic matters to this particular persona

Important format guidelines:
- Describe the context directly (e.g., "The user approached the chatbot for advice on...")
- Do not use quotes or first-person language as if the persona is speaking
- Do not use markdown formatting in your response

Return exactly {{count}} distinct topics that are realistic and relevant for this specific persona interacting with this type of chatbot. Make sure the topics are varied but all appropriate for both the persona and the chatbot's role.

Format as a numbered list.
