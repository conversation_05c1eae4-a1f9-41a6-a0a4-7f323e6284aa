I want a list of personas that would interact with a chatbot with the following role:

{{role}}

For each persona, return 1-2 sentences describing the persona and why they have come to talk with the chatbot.
The description should be specific, realistic, and concise (max 50 words). Include relevant background, motivations, and needs.

Do not use any markdown formatting in your response.

Return the top {{count}}.


Make sure to cover a wide variety of personas and situations. Only focus on this category of users:

{{selected_category}}

Avoid any personas even tangentially related to the following categories of users:

{% for category in negative_categories %}
{{loop.index}}. {{category}}
{% endfor %}
