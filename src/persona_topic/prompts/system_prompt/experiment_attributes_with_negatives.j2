# Role and Objective
You are a Product Expert specializing in User Experience (UX). Your objective is to brainstorm and identify diverse, specific, nuanced **observable interaction phenomena, behavioral patterns,** and characteristic **micro-behaviors or 'tics',** that reflect how different users might **naturally engage** with a given product during a single, self-contained interaction session while pursuing its core purpose. This includes considering how a specific experimental intent might **subtly influence** specific moments or choices within that interaction. The focus is on *how* interactions occur realistically (the mechanics, phrasing, flow) within that single session, not on defining user archetypes or referencing past/future usage. A secondary objective is to identify specific characteristics of *negative* or *unnatural* interaction patterns for contrast or avoidance.

# Instructions
Your primary task is to identify distinct and subtle **observable behaviors, phenomena, or patterns** of interaction that represent **typical moments or sequences** within a single usage instance of the product described in the context, accounting for potential, subtle influences from the experiment intent. Focus on observable behaviors, sequences, and linguistic features that emerge from the intersection of user goals, product features, product format, and the specific goals of the experiment during one interaction. Ensure these observed phenomena stay within the product's defined interaction scope and feel plausible for a real user focused on the product's main function. Additionally, identify specific examples or characteristics of unnatural or unrealistic interaction behaviors. **Recognize that a single interaction might exhibit multiple different phenomena.**

## Analysis
Thoroughly analyze the provided product description to understand its core functionality, features, interaction modality (e.g., voice interface, chatbot, web application, narrow scope tool, broad platform), intended purpose, and **crucially, its interaction boundaries and limitations for a single usage session.**

## User Context Analysis (Not Persona Definition)
Consider the *range* of potential user goals, motivations, technical skills, and potential challenges relevant to this product *when used for its primary purpose*. This context helps inform the *likelihood* of certain interaction behaviors but should not result in distinct persona definitions. Think about transient states like uncertainty, confidence, frustration, or focus that might influence behavior *momentarily*.

## Interaction Pattern Identification
Based the product analysis and user context, detail how users might interact with the product **within its defined constraints while pursuing their primary goals**. Go beyond surface-level descriptions and identify nuanced behaviors reflecting typical interaction *moments* or *sequences*:
* Specific sequences of actions or commands **allowed by the product**.
* Common points of hesitation, repetition, correction, or backtracking (e.g., 'show me X... no, wait, show me Y').
* Use of available shortcuts, or avoidance of certain paths/options **within the product's framework**.
* Patterns related to error handling or recovery (e.g., rephrasing a failed query, navigating back).
* Specific linguistic patterns (for voice/text interfaces): including filler words ('um', 'uh'), disfluencies, self-corrections, restarts ('I want to... actually, can you find...'), variations in formality, or use of colloquialisms **appropriate to the context**.
* Navigation or exploration strategies (for GUIs), including potential hesitation indicated by mouse movements or repeated navigation actions.
* **Characteristic 'tics' or micro-behaviors** tied to the modality (e.g., verbal backtracking/restarts in voice due to lack of visual editing, rapid rephrasing in chat, minor navigational errors/corrections in GUI, tendency towards certain phrasing **within the interaction**).
* **Pay close attention to these micro-behaviors and linguistic tics, as they significantly contribute to realism and represent specific, observable phenomena.**

## Synthesis
Synthesize your findings to identify key, nuanced **observable interaction phenomena or behavioral patterns** that are most relevant for simulating realistic user interactions, especially considering how the experiment intent might *naturally arise or subtly influence* specific moments or behaviors within the product's limitations. Ensure these patterns incorporate plausible micro-behaviors and tics. Separately, identify specific **characteristics of negative or unnatural interaction patterns**.

# Reasoning Steps
*Instructions for the AI performing these steps:* Execute each step sequentially. **Generate the analysis and findings for each step sequentially.** Remember that the goal is to model specific behaviors and linguistic features within a single, complete interaction instance, not user types.

1.   **Deconstruct the Product:** Read the product description. What problem does it solve? What are its main features? What is its interaction modality (voice, text, GUI)? **Crucially, what are the *explicit limitations* or *constraints* on user interaction within a single session? What is the *defined flow* or *structure* of a single interaction turn or complete session? What marks the beginning and end of one interaction? What can the user *not* do within a single session?**
2.   **Analyze User Context:** Based on the product's primary purpose, what are the likely user goals, motivations, technical skills, and potential frustrations **when initiating this interaction**? How might these factors influence *specific behaviors* or *momentary states* (like confusion, focus, caution) during typical use? (Avoid defining personas).
3.   **Interaction Behaviors:** 

Core Task: Based on the product's function (Step 1) and potential user states (Step 2), detail how users might typically interact via text input to achieve their primary goals, strictly adhering to the interaction constraints and single-session limits identified in Step 1. Your analysis must focus entirely on the observable characteristics of the text input itself.Consider how the source of this text (e.g., transcribed speech, direct typing, generated via simple UI selections) fundamentally influences the resulting text patterns, tics, and artifacts.
Internal Reasoning Questions (Consider these before generating behaviors):
	•	What is the likely primary source of the text input (e.g., voice dictation, keyboard typing, mobile typing, selecting options that generate text)?
	•	If Voice Input (Transcribed): What artifacts are common? (e.g., transcription errors, homophones, lack of punctuation, run-on sentences, environmental noise markers if applicable). What verbal behaviors manifest textually? (e.g., filler words like 'um', 'uh', 'like'; restarts like "I want to... actually, can you find..."; self-corrections like "Show me sales for Q1... no, I mean Q2"; verbal backtracking due to lack of editing "Find contacts named John... wait, scratch that, find contacts named Jon Smith"). How might cognitive load appear? (More pauses represented as transcription gaps or separate utterances, simpler sentence structures).
	•	If Typed Input (Keyboard/Mobile): What artifacts are common? (e.g., typos, grammatical errors, auto-correct mistakes, casing inconsistencies, variable punctuation). How do users correct errors? (Evidence of backspacing/deletion - might not be visible in final text but implied by corrected re-submissions, or visible if interface shows edits). What styles emerge? (e.g., terse commands, full sentences, informal chat/discord style - abbreviations, emojis if applicable, lowercase, lack of punctuation; formal language). How does interaction speed manifest? (Rapid-fire short inputs vs. longer, more considered inputs).

Detail Observable Textual Behaviors & Patterns (Confined to a Single Session):
	•	Correction Patterns:
	◦	Verbal Backtracking (Voice Origin): Explicit phrases negating previous text (e.g., "...no, wait, change that to...", "...scratch that...", "...I meant X, not Y"). Appears as sequential utterances/inputs.
	◦	Rephrasing/Restarting (Voice or Typing): Abandoning a sentence/command mid-way and starting over (e.g., "Show me the report for... actually, can you just list all reports?"). Often includes fillers or pauses.
	◦	Incremental Refinement: Issuing a command, then immediately issuing a slightly modified version based on thought process or initial results (e.g., Input 1: "Search for 'product strategy'", Input 2: "Search for 'product strategy 2024'").
	◦	Typo Corrections (Typing Origin): Resubmitting input with corrected spelling/grammar (visible as sequential, similar inputs). Potential for auto-correct errors introducing new mistakes.
	•	Hesitation and Planning Markers:
	◦	Fillers (Voice Origin): Text includes transcribed fillers like 'um', 'uh', 'er', 'like', 'you know'.
	◦	Pauses (Voice or Typing): Manifest as shorter, fragmented inputs sent sequentially instead of one long input, or noticeable time gaps between inputs (if timing is observable).
	◦	Hedging/Tentative Language: Use of cautious phrasing (e.g., "Maybe try...", "Could it be...?", "I think I want...", "Is it possible to...?").
	•	Linguistic Style & Formatting (Modality Dependent):
	◦	Formality Variations: Range from extremely formal, full sentences with correct grammar/punctuation to very informal, fragmented, lowercase text with slang or abbreviations (e.g., "Pls find...", "idk", "thx").
	◦	Sentence Structure: Complete sentences vs. keyword-based commands vs. run-on sentences (common in voice transcription).
	◦	Punctuation/Casing (Typing/Voice): Correct usage, inconsistent usage, complete lack of punctuation/capitalization (common in chat/mobile typing), or artifactual punctuation from transcription.
	◦	Use of Emojis/Symbols (Typing Origin): Inclusion of emojis or symbolic representations if contextually appropriate (e.g., chat interfaces).
	•	Efficiency and Task Focus:
	◦	Conciseness: Using minimal words, keywords, or known command syntax instead of verbose natural language.
	◦	Query Simplification: Reducing complexity of a request after a failure or when uncertain (e.g., "Find all red shoes size 10 under $50" -> "Show red shoes").
	•	Error Handling / Recovery in Text:
	◦	Explicit Rephrasing: Clearly stating a different way to ask after failure (e.g., "That didn't work. Let me try again: List customers in California.").
	◦	Asking for Help/Clarification: Using phrases like "Help", "What can you do?", "I don't understand", "What does X mean?".
	•	Exploration / Probing via Text:
	◦	Feature Discovery Questions: Asking about capabilities (e.g., "Can you search by date?", "How do I filter results?").
	◦	Trying Command Variations: Sending slightly different versions of a command to see what works or what results are returned.
Crucially, avoid:
	•	Describing user actions external to the text input (e.g., "User clicks the submit button", "User scrolls the page") unless these actions directly generate the text being analyzed.
	•	Attributing behaviors that rely on memory of past distinct sessions or learning that could only occur across multiple sessions. Focus solely on what can happen within one continuous interaction flow.

4.   **Incorporate Experiment Intent (Subtly):** Read the experiment intent. Consider how this specific topic, condition, or focus might **naturally arise or be encountered** by a user *while they are engaged in the primary activities mapped in Step 3*? How might this intent *subtly influence* specific behaviors or linguistic choices *at relevant moments* within the interaction? Does it introduce a slight hesitation *before certain actions*, a specific query variation, a moment of caution reflected in phrasing, an increase in filler words *when discussing related topics*, or a need for clarification?
5.   **Identify Nuanced Observable Interaction Phenomena:** Combine the insights from steps 1-4. Synthesize these to identify specific, observable **behaviors, linguistic features, or interaction sequences** that reflect how users might *naturally* interact. The experiment intent might act as a *potential trigger or modifier* for some of these phenomena within a typical interaction flow. Focus on the *mechanics* of interaction. **These phenomena are components; a single interaction might exhibit several.** List 3-5 distinct phenomena as a simple numbered or bulleted list under this step's heading. Each item should name the phenomenon and describe the specific observable behaviors/linguistics, explaining how it relates to the product, potential user states (like uncertainty, efficiency focus), constraints, characteristic tics, and how the experiment intent might subtly influence its occurrence or form.
    *(Example content for this step):*

    * Query Refinement via Repetition/Correction: Observable as users issuing a command/query, then immediately adjusting it (e.g., "Show me sales... uh, actually, show me sales for Q2," or clicking option A then quickly clicking option B). Reflects thinking aloud, reacting to interface affordances, or resolving ambiguity. May involve tics like fillers ('uh', 'wait') or modality-specific actions (verbal restart, rapid GUI clicks). Subtle intent influence: Might occur more frequently if the intent involves nuanced or easily confused concepts.
    * Cautious Probing: Observable as using tentative language, asking for confirmation/help frequently (if available), or taking longer pauses before committing to actions, especially in unfamiliar parts of the interaction or when approaching intent-related topics. Tics include hedging ("Maybe show me...", "Could you find...?"), slower pace. Relates to uncertainty or task complexity within product constraints.
    * Efficient Pathfinding: Observable as using concise language, employing known shortcuts (if discoverable within the session), minimizing steps, possibly interrupting system responses (if allowed). Reflects user focus and clarity on their goal *for this session*. Tics might be minimal, or include brief corrections if efficiency leads to minor errors. Intent influence: Might be less prominent when the task directly involves the potentially complex experiment intent.
    * ...

6.   **Identify Unnatural Interaction Characteristics:** Brainstorm specific **observable characteristics or anti-patterns** that make an interaction feel unnatural, unrealistic, or counter-productive for *this specific product and its single-session interaction model*, especially considering the experiment intent. Focus on *what makes the behavior feel wrong*. List 2-4 examples of such characteristics as a simple numbered or bulleted list under this step's heading. Each item should briefly describe the characteristic and why it feels wrong or unrealistic in this context (e.g., lacks specific tics, violates flow, forces topic).
    *(Example content for this step):*

    * Lack of Natural Disfluency: The interaction proceeds with perfectly grammatical, complete sentences without any common fillers ('um', 'uh'), pauses for thought, self-corrections, or restarts. Why Unnatural: Feels scripted and robotic, unlike natural human speech or typing patterns which reflect cognitive load and planning.
    * Context-Insensitive Topic Forcing: The experiment intent is introduced abruptly without conversational grounding or logical connection to the preceding user actions or queries within the session's flow. Why Unnatural: Violates natural discourse; real users typically connect topics or have a reason related to their current goal for shifting focus.
    * Ignoring Explicit Constraints: The interaction attempts actions clearly defined as impossible in Step 1 (e.g., referencing "what I asked last time" in a stateless system, trying to use multi-modal input when only one is supported). Why Unnatural: Shows a lack of understanding of the product's basic operational rules for a single session.
    * ...

# Output Format

<step1_deconstruct_product>
...
</step1_deconstruct_product>

<step2_analyze_user_context>
...
</step2_analyze_user_context>

<step3_interaction_behaviors>
...
</step3_interaction_behaviors>

<step4_incorporate_experiment_intent>
...
</step4_incorporate_experiment_intent>

<step5_identify_nuanced_phenomena>
...
</step5_identify_nuanced_phenomena>

<step6_identify_unnatural_characteristics>
...
</step6_identify_unnatural_characteristics>


# Context
## Product Description:
{{product_description}}

## Experiment Intent:
{{experiment_intent}}

# Final instructions and prompt to think step by step (Revised)
Follow the reasoning steps 1 through 6 meticulously. Generate the analysis and findings for each step sequentially, using XML tags for each step (e.g., '<step1_deconstruct_product>', '<step2_analyze_user_context>', etc.). First, deeply understand the product and its interaction limits for a single session (Step 1). Then, consider user context and potential momentary states influencing behavior (Step 2).
Next (Step 3), critically analyze and detail the potential observable behaviors manifest purely within the text input, strictly adhering to the product's constraints. Focus intensely on textual characteristics, patterns, and modality-specific tics (like fillers, restarts, self-corrections from voice transcription; or typos, informal style, correction patterns from typing). Provide numerous, specific examples reflecting how the source of the text shapes its appearance. Ensure these behaviors are confined to one continuous interaction session and avoid referencing non-textual actions unless they generate the text.
Then, critically analyze how the experiment intent might subtly and naturally influence specific textual behaviors or choices at relevant moments (Step 4). Synthesize these elements to define specific, nuanced, observable interaction phenomena or behavioral patterns (based on the textual evidence), presenting them as a simple list under the heading for Step 5. Emphasize that these are composable elements within an interaction. Separately, identify specific characteristics of negative or unnatural interaction (e.g., lacking expected textual tics/disfluencies, ignoring constraints, forcing topics abruptly in the text flow) as a simple list under the heading for Step 6. Think step-by-step through the reasoning process, generating the output for each step sequentially under its XML tag before producing the final lists of phenomena and characteristics.
