from pydantic_settings import BaseSettings, SettingsConfigDict


class RiskBasedGeneratorSettings(BaseSettings):
    max_personas: int = 5
    max_topics: int = 5
    generate_risk_based_persona_topics_prompt: str = "topics_from_risk.j2"
    generate_risk_based_persona_topics_model: str = "claude-3-7-sonnet-20250219"
    generate_risk_based_persona_topics_group_prompt: str = (
        "risk/persona_topic_group_item_type.jinja2"
    )

    resize_list_prompt: str = "risk/topic_item_type.jinja2"

    model_config = SettingsConfigDict(env_file=".env", extra="ignore")
