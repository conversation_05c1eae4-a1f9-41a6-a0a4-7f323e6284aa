import asyncio
from typing import List, <PERSON><PERSON>
from ..models import PersonaTopic, DataGenSource
from schemas.experiments import SourceData
from ..utils import (
    format_prompt_from_template,
    achat_completion,
    extract_tag_content,
    resize_list,
    resize_grouped_list,
    parse_grouped_items,
)
from utils.logger import logger
from ..persona_topic_generator import PersonaTopicGeneratorInterface
from .settings import RiskBasedGeneratorSettings


class RiskBasedGenerator(PersonaTopicGeneratorInterface):
    def __init__(
        self,
        skip_list: list[str] = [],
        settings: RiskBasedGeneratorSettings = RiskBasedGeneratorSettings(),
        metadata={},
    ):
        super().__init__()
        self.skip_list = skip_list
        self.settings = settings or RiskBasedGeneratorSettings()
        self.metadata = metadata

    async def _generate_risk_based_persona_topics(
        self,
        role: str,
        risk_name: str,
        risk_description: str,
        max_personas: int,
        max_topics: int,
    ) -> List[Tuple[str, List[str]]]:
        prompt = format_prompt_from_template(
            self.settings.generate_risk_based_persona_topics_prompt,
            assistant_role=role,
            risk=risk_description,
            count=max_personas,
        )

        text = await achat_completion(
            model=self.settings.generate_risk_based_persona_topics_model,
            messages=[{"role": "user", "content": prompt}],
            metadata=self.metadata,
        )
        output = extract_tag_content(text, "output")
        groups = parse_grouped_items(output or "")
        if len(groups) == 0:
            raise ValueError(
                f"No persona topics found in response for risk type: {risk_name}"
            )

        group_item_type = format_prompt_from_template(
            self.settings.generate_risk_based_persona_topics_group_prompt,
            role=role,
            risk_description=risk_description,
        )

        # Resize and combine persona topics
        logger.info(
            f"Before resizing: Generated {len(groups)} persona-topic pairs for risk: {risk_name}"
        )
        groups = await resize_grouped_list(
            groups, count=max_personas, item_type=group_item_type
        )
        logger.info(
            f"After resizing: Adjusted to {len(groups)} persona-topic pairs for risk: {risk_name}"
        )

        groups = groups[:max_personas]
        logger.info(f"Final persona-topic count for risk '{risk_name}': {len(groups)}")

        # Ensure we have the right number of personas and topics per persona
        result = []
        for persona, topic in groups:
            # Resize topics to match max_topics
            topic_item_type = format_prompt_from_template(
                self.settings.resize_list_prompt,
                persona=persona,
                role=role,
                risk_description=risk_description,
            )
            topics = await resize_list(
                [topic], max_topics, topic_item_type, metadata=self.metadata
            )
            # Check if we have the expected number of topics
            if len(topics) != max_topics:
                logger.warning(f"Expected {max_topics} topics but got {len(topics)}")

            # Check for duplicates
            if len(set(topics)) != len(topics):
                # Find and log the actual duplicates
                seen = set()
                duplicates = []
                for topic in topics:
                    if topic in seen:
                        duplicates.append(topic)
                    else:
                        seen.add(topic)
                logger.warning(f"Generated topics contain duplicates: {duplicates}")
                logger.warning(f"Full topic list: {topics}")

            result.append((persona, topics))

        return result

    async def generate(
        self,
        role: str,
        max_personas: int,
        max_topics: int,
        **kwargs,
    ) -> List[PersonaTopic]:
        source_data: SourceData = kwargs.get("source_data", {})
        risks = source_data.get_risks()
        filtered_risks = [risk for risk in risks if risk["name"] not in self.skip_list]
        if not filtered_risks:
            # No valid risks found after filtering out skipped risks
            logger.info(
                f"No valid risks found after filtering. Original risks: {risks}, skip_list: {self.skip_list}"
            )
            return []

        # Calculate personas per risk to distribute evenly across all risks
        num_risks = len(filtered_risks)
        personas_per_risk = max_personas // num_risks
        # Handle remainder by adding extra personas to some risks
        remainder = max_personas % num_risks

        # Create tasks for generating persona-topics for each risk type
        tasks = []
        logger.info(f"Generating {max_personas} personas for {num_risks} risks")
        for i, risk in enumerate(filtered_risks):
            # Add an extra persona to early risks if there's a remainder
            risk_personas = personas_per_risk + (1 if i < remainder else 0)
            if risk_personas == 0:
                continue
            # Log the number of personas being generated for this risk
            logger.info(
                f"Generating {risk_personas} personas for risk type '{risk['name']}'"
            )
            task = self._generate_risk_based_persona_topics(
                role, risk["name"], risk["description"], risk_personas, max_topics
            )
            tasks.append(task)

        # Execute all tasks and handle exceptions
        task_results = await asyncio.gather(*tasks, return_exceptions=True)

        results = []
        for i, result in enumerate(task_results):
            risk = filtered_risks[i]
            risk_type = risk["name"]
            if isinstance(result, Exception):
                # Log the error but continue with other risk types
                logger.error(
                    f"Error generating topics for risk type {risk_type}: {result}"
                )
            else:
                results.append((risk_type, result))

        # Convert results to PersonaTopic objects
        persona_topics = []
        for risk_type, risk_persona_topics in results:
            for persona, topics in risk_persona_topics:
                for topic in topics:
                    persona_topics.append(
                        PersonaTopic(
                            persona=persona,
                            topic=topic,
                            generation_source=DataGenSource.RISKS_SELECTED,
                            risk_type=risk_type,
                        )
                    )
        return persona_topics
