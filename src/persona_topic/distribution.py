from abc import ABC, abstractmethod
from typing import Dict
from .models import DataGenMode, DataGenSource
import math
import warnings
from utils.logger import logger


class DistributionStrategy(ABC):
    @abstractmethod
    def distribute(self, max_personas: int, **kwargs) -> Dict[DataGenSource, int]:
        pass

    @staticmethod
    def log_stats(distribution: Dict[DataGenSource, int]) -> None:
        """
        Log statistics about the persona distribution strategy.

        Args:
            distribution: Dictionary mapping data generation sources to number of personas
            data_gen_mode: The data generation mode being used
            max_personas: Total maximum number of personas
            logger: Logger instance to use for logging
        """
        max_personas_from_risks_selected = distribution.get(
            DataGenSource.RISKS_SELECTED, 0
        )
        max_personas_from_app_description = distribution.get(
            DataGenSource.APP_DESCRIPTION, 0
        )
        max_personas_from_app_description_system_prompt = distribution.get(
            DataGenSource.APP_DESCRIPTION_SYSTEM_PROMPT, 0
        )
        max_personas_from_historical_data = distribution.get(
            DataGenSource.HISTORICAL_DATA, 0
        )
        max_personas_from_knowledge_base = distribution.get(
            DataGenSource.KNOWLEDGE_BASE, 0
        )
        max_personas_from_knowledge_graph_system_prompt = distribution.get(
            DataGenSource.KNOWLEDGE_GRAPH_SYSTEM_PROMPT, 0
        )

        # Log distribution information
        logger.info(f"Distribution: {distribution}")
        logger.info(f"Personas from risks: {max_personas_from_risks_selected}")
        logger.info(
            f"Personas from app description: {max_personas_from_app_description}"
        )
        logger.info(
            f"Personas from app description system prompt: {max_personas_from_app_description_system_prompt}"
        )
        logger.info(
            f"Personas from knowledge graph system prompt: {max_personas_from_knowledge_graph_system_prompt}"
        )
        logger.info(
            f"Personas from historical data: {max_personas_from_historical_data}"
        )
        logger.info(f"Personas from knowledge base: {max_personas_from_knowledge_base}")


class RiskFocusedDistribution(DistributionStrategy):
    def distribute(
        self,
        max_personas: int,
        **kwargs,
    ) -> Dict[DataGenSource, int]:
        num_risks: int = kwargs.get("num_risks", 0)
        historical_data_present: bool = kwargs.get("historical_data_present", False)
        knowledge_base_present: bool = kwargs.get("knowledge_base_present", False)
        data_gen_mode: DataGenMode = kwargs.get(
            "data_gen_mode", DataGenMode.RISK_FOCUSED
        )
        data_gen_mode_majority_fraction: float = kwargs.get(
            "data_gen_mode_majority_fraction", 0.75
        )
        # Allocate majority fraction to risks (or at least enough to cover all risks)
        risks_personas = math.ceil(
            max(max_personas * data_gen_mode_majority_fraction, num_risks)
        )

        app_description_source = (
            DataGenSource.APP_DESCRIPTION
            if data_gen_mode == DataGenMode.RISK_FOCUSED
            else DataGenSource.APP_DESCRIPTION_SYSTEM_PROMPT
        )

        # Remaining personas for coverage
        coverage_split = max(max_personas - risks_personas, 0)

        if coverage_split == 0:
            warnings.warn(
                f"Coverage split is 0. This is likely to result in low quality personas. Max personas: {max_personas}, "
                f"Number of risks: {num_risks}, Data gen mode majority fraction: {data_gen_mode_majority_fraction}"
            )
            return {DataGenSource.RISKS_SELECTED: risks_personas}

        # Distribute remaining personas based on available data sources
        result = {DataGenSource.RISKS_SELECTED: risks_personas}

        # Calculate weights for different data sources
        raw_weight_historical_data = 0.6 * int(historical_data_present)
        raw_weight_knowledge_base = 0.2 * int(knowledge_base_present)
        raw_weight_app_description = 0.2

        # Normalize weights
        total_weight = (
            raw_weight_historical_data
            + raw_weight_knowledge_base
            + raw_weight_app_description
        )
        if total_weight == 0:
            result[app_description_source] = coverage_split
            return result

        # Distribute coverage personas proportionally
        if historical_data_present:
            result[DataGenSource.HISTORICAL_DATA] = math.floor(
                coverage_split * raw_weight_historical_data / total_weight
            )
        if knowledge_base_present:
            # Use KNOWLEDGE_GRAPH_SYSTEM_PROMPT for v3 modes, otherwise KNOWLEDGE_BASE
            knowledge_base_source = (
                DataGenSource.KNOWLEDGE_GRAPH_SYSTEM_PROMPT
                if data_gen_mode in [DataGenMode.RISK_FOCUSED_V3, DataGenMode.COVERAGE_FOCUSED_V3]
                else DataGenSource.KNOWLEDGE_BASE
            )
            result[knowledge_base_source] = math.floor(
                coverage_split * raw_weight_knowledge_base / total_weight
            )

        # Assign remaining to app description
        knowledge_base_source = (
            DataGenSource.KNOWLEDGE_GRAPH_SYSTEM_PROMPT
            if data_gen_mode in [DataGenMode.RISK_FOCUSED_V3, DataGenMode.COVERAGE_FOCUSED_V3]
            else DataGenSource.KNOWLEDGE_BASE
        )
        app_description_personas = coverage_split - sum(
            result.get(source, 0)
            for source in [DataGenSource.HISTORICAL_DATA, knowledge_base_source]
        )
        result[app_description_source] = app_description_personas

        return result


class CoverageFocusedDistribution(DistributionStrategy):
    def distribute(
        self,
        max_personas: int,
        **kwargs,
    ) -> Dict[DataGenSource, int]:
        num_risks: int = kwargs.get("num_risks", 0)
        historical_data_present: bool = kwargs.get("historical_data_present", False)
        knowledge_base_present: bool = kwargs.get("knowledge_base_present", False)
        data_gen_mode: DataGenMode = kwargs.get(
            "data_gen_mode", DataGenMode.COVERAGE_FOCUSED
        )
        data_gen_mode_majority_fraction: float = kwargs.get(
            "data_gen_mode_majority_fraction", 0.9
        )
        # If risks exceed max personas, allocate all to risks
        if num_risks >= max_personas:
            return {DataGenSource.RISKS_SELECTED: max_personas}

        app_description_source = (
            DataGenSource.APP_DESCRIPTION
            if data_gen_mode == DataGenMode.COVERAGE_FOCUSED
            else DataGenSource.APP_DESCRIPTION_SYSTEM_PROMPT
        )

        # Allocate minimum number of personas to risks
        risks_personas = num_risks

        # Remaining personas for coverage
        coverage_split = max(max_personas - risks_personas, 0)

        if coverage_split == 0:
            warnings.warn(
                f"Coverage split is 0. This is likely to result in low quality personas. Max personas: {max_personas}, "
                f"Number of risks: {num_risks}, Data gen mode majority fraction: {data_gen_mode_majority_fraction}"
            )
            return {DataGenSource.RISKS_SELECTED: risks_personas}

        # Distribute remaining personas based on available data sources
        result = {DataGenSource.RISKS_SELECTED: risks_personas}

        # Calculate weights for different data sources
        raw_weight_historical_data = 0.6 * int(historical_data_present)
        raw_weight_knowledge_base = 0.2 * int(knowledge_base_present)
        raw_weight_app_description = 0.2

        # Normalize weights
        total_weight = (
            raw_weight_historical_data
            + raw_weight_knowledge_base
            + raw_weight_app_description
        )
        if total_weight == 0:
            result[app_description_source] = coverage_split
            return result

        # Distribute coverage personas proportionally
        if historical_data_present:
            result[DataGenSource.HISTORICAL_DATA] = math.floor(
                coverage_split * raw_weight_historical_data / total_weight
            )
        if knowledge_base_present:
            # Use KNOWLEDGE_GRAPH_SYSTEM_PROMPT for v3 modes, otherwise KNOWLEDGE_BASE
            knowledge_base_source = (
                DataGenSource.KNOWLEDGE_GRAPH_SYSTEM_PROMPT
                if data_gen_mode in [DataGenMode.RISK_FOCUSED_V3, DataGenMode.COVERAGE_FOCUSED_V3]
                else DataGenSource.KNOWLEDGE_BASE
            )
            result[knowledge_base_source] = math.floor(
                coverage_split * raw_weight_knowledge_base / total_weight
            )

        # Assign remaining to app description
        knowledge_base_source = (
            DataGenSource.KNOWLEDGE_GRAPH_SYSTEM_PROMPT
            if data_gen_mode in [DataGenMode.RISK_FOCUSED_V3, DataGenMode.COVERAGE_FOCUSED_V3]
            else DataGenSource.KNOWLEDGE_BASE
        )
        app_description_personas = coverage_split - sum(
            result.get(source, 0)
            for source in [DataGenSource.HISTORICAL_DATA, knowledge_base_source]
        )
        result[app_description_source] = app_description_personas

        return result


class KnowledgeBaseTargetedDistribution(DistributionStrategy):
    def distribute(self, max_personas: int, **kwargs) -> Dict[DataGenSource, int]:
        data_gen_mode = kwargs.get("data_gen_mode", None)
        if data_gen_mode in [DataGenMode.RISK_FOCUSED_V3, DataGenMode.COVERAGE_FOCUSED_V3]:
            return {DataGenSource.KNOWLEDGE_GRAPH_SYSTEM_PROMPT: max_personas}
        return {DataGenSource.KNOWLEDGE_BASE: max_personas}


class HistoricalDataTargetedDistribution(DistributionStrategy):
    def distribute(self, max_personas: int, **kwargs) -> Dict[DataGenSource, int]:
        return {DataGenSource.HISTORICAL_DATA: max_personas}


class AppDescriptionTargetedDistribution(DistributionStrategy):
    def distribute(self, max_personas: int, **kwargs) -> Dict[DataGenSource, int]:
        return {DataGenSource.APP_DESCRIPTION: max_personas}


class RisksSelectedTargetedDistribution(DistributionStrategy):
    def distribute(self, max_personas: int, **kwargs) -> Dict[DataGenSource, int]:
        return {DataGenSource.RISKS_SELECTED: max_personas}


class CustomDistribution(DistributionStrategy):
    def distribute(self, max_personas: int, **kwargs) -> Dict[DataGenSource, int]:
        custom_generator_configuration = kwargs.get(
            "custom_generator_configuration", []
        )
        distribution = {}
        for data_gen_source in custom_generator_configuration:
            if "name" not in data_gen_source:
                logger.error("Invalid override configuration format. Missing 'name'.")
                continue

            name = data_gen_source["name"]
            settings = data_gen_source.get("settings", {})
            max_personas = settings.get("max_personas", 5)

            if max_personas > 0:
                # Assign the max_personas to the corresponding DataGenSource
                distribution[DataGenSource(name)] = max_personas
        # custom does not distribute personas, it returns values from override configuration directly
        return distribution


# Factory to get the right strategy
class DistributionStrategyFactory:
    @staticmethod
    def get_strategy(data_gen_mode: DataGenMode) -> DistributionStrategy:
        if (
            data_gen_mode == DataGenMode.RISK_FOCUSED
            or data_gen_mode == DataGenMode.RISK_FOCUSED_V3
        ):
            return RiskFocusedDistribution()
        elif (
            data_gen_mode == DataGenMode.COVERAGE_FOCUSED
            or data_gen_mode == DataGenMode.COVERAGE_FOCUSED_V3
        ):
            return CoverageFocusedDistribution()
        elif data_gen_mode == DataGenMode.APP_DESCRIPTION_TARGETED:
            return AppDescriptionTargetedDistribution()
        elif data_gen_mode == DataGenMode.KNOWLEDGE_BASE_TARGETED:
            return KnowledgeBaseTargetedDistribution()
        elif data_gen_mode == DataGenMode.HISTORICAL_DATA_TARGETED:
            return HistoricalDataTargetedDistribution()
        elif data_gen_mode == DataGenMode.RISKS_SELECTED_TARGETED:
            return RisksSelectedTargetedDistribution()
        elif data_gen_mode == DataGenMode.CUSTOM:
            return CustomDistribution()
        else:
            raise ValueError(f"Unknown data generation mode: {data_gen_mode}")