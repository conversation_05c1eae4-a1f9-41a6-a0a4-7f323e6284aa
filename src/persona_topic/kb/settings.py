from typing import Optional
from pydantic_settings import BaseSettings, SettingsConfigDict


class KnowledgeBaseGeneratorSettings(BaseSettings):
    max_personas: Optional[int] = 5
    max_topics: Optional[int] = 5
    seed_to_topic_scenario_prompt: str = "kb/seed_topic_to_scenario.jinja2"
    seed_to_topic_scenario_model: str = "claude-3-7-sonnet-20250219"
    scenario_to_persona_prompt: str = "kb/scenario_to_persona.jinja2"
    scenario_to_persona_model: str = "claude-3-7-sonnet-20250219"
    expand_scenarios_item_type_prompt: str = "kb/context_item_type.jinja2"

    model_config = SettingsConfigDict(env_file=".env", extra="ignore")
