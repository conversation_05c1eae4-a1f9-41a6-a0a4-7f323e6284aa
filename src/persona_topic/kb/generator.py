import asyncio
from typing import List, TypeVar
from ..models import PersonaTopic, DataGenSource
from ..utils import achat_completion, resize_list, format_prompt_from_template
from ..persona_topic_generator import PersonaTopicGeneratorInterface
from ..topic_tree import Topic<PERSON>ree
from utils.logger import logger
from .settings import KnowledgeBaseGeneratorSettings

T = TypeVar("T")


class KnowledgeBaseGenerator(PersonaTopicGeneratorInterface):
    def __init__(
        self,
        settings: KnowledgeBaseGeneratorSettings = KnowledgeBaseGeneratorSettings(),
        metadata={},
    ):
        super().__init__(metadata=metadata)
        self.settings = settings or KnowledgeBaseGeneratorSettings()

    async def _seed_topic_to_scenario(self, role: str, seed_topic: str) -> str:
        """Convert a seed topic to a rich conversation scenario."""
        prompt = format_prompt_from_template(
            self.settings.seed_to_topic_scenario_prompt,
            role=role,
            seed_topic=seed_topic,
        )
        response = await achat_completion(
            model=self.settings.seed_to_topic_scenario_model,
            messages=[{"role": "user", "content": prompt}],
            metadata=self.metadata,
        )
        return response

    async def _scenario_to_persona(self, role: str, scenario: str) -> str:
        """Derive a user persona from a conversation scenario."""
        prompt = format_prompt_from_template(
            self.settings.scenario_to_persona_prompt, role=role, scenario=scenario
        )
        response = await achat_completion(
            model=self.settings.scenario_to_persona_model,
            messages=[{"role": "user", "content": prompt}],
            metadata=self.metadata,
        )
        return response

    async def _expand_scenarios(
        self,
        role: str,
        seed_topic: str,
        persona: str,
        initial_scenario: str,
        count: int,
    ) -> List[str]:
        """Expand a single scenario into multiple related scenarios for the same persona."""
        if count <= 1:
            return [initial_scenario]

        logger.debug(
            f"Expanding scenarios for persona: {persona}, initial scenario: {initial_scenario}, target count: {count}"
        )

        item_type = format_prompt_from_template(
            self.settings.expand_scenarios_item_type_prompt,
            role=role,
            seed_topic=seed_topic,
            persona=persona,
            initial_scenario=initial_scenario,
        )

        all_scenarios = await resize_list(
            items=[initial_scenario],
            count=count,
            item_type=item_type,
            metadata=self.metadata,
        )

        if len(all_scenarios) != count:
            logger.warning(
                f"Scenario count mismatch: got {len(all_scenarios)}, expected {count}"
            )

        return all_scenarios

    async def _process_seed_topic(
        self, role: str, seed_topic: str, max_topics: int
    ) -> List[PersonaTopic]:
        """Process a single seed topic to generate persona and topics."""
        # Convert seed topic to initial scenario
        initial_scenario = await self._seed_topic_to_scenario(role, seed_topic)

        # Derive persona from scenario
        persona = await self._scenario_to_persona(role, initial_scenario)

        # Expand to multiple scenarios for this persona
        scenarios = await self._expand_scenarios(
            role=role,
            seed_topic=seed_topic,
            persona=persona,
            initial_scenario=initial_scenario,
            count=max_topics,
        )

        # Create PersonaTopic objects
        return [
            PersonaTopic(
                persona=persona,
                topic=scenario,
                generation_source=DataGenSource.KNOWLEDGE_BASE,
                risk_type=None,
            )
            for scenario in scenarios
        ]

    async def generate(
        self,
        role: str,
        max_personas: int,
        max_topics: int,
        **kwargs,
    ) -> List[PersonaTopic]:
        topic_tree: TopicTree | None = kwargs.get("topic_tree")
        if not topic_tree:
            return []
        seed_topics = topic_tree.get_unique_topics()

        # Log the seed topics for development purposes
        logger.debug(f"Knowledge Base Generator - Seed Topics: {seed_topics}")

        # If we have no seed topics, we can't generate personas
        if not seed_topics:
            logger.warning("No seed topics available for knowledge base generation")
            return []

        seed_topics = await resize_list(
            seed_topics,
            max_personas,
            item_type="Conversation topics between a user and a chatbot with the following role:\n{role}",
            metadata=self.metadata,
        )

        # Log the seed topics after resizing for development purposes
        logger.debug(
            f"Knowledge Base Generator - Seed Topics after resizing: {seed_topics}"
        )

        # Process seed topics in parallel
        tasks = [
            self._process_seed_topic(role, seed_topic, max_topics)
            for seed_topic in seed_topics
        ]
        results = await asyncio.gather(*tasks)

        # Flatten the list of lists
        persona_topics = [pt for sublist in results for pt in sublist]

        return persona_topics
