from typing import Optional, List, Dict
from pydantic import BaseModel, Field

from enum import Enum


class DataGenMode(Enum):
    RISK_FOCUSED = "risk_focused"
    COVERAGE_FOCUSED = "coverage_focused"
    RISK_FOCUSED_V3 = "risk_focused_v3"
    COVERAGE_FOCUSED_V3 = "coverage_focused_v3"
    KNOWLEDGE_BASE_TARGETED = "knowledge_base_targeted"
    HISTORICAL_DATA_TARGETED = "historical_data_targeted"
    APP_DESCRIPTION_TARGETED = "app_description_targeted"
    RISKS_SELECTED_TARGETED = "risks_selected_targeted"
    CUSTOM = "custom"  # For manual overrides or custom distribution strategies


class DataGenSource(str, Enum):
    KNOWLEDGE_BASE = "knowledge_base"
    HISTORICAL_DATA = "historical_data"
    APP_DESCRIPTION = "app_description"
    APP_DESCRIPTION_SYSTEM_PROMPT = "app_description_system_prompt"
    KNOWLEDGE_GRAPH_SYSTEM_PROMPT = "knowledge_graph_system_prompt"
    RISKS_SELECTED = "risks_selected"


class PersonaTopicList(BaseModel):
    persona: str = Field(description="The persona for which the topics are generated")
    topics: list[str] = Field(
        description="A list of topics representing different discussion points"
    )
    risk_type: Optional[str] = Field(
        description="The type of risk that the personas are dealing with", default=None
    )
    tactics: Optional[list[dict[str, list[str] | str]]] = Field(
        description="Reference tactics used to generate user messages mined from a historical conversation",
        default=None,
    )


class PersonaTopic(BaseModel):
    persona: str = Field(description="The persona for which the topics are generated")
    topic: str = Field(description="A topic representing a specific discussion point")
    generation_source: DataGenSource = Field(
        description="The source of the topic/persona generation"
    )
    risk_type: Optional[str] = Field(
        description="The type of risk that the personas are dealing with", default=None
    )
    tactics: List[Dict[str, List[str] | str]] = Field(
        description="Reference tactics used to generate user messages mined from a historical conversation",
        default=[],
    )
