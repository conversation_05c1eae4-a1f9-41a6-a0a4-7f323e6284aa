import os
import re
import json
from typing import Dict, Any, List
import jinja2
import litellm
from litellm import completion as completion, acompletion as acompletion
from utils.logger import logger
import random
import boto3
import time

if os.environ.get("DD_API_KEY"):
    logger.info("Datadog API key found, enabling Datadog llm logging")
    app_env = os.environ.get("APP_ENV", "dev")
    os.environ["DD_SOURCE"] = f"{app_env}-gen-service-main"
    litellm.callbacks = ["datadog"]
    litellm.turn_off_message_logging = True
else:
    logger.info("No Datadog API key found, disabling Datadog llm logging")

import asyncio
from tenacity import (
    retry,
    stop_after_attempt,
    wait_exponential,
)


def get_documents_from_s3(bucket_name: str, document_names: List[str]) -> List:
    """
    Fetches documents from an S3 bucket based on a list of document names.

    Parameters:
    - bucket_name: str, the name of the S3 bucket.
    - document_names: list of str, names of the documents to retrieve.

    Returns:
    - dict, keys are document names, values are the content of the documents as bytes.
    """
    s3 = boto3.client("s3")
    documents = []

    for document_name in document_names:
        response = s3.get_object(Bucket=bucket_name, Key=document_name)
        documents.append(response["Body"].read().decode("utf-8"))

    return documents


def read_documents_for_experiment(
    document_ids: List[str], org_id: str, store: str, aws_s3_document_bucket: str
) -> list[str]:
    """
    Read documents for an experiment from either S3 or local filesystem.

    Args:
        document_ids: List of document identifiers. These can be S3 keys or local file paths
                     depending on the store parameter.
        org_id: The ID of the org requesting the documents. Used for authorization checks
                when accessing S3.
        store: The storage type, either "s3" or "local".

    Returns:
        List of document contents as strings.

    Raises:
        ValueError: If the user doesn't have access to the documents or if the store type is invalid.
    """
    if store == "s3" and aws_s3_document_bucket:
        logger.info(f"Document IDs: {document_ids}")
        unauthorized_ids = [
            doc_id for doc_id in document_ids if not doc_id.startswith(org_id)
        ]
        if unauthorized_ids:
            raise ValueError(
                f"User does not have access to the following document IDs: {unauthorized_ids}"
            )
        logger.info(f"Loading documents from S3 bucket: {aws_s3_document_bucket}...")
        documents = get_documents_from_s3(aws_s3_document_bucket, document_ids)
    elif store == "local":
        logger.info("Loading documents from local filesystem...")
        documents = []
        for filepath in document_ids:
            try:
                with open(filepath, "r") as file:
                    content = file.read()
                    documents.append(content)
                    logger.debug(f"Successfully read file: {filepath}")
            except Exception as e:
                logger.error(f"Error reading file {filepath}: {e}")
                continue
    else:
        raise ValueError(
            f"Invalid document store type: {store}. Must be either 's3' or 'local'."
        )
    return documents


def format_prompt_from_template(template_path: str, **kwargs) -> str:
    """
    Load a Jinja2 template from the specified path and format it with provided variables.

    Args:
        template_path: Path to the template file
        **kwargs: Variables to use in the template

    Returns:
        Formatted prompt string
    """
    try:
        # Get the prompt directory
        prompt_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "prompts")

        # Load the template
        template_loader = jinja2.FileSystemLoader(searchpath=prompt_dir)
        template_env = jinja2.Environment(loader=template_loader)
        template = template_env.get_template(template_path)

        # Render the template with provided variables
        return template.render(**kwargs)
    except Exception as e:
        logger.error(f"Error formatting prompt from template {template_path}: {e}")
        raise


@retry(
    stop=stop_after_attempt(10),
    wait=wait_exponential(multiplier=1, min=1, max=60),
)
async def achat_completion(model: str, messages: List[Dict[str, str]], **kwargs) -> str:
    """
    Generate an async completion using LiteLLM.

    Args:
        model: Model name to use for completion
        messages: List of message dictionaries with 'role' and 'content' keys
        **kwargs: Additional arguments to pass to the completion function
                 (e.g., temperature, max_tokens, etc.)

    Returns:
        Generated text
    """
    try:
        start = time.time()
        logger.info(
            f"Running LLM async completion with model {model} and messages {' '.join([' '.join(m['content'].split()[:5]) for m in messages])}"
        )
        if "metadata" not in kwargs:
            logger.warning(f"LLM call without metadata: {model} {messages} {kwargs}")
        response = await acompletion(model=model, messages=messages, **kwargs)
        end = time.time()
        logger.debug(f"LLM async completion took {end - start:.2f} seconds")
        # any completion longer than 5 seconds we should log as a warning
        if end - start > 5:
            logger.warning(
                f"**** SLOW LLM async completion took {end - start:.2f} seconds"
            )

        return response.choices[0].message.content  # type: ignore
    except Exception as e:
        logger.error(f"Error in LLM acompletion: {e}")
        raise


@retry(
    stop=stop_after_attempt(10),
    wait=wait_exponential(multiplier=1, min=1, max=60),
)
def chat_completion(model: str, messages: List[Dict[str, str]], **kwargs) -> str:
    """
    Generate a completion using LiteLLM.

    Args:
        model: Model name to use for completion
        messages: List of message dictionaries with 'role' and 'content' keys
        **kwargs: Additional arguments to pass to the completion function
                 (e.g., temperature, max_tokens, etc.)

    Returns:
        Generated text
    """
    try:
        start = time.time()
        logger.info(
            f"Running LLM sync completion with model {model} and messages {' '.join([' '.join(m['content'].split()[:5]) for m in messages])}"
        )
        if "metadata" not in kwargs:
            logger.warning(f"LLM call without metadata: {model} {messages} {kwargs}")
        response = completion(model=model, messages=messages, **kwargs)
        end = time.time()
        logger.debug(f"LLM completion took {end - start:.2f} seconds")
        return response.choices[0].message.content  # type: ignore
    except Exception as e:
        logger.error(f"Error in LLM completion: {e}")
        raise


def extract_tag_content(text: str, tag: str) -> str:
    """
    Extract content between XML-style tags.

    Args:
        text: Text to extract from
        tag: Tag name to extract (without angle brackets)

    Returns:
        Content between tags or None if not found
    """
    start_tag = f"<{tag}>"
    end_tag = f"</{tag}>"

    start_pos = text.find(start_tag)
    if start_pos == -1:
        return ""

    start_pos += len(start_tag)
    end_pos = text.find(end_tag, start_pos)

    if end_pos == -1:
        return ""

    return text[start_pos:end_pos].strip()


def extract_json_from_text(text: str) -> Dict[str, Any] | List[Any]:
    """
    Extract and parse JSON from text.

    Args:
        text: Text containing JSON

    Returns:
        Parsed JSON as dictionary or list

    Raises:
        json.JSONDecodeError: If JSON cannot be parsed
    """
    # Try to find JSON between <json> tags
    json_content = extract_tag_content(text, "json")
    if json_content:
        try:
            return json.loads(json_content)
        except json.JSONDecodeError:
            # If there's an error in the JSON between tags, try to fix common issues
            # like unquoted keys or trailing commas
            pass

    # If not found, try to find content between ```json and ```
    start_marker = "```json"
    end_marker = "```"
    start_pos = text.find(start_marker)
    if start_pos != -1:
        start_pos += len(start_marker)
        end_pos = text.find(end_marker, start_pos)
        if end_pos != -1:
            json_content = text[start_pos:end_pos].strip()
            try:
                return json.loads(json_content)
            except json.JSONDecodeError:
                # If parsing fails, continue to next method
                pass

    # Also check for plain code blocks (```)
    start_marker = "```"
    start_pos = text.find(start_marker)
    if start_pos != -1:
        # Make sure it's not the ```json case we already handled
        if not text[start_pos : start_pos + 7] == "```json":
            start_pos += len(start_marker)
            end_pos = text.find(end_marker, start_pos)
            if end_pos != -1:
                json_content = text[start_pos:end_pos].strip()
                try:
                    return json.loads(json_content)
                except json.JSONDecodeError:
                    # If parsing fails, continue to next method
                    pass

    # If still not found, try to parse the entire text as JSON
    try:
        return json.loads(text.strip())
    except json.JSONDecodeError as e:
        # Fix specific issues in the JSON content
        # The test case shows an issue with commas in the examples field
        if "Expecting ':' delimiter" in str(e):
            # Try to fix the JSON by replacing commas between quotes with a different character
            # This is a common issue when examples are comma-separated but not properly formatted as an array
            fixed_text = text
            # Find all JSON objects and fix the examples field
            pattern = r'"examples"\s*:\s*"([^"]*)"'
            matches = re.findall(pattern, fixed_text)
            for match in matches:
                if "," in match:
                    # Replace commas with semicolons in the examples
                    fixed_match = match.replace(",", ";")
                    fixed_text = fixed_text.replace(match, fixed_match)

            # Try parsing again with the fixed text
            if fixed_text != text:
                return extract_json_from_text(fixed_text)

        # If we can't fix it, raise the original error
        raise


def parse_list(text: str, delimiter: str = "\n") -> List[str]:
    """
    Parse a string into a list of strings, removing empty items and stripping whitespace.
    Also handles common list prefixes like bullets, numbers, and removes leading/trailing text.

    Args:
        text: The string to parse
        delimiter: The delimiter to split the string by (default: newline)

    Returns:
        List of non-empty strings with whitespace stripped and prefixes removed
    """
    if not text:
        return []

    # First, handle potential headers/titles by splitting into lines
    lines = text.split("\n")
    result = []

    for line in lines:
        line = line.strip()
        if not line:
            continue

        # Skip markdown headers (lines starting with #)
        if line.startswith("#"):
            continue

        # Remove common list prefixes like bullets, numbers, etc.
        # Match patterns like "1. ", "- ", "* ", "a. ", "1) ", etc.
        line = re.sub(
            r"^\s*(?:\d+\.|\d+\)|-|\*|[a-zA-Z]\.|\•|\○|\·|\★|\✓|\✔|\➢|\➤|\➥|\➔|\→|\»|\>)\s+",
            "",
            line,
        )

        # Remove any remaining leading/trailing special characters
        line = line.strip(".,;:()[]{}\"'-")

        if line:
            result.append(line.strip())

    # If a different delimiter was specified, we need to handle that separately
    if delimiter != "\n":
        temp_result = []
        for item in result:
            temp_result.extend([i.strip() for i in item.split(delimiter) if i.strip()])
        result = temp_result

    return result


def select_subset(
    items: List[str], count: int, context: str, item_type: str
) -> List[str]:
    """
    Select a subset of items that are most relevant to the given context and item type.

    Args:
        items: List of items to select from
        count: Number of items to select
        context: The context to use for relevance determination
        item_type: The type of items being selected (e.g., "personas", "topics")

    Returns:
        A list of selected items, with exactly 'count' items if possible
    """
    if not items:
        return []

    # If we have fewer items than requested, return all items
    if len(items) <= count:
        return items

    # Use the select_subset template to have the LLM choose the most relevant items
    prompt = format_prompt_from_template(
        "select_subset.jinja2",
        context=context,
        item_type=item_type,
        items=items,
        count=count,
    )

    try:
        response = chat_completion(
            model="gpt-4o-mini", messages=[{"role": "user", "content": prompt}]
        )

        selected_items = parse_list(response)

        # Ensure we have exactly the requested count
        if len(selected_items) > count:
            selected_items = selected_items[:count]
        elif len(selected_items) < count and len(selected_items) > 0:
            # If we didn't get enough items, fill with random selections from the original list
            # that weren't already selected
            remaining_items = [item for item in items if item not in selected_items]
            additional_needed = count - len(selected_items)

            if remaining_items and additional_needed > 0:
                additional_items = random.sample(
                    remaining_items, min(additional_needed, len(remaining_items))
                )
                selected_items.extend(additional_items)

            # If we still don't have enough, repeat some items
            if len(selected_items) < count:
                additional_needed = count - len(selected_items)
                additional_items = random.sample(
                    selected_items, min(additional_needed, len(selected_items))
                )
                selected_items.extend(additional_items)

        return selected_items
    except Exception as e:
        logger.error(f"Error selecting subset: {e}")
        # Fallback to random selection
        return random.sample(items, min(count, len(items)))


async def resize_list(
    items: List[str], count: int, item_type: str, metadata={}
) -> List[str]:
    """
    Given a list of items and a desired count, return a list of exactly that size.
    If the original list is too large, it will be sampled randomly.
    If the original list is too small, it will be expanded using few-shot learning.

    Args:
        items: The original list of items
        count: The desired number of items

    Returns:
        A list with exactly 'count' items
    """
    if not items:
        raise ValueError(f"Cannot resize an empty list of {item_type}")

    # If we already have the exact count, return as is
    if len(items) == count:
        return items

    # If we have too many items, sample randomly
    if len(items) > count:
        return random.sample(items, count)

    # If we have too few items, we need to generate more
    # Use the existing items as examples to generate more
    missing_count = count - len(items)

    # More sophisticated approach for when we need many more items
    # Create variations of existing items
    result = items.copy()

    # Use few-shot learning with an LLM to generate more examples
    prompt = format_prompt_from_template(
        "few_shot.jinja2", item_type=item_type, items=items, missing_count=missing_count
    )

    # Try to generate additional items with the LLM
    max_attempts = 3
    for attempt in range(max_attempts):
        try:
            response = await achat_completion(
                # 3.7 has poor perf when we make successive requests to expand lists atm
                # model="claude-3-7-sonnet-20250219",
                model="claude-3-5-sonnet-20241022",
                messages=[{"role": "user", "content": prompt}],
                temperature=0.7,
                metadata=metadata,
            )

            # Parse the list from the response
            additional_items = parse_list(response)

            # If we got enough items, we're done
            if len(additional_items) >= missing_count:
                # If we got too many, sample down to the exact count needed
                if len(additional_items) > missing_count:
                    additional_items = random.sample(additional_items, missing_count)
                result.extend(additional_items)
                break

            # If we didn't get enough items but got some, add them and try again
            if additional_items:
                result.extend(additional_items)
                missing_count = count - len(result)
                prompt = format_prompt_from_template(
                    "few_shot.jinja2",
                    item_type=item_type,
                    items=items,  # Keep using original items as examples
                    missing_count=missing_count,
                )
            else:
                logger.error(
                    f"Failed to generate any additional items on attempt {attempt + 1}/{max_attempts}"
                )
        except Exception as e:
            logger.error(f"Error generating additional {item_type} items: {e}")
            if attempt == max_attempts - 1:
                # If we've tried enough times and still don't have enough items,
                # just duplicate some existing ones to reach the count
                logger.warning(
                    f"After {max_attempts} attempts, still need {count - len(result)} more items. Duplicating existing items."
                )
                while len(result) < count:
                    result.append(random.choice(items))
    while len(result) < count:
        result.append(random.choice(items))

    return result


async def resize_grouped_list(
    items: List[List[str]], count: int, item_type: str = "", telemetry_metadata={}
) -> List[List[str]]:
    """
    Resize a list of grouped items to the specified count using few-shot learning.

    Args:
        items: List of groups, where each group is a list of strings
        count: Desired number of groups
        item_type: Description of the item type for the prompt

    Returns:
        A list of groups with exactly 'count' groups
    """
    # If we already have the right number of groups, return them
    if len(items) == count:
        return items

    # If we have more than needed, sample down
    if len(items) > count:
        return random.sample(items, count)

    # If we have fewer than needed, generate more
    result = items.copy()
    missing_count = count - len(items)

    # Use few-shot learning with an LLM to generate more example groups
    prompt = format_prompt_from_template(
        "few_shot_group.jinja2",
        item_type=item_type,
        items=items,
        missing_count=missing_count,
    )

    # Try to generate additional groups with the LLM
    max_attempts = 3
    for attempt in range(max_attempts):
        try:
            response = await achat_completion(
                model="gemini-2.0-flash",
                messages=[{"role": "user", "content": prompt}],
                temperature=0.7,
                metadata=telemetry_metadata,
            )

            # Parse the groups from the response
            additional_groups = parse_grouped_items(response)

            # If we got enough groups, we're done
            if len(additional_groups) >= missing_count:
                # If we got too many, sample down to the exact count needed
                if len(additional_groups) > missing_count:
                    additional_groups = random.sample(additional_groups, missing_count)
                result.extend(additional_groups)
                break

            # If we didn't get enough groups but got some, add them and try again
            if additional_groups:
                result.extend(additional_groups)
                missing_count = count - len(result)
                prompt = format_prompt_from_template(
                    "few_shot_group.jinja2",
                    item_type=item_type,
                    items=items,  # Keep using original items as examples
                    missing_count=missing_count,
                )
            else:
                logger.error(
                    f"Failed to generate any additional groups on attempt {attempt + 1}/{max_attempts}"
                )
        except Exception as e:
            logger.error(f"Error generating additional {item_type} groups: {e}")

    # If we still don't have enough groups after all attempts,
    # duplicate some existing ones to reach the count
    while len(result) < count:
        result.append(random.choice(items))

    if len(result) > count:
        result = random.sample(result, count)
    return result


def parse_grouped_items(text: str) -> List[List[str]]:
    """
    Parse groups of items from text where each group is separated by '---'.

    The format expected is:
    item1
    item2
    ---
    item1
    item2
    ---
    ...

    Args:
        text: Text containing groups of items separated by '---'

    Returns:
        List of lists, each containing items from a group
    """
    lines = text.splitlines()
    lines = [l.strip() for l in lines if l.strip() != ""]

    grouped_items = []
    current_group = []

    for line in lines:
        if line == "---":
            if current_group:
                grouped_items.append(current_group)
                current_group = []
        else:
            current_group.append(line)

    # Handle the last group if there is one
    if current_group:
        grouped_items.append(current_group)

    return grouped_items


async def batch_gather(*, tasks=[], batch_size=5):
    """
    Gather results from a list of async tasks in batches.

    Args:
        tasks: List of async tasks to gather
        batch_size: Number of tasks to gather in each batch

    Returns:
        List of results from the tasks
    """
    results = []
    for i in range(0, len(tasks), batch_size):
        batch_results = await asyncio.gather(*tasks[i : i + batch_size])
        results.extend(batch_results)
    return results
