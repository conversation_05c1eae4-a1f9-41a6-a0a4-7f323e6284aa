import asyncio
from typing import List
from ..models import PersonaTopic, DataGenSource
from ..utils import (
    format_prompt_from_template,
    parse_list,
    resize_list,
    achat_completion,
    batch_gather,
    extract_tag_content,
)
from ..persona_topic_generator import PersonaTopicGeneratorInterface
from ..constants import GEMINI_2_5_FLASH_PREVIEW_04_17
from .settings import (
    AppDescriptionGeneratorSettings,
    AppDescriptionSystemPromptGeneratorSettings,
)
from dataclasses import dataclass


@dataclass
class ExperimentAttributes:
    product_description: str
    experiment_intent: str
    product_breakdown: str
    user_context: str
    interaction_styles: str
    experiment_intent_incorporation: str
    good_patterns: str
    bad_patterns: str

    @classmethod
    def from_string(
        cls, product_description: str, experiment_intent: str, text: str
    ) -> "ExperimentAttributes":
        product_description = product_description
        experiment_intent = experiment_intent
        product_breakdown = extract_tag_content(text, "step1_deconstruct_product")
        user_context = extract_tag_content(text, "step2_analyze_user_context")
        basic_interaction_behaviors = extract_tag_content(
            text, "step3_interaction_behaviors"
        )
        experiment_intent_incorporation = extract_tag_content(
            text, "step4_incorporate_experiment_intent"
        )
        good_patterns = extract_tag_content(text, "step5_identify_nuanced_phenomena")
        # interaction_patterns = [InteractionPattern(pattern_name=pattern["patternName"], description=pattern["description"], relation=pattern["relation"]) for pattern in xmltodict.parse(interaction_patterns_string)["interactionPatterns"]["interactionPattern"]]
        bad_patterns = extract_tag_content(
            text, "step6_identify_unnatural_characteristics"
        )
        return cls(
            product_description,
            experiment_intent,
            product_breakdown,
            user_context or "",
            basic_interaction_behaviors,
            experiment_intent_incorporation or "",
            good_patterns,
            bad_patterns,
        )


class AppDescriptionGenerator(PersonaTopicGeneratorInterface):
    def __init__(
        self,
        settings: AppDescriptionGeneratorSettings = AppDescriptionGeneratorSettings(),
        metadata={},
    ):
        super().__init__(metadata=metadata)
        self.settings = settings or AppDescriptionGeneratorSettings()

    async def _generate_personas_from_persona_categories(
        self, role: str, selected_category, negative_categories: list[str], count: int
    ):
        prompt = format_prompt_from_template(
            self.settings.generate_personas_from_categories_prompt,
            role=role,
            selected_category=selected_category,
            negative_categories=negative_categories,
            count=count,
        )

        text = await achat_completion(
            model=self.settings.generate_personas_from_categories_model,
            messages=[{"role": "user", "content": prompt}],
            metadata=self.metadata,
        )

        personas = parse_list(text)

        item_type = format_prompt_from_template(
            self.settings.generate_personas_from_categories_resize_prompt,
            selected_category=selected_category,
            negative_categories=negative_categories,
        )

        return await resize_list(
            personas, count, item_type=item_type, metadata=self.metadata
        )

    async def _generate_topics_from_persona(
        self, role: str, persona: str, count: int
    ) -> list[str]:
        prompt = format_prompt_from_template(
            self.settings.topics_from_persona_prompt,
            role=role,
            persona=persona,
            count=count,
        )

        text = await achat_completion(
            model=self.settings.topics_from_persona_model,
            messages=[{"role": "user", "content": prompt}],
            metadata=self.metadata,
        )

        topics = parse_list(text)

        item_type = format_prompt_from_template(
            self.settings.topics_from_persona_resize_prompt, persona=persona
        )

        return await resize_list(
            topics, count, item_type=item_type, metadata=self.metadata
        )

    async def generate(
        self,
        role: str,
        max_personas: int,
        max_topics: int,
        **kwargs,
    ) -> List[PersonaTopic]:
        persona_categories = await self._generate_persona_categories_from_role(
            role=role, count=max_personas
        )

        print(
            f"generating based off of {len(persona_categories)} categories: {persona_categories}"
        )
        # Create tasks for generating personas from each category
        tasks = []

        # For each category, all other categories are considered negative categories
        for selected_category in persona_categories:
            negative_categories = [
                cat for cat in persona_categories if cat != selected_category
            ]

            # Create a task to generate personas for this category
            task = self._generate_personas_from_persona_categories(
                role=role,
                selected_category=selected_category,
                negative_categories=negative_categories,
                count=1,
            )
            tasks.append(task)

        # Execute all tasks concurrently
        personas_list = await batch_gather(tasks=tasks, batch_size=2)

        assert len(personas_list) == max_personas, (
            f"Expected {max_personas} personas but got {len(personas_list)}"
        )

        # Flatten the results into a single list of personas
        all_personas = []
        for personas in personas_list:
            all_personas.extend(personas)

        # Generate topics for each persona concurrently
        topic_tasks = []
        for persona in all_personas:
            task = self._generate_topics_from_persona(role, persona, max_topics)
            topic_tasks.append(task)

        # Execute all topic generation tasks concurrently
        topics_list = await asyncio.gather(*topic_tasks)

        # Create PersonaTopic objects from the results
        persona_topics = []
        for persona, topics in zip(all_personas, topics_list):
            for topic in topics:
                persona_topics.append(
                    PersonaTopic(
                        persona=persona,
                        topic=topic,
                        generation_source=DataGenSource.APP_DESCRIPTION,
                    )
                )

        return persona_topics


class AppDescriptionSystemPromptGenerator(PersonaTopicGeneratorInterface):
    def __init__(
        self, settings: AppDescriptionSystemPromptGeneratorSettings, metadata={}
    ):
        super().__init__(metadata=metadata)
        self.settings = settings or AppDescriptionSystemPromptGeneratorSettings()

    async def _generate_user_message_primer(
        self,
        product_description: str,
        product_breakdown: str,
    ) -> str:
        prompt = format_prompt_from_template(
            "system_prompt/user_message_primer.j2",
            product_description=product_description,
            product_breakdown=product_breakdown,
        )
        return await achat_completion(
            GEMINI_2_5_FLASH_PREVIEW_04_17,
            [{"role": "user", "content": prompt}],
            metadata=self.metadata,
        )

    async def _generate_experiment_attributes(
        self, product_description: str, experiment_intent: str
    ) -> ExperimentAttributes:
        prompt = format_prompt_from_template(
            "system_prompt/experiment_attributes_with_negatives.j2",
            product_description=product_description,
            experiment_intent=experiment_intent,
        )
        experiment_attributes_string = await achat_completion(
            # GEMINI_2_5_PRO_PREVIEW_03_25, [{"role": "user", "content": prompt}] # REVERT THIS
            GEMINI_2_5_FLASH_PREVIEW_04_17,
            [{"role": "user", "content": prompt}],
            metadata=self.metadata,
        )
        return ExperimentAttributes.from_string(
            product_description, experiment_intent, experiment_attributes_string
        )

    async def _generate_persona(
        self,
        product_description: str,
        experiment_intent: str,
        good_patterns: str,
        bad_patterns: str,
        user_context: str,
    ) -> str:
        prompt = format_prompt_from_template(
            "system_prompt/persona.j2",
            product_description=product_description,
            experiment_intent=experiment_intent,
            good_patterns=good_patterns,
            bad_patterns=bad_patterns,
            user_context=user_context,
        )
        # return await achat_completion(GEMINI_2_5_PRO_PREVIEW_03_25, [{"role": "user", "content": prompt}]) # REVERT THIS
        return await achat_completion(
            GEMINI_2_5_FLASH_PREVIEW_04_17,
            [{"role": "user", "content": prompt}],
            metadata=self.metadata,
        )

    async def generate_one_persona(
        self,
        product_description: str,
        experiment_intent: str,
        user_message_primer: str,
        # experiment_attributes: ExperimentAttributes,
    ) -> PersonaTopic:
        experiment_attributes = await self._generate_experiment_attributes(
            product_description, experiment_intent
        )
        persona_text = await self._generate_persona(
            product_description,
            experiment_intent,
            experiment_attributes.good_patterns,
            experiment_attributes.bad_patterns,
            experiment_attributes.user_context,
        )
        # postprocess, remove markdown formatting
        persona_text = (
            persona_text.replace("```markdown", "")
            .replace("```", "")
            .replace("**", "")
            .replace("*", "")
            .strip()
        )
        # remove leading # on each line
        persona_text = "\n".join(
            [line.lstrip("#") for line in persona_text.split("\n")]
        )

        system_prompt = format_prompt_from_template(
            "system_prompt/persona_system_prompt.j2",
            persona=persona_text,
            user_message_primer=user_message_primer,
            user_interaction_style=experiment_attributes.interaction_styles,
        )
        return PersonaTopic(
            persona=system_prompt,
            topic="nothing",
            generation_source=DataGenSource.APP_DESCRIPTION_SYSTEM_PROMPT,
        )  # we put the system prompt in the persona field

    async def generate(
        self, role: str, max_personas: int, max_topics: int, **kwargs
    ) -> List[PersonaTopic]:
        experiment_intent = kwargs.get("experiment_intent", "")
        product_description = role
        # might want to put this inside of generate_one_persona
        user_message_primer = await self._generate_user_message_primer(
            product_description, experiment_intent
        )

        tasks = [
            self.generate_one_persona(
                product_description,
                experiment_intent,
                user_message_primer,
            )
            for _ in range(max_personas)
        ]
        persona_topics = await batch_gather(tasks=tasks, batch_size=15)

        return persona_topics
