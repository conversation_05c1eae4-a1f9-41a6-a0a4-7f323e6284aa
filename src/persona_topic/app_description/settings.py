from pydantic_settings import BaseSettings, SettingsConfigDict


class AppDescriptionGeneratorSettings(BaseSettings):
    max_personas: int = 5
    max_topics: int = 5
    generate_personas_from_categories_model: str = "claude-3-7-sonnet-20250219"
    generate_personas_from_categories_prompt: str = (
        "persona_from_persona_category.jinja2"
    )
    generate_personas_from_categories_resize_prompt: str = (
        "app_description/persona_item_type.jinja2"
    )
    topics_from_persona_model: str = "claude-3-7-sonnet-20250219"
    topics_from_persona_prompt: str = "topic_from_persona.jinja2"
    topics_from_persona_resize_prompt: str = "app_description/topic_item_type.jinja2"

    model_config = SettingsConfigDict(env_file=".env", extra="ignore")


class AppDescriptionSystemPromptGeneratorSettings(BaseSettings):
    max_personas: int = 5
