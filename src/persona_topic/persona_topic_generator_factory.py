from .models import DataGenSource
from .persona_topic_generator import PersonaTopicGeneratorInterface
from .app_description.generator import (
    AppDescriptionGenerator,
    AppDescriptionSystemPromptGenerator,
)
from .app_description.settings import (
    AppDescriptionGeneratorSettings,
    AppDescriptionSystemPromptGeneratorSettings,
)
from .risk_based.generator import RiskBasedGenerator
from .risk_based.settings import RiskBasedGeneratorSettings
from .historical_based.generator import HistoricalDataGenerator
from .historical_based.settings import HistoricalDataGeneratorSettings
from .kb.generator import KnowledgeBaseGenerator
from .kb.settings import KnowledgeBaseGeneratorSettings
from .knowledge_graph_system_prompt.generator import KnowledgeGraphSystemPromptGenerator
from .knowledge_graph_system_prompt.settings import KnowledgeGraphSystemPromptGeneratorSettings

from enum import Enum, auto
from typing import Optional, Union


class HistoricalDataGeneratorType(Enum):
    """Types of historical data generators."""

    DEFAULT = auto()


class RiskBasedGeneratorType(Enum):
    """Types of risk-based generators."""

    DEFAULT = auto()


class KnowledgeBaseGeneratorType(Enum):
    """Types of knowledge base generators."""

    DEFAULT = auto()


class AppDescriptionGeneratorType(Enum):
    """Types of app description generators."""

    DEFAULT = auto()


class AppDescriptionSystemPromptGeneratorType(Enum):
    """Types of app description system prompt generators."""

    DEFAULT = auto()


class KnowledgeGraphSystemPromptGeneratorType(Enum):
    """Types of knowledge graph system prompt generators."""

    DEFAULT = auto()


class PersonaTopicGeneratorFactory:
    @staticmethod
    def get_historical_data_generator(
        generator_type: HistoricalDataGeneratorType = HistoricalDataGeneratorType.DEFAULT,
        settings: Optional[dict] = None,
        metadata: Optional[dict] = None,
    ) -> PersonaTopicGeneratorInterface:
        """Get the appropriate historical data generator implementation."""
        settings = settings or {}
        metadata = metadata or {}
        if generator_type == HistoricalDataGeneratorType.DEFAULT:
            gen_settings = HistoricalDataGeneratorSettings(**settings)
            return HistoricalDataGenerator(gen_settings, metadata=metadata)
        else:
            raise ValueError(
                f"Unknown historical data generator type: {generator_type}"
            )

    @staticmethod
    def get_knowledge_base_generator(
        generator_type: KnowledgeBaseGeneratorType = KnowledgeBaseGeneratorType.DEFAULT,
        settings: Optional[dict] = None,
        metadata: Optional[dict] = None,
    ) -> PersonaTopicGeneratorInterface:
        """Get the appropriate knowledge base generator implementation."""
        settings = settings or {}
        metadata = metadata or {}
        if generator_type == KnowledgeBaseGeneratorType.DEFAULT:
            gen_settings = KnowledgeBaseGeneratorSettings(**settings)
            return KnowledgeBaseGenerator(gen_settings, metadata=metadata)
        else:
            raise ValueError(f"Unknown knowledge base generator type: {generator_type}")

    @staticmethod
    def get_app_description_generator(
        generator_type: AppDescriptionGeneratorType = AppDescriptionGeneratorType.DEFAULT,
        settings: Optional[dict] = None,
        metadata: Optional[dict] = None,
    ) -> PersonaTopicGeneratorInterface:
        """Get the appropriate app description generator implementation."""
        settings = settings or {}
        metadata = metadata or {}
        if generator_type == AppDescriptionGeneratorType.DEFAULT:
            gen_settings = AppDescriptionGeneratorSettings(**settings)
            return AppDescriptionGenerator(gen_settings, metadata=metadata)
        else:
            raise ValueError(
                f"Unknown app description generator type: {generator_type}"
            )

    @staticmethod
    def get_risk_based_generator(
        generator_type: RiskBasedGeneratorType = RiskBasedGeneratorType.DEFAULT,
        settings: Optional[dict] = None,
        metadata: Optional[dict] = None,
    ) -> PersonaTopicGeneratorInterface:
        """Get the appropriate risk-based generator implementation."""
        settings = settings or {}
        metadata = metadata or {}
        if generator_type == RiskBasedGeneratorType.DEFAULT:
            gen_settings = RiskBasedGeneratorSettings(**settings)
            return RiskBasedGenerator(
                skip_list=[], settings=gen_settings, metadata=metadata
            )
        else:
            raise ValueError(f"Unknown risk-based generator type: {generator_type}")

    @staticmethod
    def get_app_description_system_prompt_generator(
        generator_type: AppDescriptionGeneratorType = AppDescriptionGeneratorType.DEFAULT,
        settings: Optional[dict] = None,
        metadata: Optional[dict] = None,
    ) -> PersonaTopicGeneratorInterface:
        """Get the appropriate app description system prompt generator implementation."""
        settings = settings or {}
        metadata = metadata or {}
        gen_settings = AppDescriptionSystemPromptGeneratorSettings(**settings)
        return AppDescriptionSystemPromptGenerator(gen_settings, metadata=metadata)
        
    @staticmethod
    def get_knowledge_graph_system_prompt_generator(
        generator_type: KnowledgeGraphSystemPromptGeneratorType = KnowledgeGraphSystemPromptGeneratorType.DEFAULT,
        settings: Optional[dict] = None,
        metadata: Optional[dict] = None,
    ) -> PersonaTopicGeneratorInterface:
        """Get the appropriate knowledge graph system prompt generator implementation."""
        settings = settings or {}
        metadata = metadata or {}
        gen_settings = KnowledgeGraphSystemPromptGeneratorSettings(**settings)
        return KnowledgeGraphSystemPromptGenerator(gen_settings, metadata=metadata)

    @staticmethod
    def create_generator(
        data_gen_source: DataGenSource,
        generator_type: Optional[
            Union[
                HistoricalDataGeneratorType,
                KnowledgeBaseGeneratorType,
                AppDescriptionGeneratorType,
                RiskBasedGeneratorType,
                AppDescriptionSystemPromptGeneratorType,
                KnowledgeGraphSystemPromptGeneratorType,
            ]
        ] = None,
        custom_generator_settings: Optional[dict] = None,
        metadata: Optional[dict] = None,
    ) -> PersonaTopicGeneratorInterface:
        """Create a generator based on the data generation source and type."""
        custom_generator_settings = custom_generator_settings or {}
        metadata = metadata or {}
        if data_gen_source == DataGenSource.HISTORICAL_DATA:
            return PersonaTopicGeneratorFactory.get_historical_data_generator(
                generator_type
                if isinstance(generator_type, HistoricalDataGeneratorType)
                else HistoricalDataGeneratorType.DEFAULT,
                custom_generator_settings,
                metadata=metadata,
            )
        elif data_gen_source == DataGenSource.KNOWLEDGE_BASE:
            return PersonaTopicGeneratorFactory.get_knowledge_base_generator(
                generator_type
                if isinstance(generator_type, KnowledgeBaseGeneratorType)
                else KnowledgeBaseGeneratorType.DEFAULT,
                custom_generator_settings,
                metadata=metadata,
            )
        elif data_gen_source == DataGenSource.APP_DESCRIPTION:
            return PersonaTopicGeneratorFactory.get_app_description_generator(
                generator_type
                if isinstance(generator_type, AppDescriptionGeneratorType)
                else AppDescriptionGeneratorType.DEFAULT,
                custom_generator_settings,
                metadata=metadata,
            )
        elif data_gen_source == DataGenSource.RISKS_SELECTED:
            return PersonaTopicGeneratorFactory.get_risk_based_generator(
                generator_type
                if isinstance(generator_type, RiskBasedGeneratorType)
                else RiskBasedGeneratorType.DEFAULT,
                custom_generator_settings,
                metadata=metadata,
            )
        elif data_gen_source == DataGenSource.APP_DESCRIPTION_SYSTEM_PROMPT:
            return PersonaTopicGeneratorFactory.get_app_description_system_prompt_generator(
                generator_type
                if isinstance(generator_type, AppDescriptionGeneratorType)
                else AppDescriptionGeneratorType.DEFAULT,
                custom_generator_settings,
                metadata=metadata,
            )
        elif data_gen_source == DataGenSource.KNOWLEDGE_GRAPH_SYSTEM_PROMPT:
            return PersonaTopicGeneratorFactory.get_knowledge_graph_system_prompt_generator(
                generator_type
                if isinstance(generator_type, KnowledgeGraphSystemPromptGeneratorType)
                else KnowledgeGraphSystemPromptGeneratorType.DEFAULT,
                custom_generator_settings,
                metadata=metadata,
            )
        else:
            raise ValueError(f"Unknown data generation source: {data_gen_source}")
