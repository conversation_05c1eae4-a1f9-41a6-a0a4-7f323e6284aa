import httpx

BATCH_SIZE = 5


def batch(iterable, n=BATCH_SIZE):
    l = len(iterable)
    for ndx in range(0, l, n):
        yield iterable[ndx : min(ndx + n, l)]


async def patch_experiment_persona_topics(
    *,
    experiment_id,
    all_topic_branches=[],
    personas=[],
    topics=[],
    persona_topics=[],
    # persona_summaries=[],
    CONTROL_PLANE_URL,
    auth_header,
):
    topic_tree_batches = list(batch(all_topic_branches, 5))
    persona_batches = list(batch(personas, 5))
    topic_batches = list(batch(topics, 5))
    persona_topic_batches = list(batch(persona_topics, 5))
    # persona_summary_batches = list(batch(persona_summaries, 5))

    # print('persona summary batches', persona_summary_batches)
    for tree_batch in topic_tree_batches:
        async with httpx.AsyncClient() as client:
            await client.patch(
                f"{CONTROL_PLANE_URL}/api/experiments/{experiment_id}/persona-topics",
                json={
                    "topic_tree": tree_batch,
                },
                headers=auth_header,
            )

    for persona_batch in persona_batches:
        async with httpx.AsyncClient() as client:
            await client.patch(
                f"{CONTROL_PLANE_URL}/api/experiments/{experiment_id}/persona-topics",
                json={
                    "source_data": {
                        "personas": persona_batch,
                    }
                },
                headers=auth_header,
            )

    # for persona_summary_batch in persona_summary_batches:
    #     print('processing persona summaries', persona_summary_batch)
    #     async with httpx.AsyncClient() as client:
    #         await client.patch(
    #             f"{CONTROL_PLANE_URL}/api/experiments/{experiment_id}/persona-topics",
    #             json={"persona_summaries": persona_summary_batch},
    #             headers=auth_header,
    #         )

    for topic_batch in topic_batches:
        async with httpx.AsyncClient() as client:
            await client.patch(
                f"{CONTROL_PLANE_URL}/api/experiments/{experiment_id}/persona-topics",
                json={
                    "source_data": {
                        "topics": topic_batch,
                    }
                },
                headers=auth_header,
            )

    for persona_topic_batch in persona_topic_batches:
        async with httpx.AsyncClient() as client:
            await client.patch(
                f"{CONTROL_PLANE_URL}/api/experiments/{experiment_id}/persona-topics",
                json={
                    "source_data": {
                        "persona_topics": persona_topic_batch,
                    }
                },
                headers=auth_header,
            )
