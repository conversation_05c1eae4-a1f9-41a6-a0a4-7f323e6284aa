import os
import jwt
from jwt import PyJWKClient


def verify_user_token(token: str):
    auth0_domain = os.environ.get("AUTH0_ISSUER_BASE_URL")
    if not auth0_domain:
        raise ValueError("AUTH0_ISSUER_BASE_URL is not set!")

    auth0_audience = os.environ.get("AUTH0_AUDIENCE")
    if not auth0_audience:
        raise ValueError("AUTH0_AUDIENCE is not set!")

    if not token.startswith("Bearer "):
        raise ValueError("Token is not a Bearer token!")

    bearer = token.split("Bearer ")[1]

    jwkUrl = f"{auth0_domain}.well-known/jwks.json"
    optional_custom_headers = {"User-agent": "custom-user-agent"}
    jwk_client = PyJWKClient(jwkUrl, headers=optional_custom_headers)
    signing_key = jwk_client.get_signing_key_from_jwt(bearer)

    verified_token = jwt.decode(
        bearer,
        signing_key,
        algorithms=["RS256"],
        audience=auth0_audience,
        options={"verify_exp": True, "verify_signature": True},
    )

    return verified_token


def verify_api_key(api_key: str):
    jwt_signing_secret = os.environ.get("JWT_SIGNING_SECRET")
    if not jwt_signing_secret:
        raise ValueError("JWT_SIGNING_SECRET is not set!")

    verified_token = jwt.decode(
        api_key,
        jwt_signing_secret,
        algorithms=["HS256"],
        options={"verify_exp": True, "verify_signature": True},
    )

    return verified_token
