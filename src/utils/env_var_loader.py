from dataclasses import dataclass
import json
import os
from typing import List, Optional
import boto3
from utils.logger import logger


@dataclass
class SecretRef:
    arn: str
    env_key: str
    secret_key: Optional[str] = None


@dataclass
class ParamRef:
    arn: str
    env_key: str


def load_secrets_onto_env(secret_refs: List[SecretRef]):
    region = os.environ.get("AWS_REGION")
    secrets_manager = boto3.client("secretsmanager", region_name=region)

    next_token = None

    # bc python doesn't have a do-while...
    while True:
        kwargs = {"SecretIdList": [secret.arn for secret in secret_refs]}
        if next_token:
            kwargs["NextToken"] = next_token
        batch_response = secrets_manager.batch_get_secret_value(**kwargs)
        for error in batch_response.get("Errors", []):
            logger.error(
                f"Failed to fetch secret {error['SecretId']}: {error['Message']}"
            )
        for secret in batch_response["SecretValues"]:
            secret_value = secret["SecretString"]
            secret_ref = [x for x in secret_refs if x.arn == secret["ARN"]][0]
            env_key = secret_ref.env_key
            env_value = (
                secret_value
                if secret_ref.secret_key is None
                else json.loads(secret_value)[secret_ref.secret_key]
            )
            logger.dev(f"Setting env var {env_key}")
            os.environ[env_key] = env_value
        next_token = batch_response.get("NextToken")
        if not next_token:
            break


def load_parameters_onto_env(param_refs: List[ParamRef]):
    region = os.environ.get("AWS_REGION")
    parameter_store = boto3.client("ssm", region_name=region)

    # SSM has a limit of 10 parameters per request
    partitions = [
        param_refs[i * 10 : (i + 1) * 10]
        for i in range((len(param_refs) + 10 - 1) // 10)
    ]

    for partition in partitions:
        batch_response = parameter_store.get_parameters(
            Names=[param.arn for param in partition], WithDecryption=True
        )
        for param in batch_response["Parameters"]:
            param_value = param["Value"]
            env_key = [x.env_key for x in param_refs if x.arn == param["ARN"]][0]
            logger.dev(f"Setting env var: {env_key}")
            os.environ[env_key] = param_value


def load_env_vars_from_arns():
    """
    Load environment variables from ARNs set as environment variables
    """
    logger.dev("Loading environment variables from ARNs")
    all_env_vars = os.environ

    secrets: List[SecretRef] = []
    parameters: List[ParamRef] = []
    for key, value in all_env_vars.items():
        if key.startswith("SECRET_ARN_"):
            env_key = key.split("SECRET_ARN_")[1]
            # Normal ARN
            if value.count(":") == 6:
                secrets.append(SecretRef(value, env_key))
            # Dynamic Reference
            elif value.count(":") == 9:
                secret_key = value.split(":")[7]
                arn = ":".join(value.split(":")[:7])
                secrets.append(SecretRef(arn, env_key, secret_key))
            else:
                logger.error(f"Invalid secret ARN: {value}")
        elif key.startswith("PARAM_ARN_"):
            env_key = key.split("PARAM_ARN_")[1]
            parameters.append(ParamRef(value, env_key))

    if len(secrets) > 0:
        load_secrets_onto_env(secrets)

    if len(parameters) > 0:
        load_parameters_onto_env(parameters)
