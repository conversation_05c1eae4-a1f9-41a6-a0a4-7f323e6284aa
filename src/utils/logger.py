import logging
from logging import Logger as ILogger
import os
from typing import Optional


class Levels:
    DEBUG = logging.DEBUG
    DEV = 15
    INFO = logging.INFO
    WARNING = logging.WARNING
    ERROR = logging.ERROR
    CRITICAL = logging.CRITICAL


logging.addLevelName(15, "DEV")


class Logger(ILogger):
    def __init__(self, name: str = "main", level: Optional[int] = None):
        env_level = os.environ.get("LOG_LEVEL", None)
        env_level_int = 0
        if env_level:
            env_level_int = logging.getLevelNamesMapping().get(env_level.upper())
        level = level or env_level_int or Levels.DEV

        print("Initializing logger with level: ", level)

        super().__init__(name, level=level)
        self.setLevel(self.level)
        self.handler = logging.StreamHandler()
        self.handler.setLevel(level)
        self.handler.setFormatter(
            logging.Formatter("%(asctime)s - %(levelname)s - %(message)s")
        )
        self.addHandler(self.handler)

    def dev(self, message: str, *args, **kwargs):
        self.log(Levels.DEV, message, *args, **kwargs)


logger = Logger()
