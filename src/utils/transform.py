from typing import Any, Dict


def lambda_to_sqs_format(message: Dict[str, Any]) -> Dict[str, Any]:
    case_controlled_message = {}
    for key, value in message.items():
        case_controlled_key = key[0].upper() + key[1:]
        case_controlled_message[case_controlled_key] = value
    message_attrs = case_controlled_message.get("MessageAttributes")
    case_controlled_message_attrs = {}
    if message_attrs is not None:
        for attr_key, attr_value in message_attrs.items():
            case_controlled_attr_key = attr_key[0].upper() + attr_key[1:]

            case_controlled_attr_value = {}
            for child_key, child_value in attr_value.items():
                case_controlled_child_key = child_key[0].upper() + child_key[1:]
                case_controlled_attr_value[case_controlled_child_key] = child_value
            case_controlled_message_attrs[case_controlled_attr_key] = (
                case_controlled_attr_value
            )

    case_controlled_message["MessageAttributes"] = case_controlled_message_attrs

    return case_controlled_message
