import os
import sys
from dotenv import load_dotenv


if __name__ == "__main__":
    filedir = os.path.dirname(os.path.abspath(__file__))
    root_dir = f"{filedir}/src"
    pythonpath = os.environ.get("PYTHONPATH", "")
    os.environ["PYTHONPATH"] = f"{pythonpath}:{root_dir}"
    sys.path.insert(0, root_dir)

    env_path = os.path.abspath(os.path.join(filedir, "./.env"))
    load_dotenv(env_path)

    import uvicorn

    uvicorn.run(
        "src.app:app",
        host="0.0.0.0",
        port=48001,
        reload=True,  # Enable auto-reload
        reload_dirs=[root_dir],
        timeout_keep_alive=300,  # Increase keep-alive timeout to 120 seconds
        timeout_graceful_shutdown=300,  # Increase graceful shutdown timeout to 60
    )
