[project]
name = "snowglobe-generation-service"
version = "0.0.9"
description = "Snowglobe generation service."
authors = [
    {name = "Guardrails AI", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">= 3.11,<3.12"
dependencies = [
    "boto3==1.35",
    "PyJWT[crypto]==2.10.1",
    "httpx==0.27.2",
    "requests==2.32.3",
    "openai==1.51.2",
    "pydantic==2.9.2",
    "pydantic-settings==2.5.2",
    "pydantic-core==2.23.4",
    "Jinja2==3.1.4",
    "litellm==1.49.4",
    "tenacity==9.0.0",
    "networkx~=3.4.2",
    "pydot~=3.0.2",
    "tokenizers==0.20.1"
]

[project.optional-dependencies]
dev = [
    "python-dotenv>=1.0.1",
    "plumbum",
    "tqdm",
    "uvicorn==0.32.0",
    "fastapi==0.115.2",
    "pytest-asyncio==0.21.1",
    "ruff>=0.9.4",
    "coverage>=7.6.12"
]
