.PHONY: install install-dev lock lint lint-fix qa serve type gen-type-stubs build test test-cov view-test-cov test-file test-one

# Installs production dependencies
install:
	pip install -e .

# Installs development dependencies
install-dev:
	pip install -e ".[dev]"

lock:
	pip freeze > requirements-lock.txt

lint:
	ruff check .
	ruff format .

lint-fix:
	ruff check . --fix
	ruff format .

qa:
	make install-dev
	make lint

serve:
	python dev_server.py

type:
	pyright

test:
	pytest

test-cov:
	pytest --cov=src --cov-report=term-missing --cov-fail-under=73

test-kg-cov:
	pytest tests/persona_topic/knowledge_graph --cov=src/persona_topic/knowledge_graph_system_prompt --cov-report=term-missing