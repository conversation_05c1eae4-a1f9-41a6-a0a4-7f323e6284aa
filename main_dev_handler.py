import os
import sys
import asyncio
from dotenv import load_dotenv

if __name__ == "__main__":
    filedir = os.path.dirname(os.path.abspath(__file__))
    root_dir = f"{filedir}/src"
    pythonpath = os.environ.get("PYTHONPATH", "")
    os.environ["PYTHONPATH"] = f"{pythonpath}:{root_dir}"
    sys.path.insert(0, root_dir)

    env_path = os.path.abspath(os.path.join(filedir, "../build-scripts/.env"))
    load_dotenv(env_path)

    from src.main_sqs_handler import MainQueueHandler

    handler = MainQueueHandler()
    asyncio.run(handler.poll())
