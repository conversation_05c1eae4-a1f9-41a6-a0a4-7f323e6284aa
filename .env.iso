ANTHROPIC_API_KEY=''
APP_ENV=dev
AUTH0_AUDIENCE=https://dev.guardrailsai.com
AUTH0_ISSUER_BASE_URL=https://guardrailsai-dev.us.auth0.com/
AWS_S3_DOCUMENT_BUCKET=gr-threat-tester-demo-docs-bucket
BRANCHING_FACTOR=2
CONTROL_PLANE_URL=http://localhost:8080
FIREWORKS_AI_API_KEY=''
GEMINI_API_KEY=''
GEN_TESTS_QUEUE_URL=http://localhost:9324/queue/gen-service-tests
GUARDRAILS_TOKEN=''
JWT_SIGNING_SECRET=''
MAX_DEPTH=3
MAX_PERSONAS=5
MAX_TOPICS=5
OPENAI_API_KEY=''
TOGETHER_API_KEY=''
TOPIC_DOCUMENT_STORE=s3
USE_HARDCODED_TOPIC_TREE=false