#!/bin/bash

# This matters for binary dependencies
expected_pythonb_version=$(cat .python-version)
actual_python_version=$(python --version minor)
if [[ $expected_pythonb_version == *"$actual_python_version"* ]]; then
    echo "Python version mismatch. Expected: $expected_pythonb_version, Actual: $actual_python_version"
    exit 1
    else
    echo "Python version matched. Expected: $expected_pythonb_version, Actual: $actual_python_version"
fi

rm -rf ./build

mkdir -p ./build

# Install dependencies
pip install \
    -r ./prod-requirements.txt \
    --platform manylinux2014_x86_64 \
    --only-binary=:all: \
    --implementation cp \
    --no-cache-dir \
    --target ./build

pip freeze --path ./build > ./build/requirements-lock.txt

# Copy everything to build
cp -r ./src ./build
cp -r ./prod-requirements.txt ./build
cp -r ./version.txt ./build

version=$(cat ./version.txt)

# zip the build folder
cd build
zip -q -r "../gen-service-main-lambda-v$version.zip" .
# confirm the zip file was created
if [ -f "../gen-service-main-lambda-v$version.zip" ]; then
    echo "Zip file created successfully: gen-service-main-lambda-v$version.zip"
else
    echo "Failed to create zip file."
    exit 1
fi