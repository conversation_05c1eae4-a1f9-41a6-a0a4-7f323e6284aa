name: PR Quality Check

on:
  pull_request:
  push:
    branches:
      - main
  workflow_dispatch:

jobs:
  lint-and-test:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.x'

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install -r dev-requirements.txt

      - name: Run Linter
        run: |
          make lint

      - name: Run Type Checker
        run: |
          make type

      - name: Run Tests
        env:
          FIREWORKS_AI_API_KEY: ${{ secrets.FIREWORKS_AI_API_KEY }}
          GEMINI_API_KEY: ${{ secrets.GEMINI_API_KEY }}
          ANTHROPIC_API_KEY: ${{ secrets.ANTHROPIC_API_KEY }}
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
          AWS_DEFAULT_REGION: us-west-1
        run: |
          make test-cov