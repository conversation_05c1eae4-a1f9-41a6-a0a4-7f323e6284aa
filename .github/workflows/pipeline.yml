name: Deployment Pipeline
# description: "Builds and deploy Snowglobe Generation Service"

permissions:
  id-token: write
  contents: write

on:
  workflow_dispatch:
  push:
    branches:
      - main

jobs:
  call-lambda-pipeline-workflow:
    uses: guardrails-ai/threat-tester-demo/.github/workflows/zip-lambda-pipeline.yml@main
    with:
      service_name: 'gen-service-main-lambda'
      zip_name: 'gen-service-main-lambda'
    secrets: inherit
  call-ecs-pipeline-workflow:
    uses: guardrails-ai/threat-tester-demo/.github/workflows/ecs-pipeline.yml@main
    with:
      repo_name: "simlab-generation-service"
      service_name_postfix: "gen-svc"
    secrets: inherit
