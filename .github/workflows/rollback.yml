name: Rollback
# description: "Rollback an environment to a previous tag"

permissions:
  id-token: write
  contents: read

on:
  workflow_dispatch:
    inputs:
      environment:
        type: choice
        description: Select the environment to rollback.
        options:
          - dev
          - staging
          - production
        default: production
      tag:
        type: string
        description: The tag to rollback to; default is {env}-previous
        default: "{env}-previous"

jobs:
  call-ecs-rollback-workflow:
    uses: guardrails-ai/threat-tester-demo/.github/workflows/ecs-rollback.yml@gr-320-ci-updates
    with:
      repo_name: "simlab-gen-service"
      service_name_postfix: "gen-svc"
      environment: ${{ inputs.environment }}
      tag: ${{ inputs.tag }}
    secrets: inherit
