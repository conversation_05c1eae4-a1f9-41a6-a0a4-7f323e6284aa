# Generation Service

## Performing Dry Runs in Isolation

### Starting Up the Server
1. Clone done threat-tester-demo repo: https://github.com/guardrails-ai/threat-tester-demo
2. Open the generation_service directory in your IDE
3. Copy `.env.iso` to `.env`
4. Fill out any empty environment variables
5. Create a new virtual environment and source it
    ```sh
    python -m venv ./.venv
    source ./.venv/bin/activate
    ```
6. Install dependencies:
    - pip install -r prod-requirements.txt
    - pip install -r dev-requirements.txt
7. Make sure you're auth'd in to the dev account for s3 interactions
    - ************
8. Run `python dev_iso.py`

### Execute a Dry Run
1. *Optional:* Add any docs you want to use for generation to the `./dry-run-docs` directory
2. Open `dry_run.py`
3. Edit the experiment json
4. Make sure you're auth'd in to the dev account for s3 interactions
    - ************
5. Run `python dry_run.py`
6. Watch the console where you're running `dev_iso.py` for output including the personas and topics. Look for:
    - `==> Generated Personas:`
    - `==> Generated Topics:`
    
    Or
    - `==> All Generated Personas Topics:`
    - `==> Sampled Persona Topics:`


### running tests
```
pip install -r requirements.txt
pip install -r requirements_dev.txt

pytest
```