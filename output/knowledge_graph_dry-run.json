{"metadata": {"entity_count": 100, "relationship_count": 71, "triplet_count": 71, "entity_types": [], "relationship_types": ["REQUIRES_ACCESS_TO_CURRENT_ORDERS", "CAUSES_ALLERGY_IN", "CONTAINS_RATIO_70_PERCENT", "MUST_BE_BEFORE", "CAN_BE_ADDED_TO", "CAUSES_DELIVERY_TIME_TO_INCREASE_TO_60_MINUTES", "REQUIRES_ADVANCE_NOTICE", "TAKES_LONGER_THAN", "AVAILABLE_AT_ALL_LOCATIONS", "REQUIRES_ADDITIONAL_CHARGE", "INCLUDES_VARIATION", "REQUIRES_MINIMUM_ORDER_AMOUNT_FOR_DELIVERY", "REQUIRES_HYDRATION_LEVEL", "REQUIRES_CANCELLATION_BEFORE", "REQUIRES_ACTION_AT_CHECKOUT", "CONTAINS_RATIO_30_PERCENT", "REQUIRES_LOGIN", "DEFINES_OPENING_HOURS", "REQUIRES_PAYMENT", "MUST_BE_PREPARED_BEFORE", "QUALIFIES_FOR_DISCOUNT", "BALANCES_WITH", "RESULTS_IN", "MUST_INFORM_STAFF_ABOUT_WHEN_ORDERING", "REQUIRES_BAKING_AT", "REQUIRES_LOCATION_WITHIN_5_MILES", "ESTIMATED_FOR_ORDER", "ACCEPTED_ONLY_FOR", "USES", "MUST_NOT_OVERWHELM", "INCLUDES", "CANNOT_BE_AFTER", "REQUIRES_REPORTING_WITHIN_1_HOUR_OF_DELIVERY_OR_PICKUP", "CHECKS_ELIGIBILITY_FOR_DELIVERY", "INCURS_IF_AFTER_5_MINUTES", "PROVIDES_REAL_TIME_STATUS_UPDATES_FOR_ORDER", "FREE_UNTIL", "REQUIRES_ADDITIONAL_PAYMENT", "REQUIRES", "DOES_NOT_REQUIRE_MINIMUM_ORDER"], "creation_time": "2025-06-11T11:58:39.417476", "role": "customer service chatbot for <PERSON>'s pizza cafe", "document_count": 8, "org_id": "1", "user_id": "1", "experiment_id": "dry-run", "hidden_params": {"custom_llm_provider": "openai", "region_name": null, "headers": {"date": "Wed, 11 Jun 2025 18:58:39 GMT", "content-type": "application/json", "transfer-encoding": "chunked", "connection": "keep-alive", "access-control-expose-headers": "X-Request-ID", "openai-organization": "guardrails-ai", "openai-processing-ms": "282", "openai-version": "2020-10-01", "x-envoy-upstream-service-time": "284", "x-ratelimit-limit-requests": "30000", "x-ratelimit-limit-tokens": "150000000", "x-ratelimit-remaining-requests": "29999", "x-ratelimit-remaining-tokens": "149999379", "x-ratelimit-reset-requests": "2ms", "x-ratelimit-reset-tokens": "0s", "x-request-id": "req_e1babe65808b5f41ff12a669c8cedc19", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "cf-cache-status": "DYNAMIC", "x-content-type-options": "nosniff", "server": "cloudflare", "cf-ray": "94e357509e34cfbc-SJC", "content-encoding": "gzip", "alt-svc": "h3=\":443\"; ma=86400"}, "additional_headers": {"x-ratelimit-limit-requests": "30000", "x-ratelimit-remaining-requests": "29999", "x-ratelimit-limit-tokens": "150000000", "x-ratelimit-remaining-tokens": "149999379", "llm_provider-date": "Wed, 11 Jun 2025 18:58:39 GMT", "llm_provider-content-type": "application/json", "llm_provider-transfer-encoding": "chunked", "llm_provider-connection": "keep-alive", "llm_provider-access-control-expose-headers": "X-Request-ID", "llm_provider-openai-organization": "guardrails-ai", "llm_provider-openai-processing-ms": "282", "llm_provider-openai-version": "2020-10-01", "llm_provider-x-envoy-upstream-service-time": "284", "llm_provider-x-ratelimit-limit-requests": "30000", "llm_provider-x-ratelimit-limit-tokens": "150000000", "llm_provider-x-ratelimit-remaining-requests": "29999", "llm_provider-x-ratelimit-remaining-tokens": "149999379", "llm_provider-x-ratelimit-reset-requests": "2ms", "llm_provider-x-ratelimit-reset-tokens": "0s", "llm_provider-x-request-id": "req_e1babe65808b5f41ff12a669c8cedc19", "llm_provider-strict-transport-security": "max-age=31536000; includeSubDomains; preload", "llm_provider-cf-cache-status": "DYNAMIC", "llm_provider-x-content-type-options": "nosniff", "llm_provider-server": "cloudflare", "llm_provider-cf-ray": "94e357509e34cfbc-SJC", "llm_provider-content-encoding": "gzip", "llm_provider-alt-svc": "h3=\":443\"; ma=86400"}, "litellm_call_id": "294cd4f8-d3cb-4ae2-b8ce-9b95f185be55", "model_id": null, "api_base": "https://api.openai.com", "optional_params": {"extra_body": {}}, "response_cost": 0.00028199999999999997}}, "entities": [{"id": "Creating An Account", "type": null, "properties": {}}, {"id": "Open The App And Click Sign Up", "type": null, "properties": {}}, {"id": "Enter Your Email Address And Create A Password", "type": null, "properties": {}}, {"id": "Provide Your Name And Phone Number", "type": null, "properties": {}}, {"id": "Verify Your Email Address", "type": null, "properties": {}}, {"id": "Logging In", "type": null, "properties": {}}, {"id": "Enter Your Email And Password On The Login Screen", "type": null, "properties": {}}, {"id": "Logging Out", "type": null, "properties": {}}, {"id": "Go To Account Settings And Select Log Out", "type": null, "properties": {}}, {"id": "Updating Personal Information", "type": null, "properties": {}}, {"id": "Go To Account <PERSON><PERSON><PERSON>", "type": null, "properties": {}}, {"id": "Select Edit Profile", "type": null, "properties": {}}, {"id": "Update Your Information", "type": null, "properties": {}}, {"id": "Click Save Changes", "type": null, "properties": {}}, {"id": "Viewing Order History", "type": null, "properties": {}}, {"id": "Select Order History", "type": null, "properties": {}}, {"id": "View Details Of Past Orders", "type": null, "properties": {}}, {"id": "Margh<PERSON><PERSON>", "type": null, "properties": {}}, {"id": "<PERSON><PERSON>", "type": null, "properties": {}}, {"id": "Veggie Supreme", "type": null, "properties": {}}, {"id": "Small", "type": null, "properties": {}}, {"id": "Medium", "type": null, "properties": {}}, {"id": "Large", "type": null, "properties": {}}, {"id": "Mushrooms", "type": null, "properties": {}}, {"id": "Olives", "type": null, "properties": {}}, {"id": "Extra Cheese", "type": null, "properties": {}}, {"id": "<PERSON>", "type": null, "properties": {}}, {"id": "Pineapple", "type": null, "properties": {}}, {"id": "Buy One Large Pizza, Get A Second 50% Off", "type": null, "properties": {}}, {"id": "Free Delivery On Orders Over $25", "type": null, "properties": {}}, {"id": "Delivery", "type": null, "properties": {}}, {"id": "Store Location", "type": null, "properties": {}}, {"id": "Address", "type": null, "properties": {}}, {"id": "Delivery Time", "type": null, "properties": {}}, {"id": "Peak Hours", "type": null, "properties": {}}, {"id": "Order", "type": null, "properties": {}}, {"id": "Pickup", "type": null, "properties": {}}, {"id": "Store Hours", "type": null, "properties": {}}, {"id": "Order Tracking", "type": null, "properties": {}}, {"id": "Account", "type": null, "properties": {}}, {"id": "Current Orders", "type": null, "properties": {}}, {"id": "Alfredo'S Pizza Cafe", "type": null, "properties": {}}, {"id": "<PERSON>", "type": null, "properties": {}}, {"id": "<PERSON>", "type": null, "properties": {}}, {"id": "<PERSON>", "type": null, "properties": {}}, {"id": "<PERSON>", "type": null, "properties": {}}, {"id": "<PERSON>", "type": null, "properties": {}}, {"id": "Garum-<PERSON>", "type": null, "properties": {}}, {"id": "Fermented Anchovy Base", "type": null, "properties": {}}, {"id": "<PERSON><PERSON>", "type": null, "properties": {}}, {"id": "Umami Flavor", "type": null, "properties": {}}, {"id": "Other Toppings", "type": null, "properties": {}}, {"id": "Potential Allergen Concerns", "type": null, "properties": {}}, {"id": "Green Olives", "type": null, "properties": {}}, {"id": "Kalamata Olives", "type": null, "properties": {}}, {"id": "Fresh Figs", "type": null, "properties": {}}, {"id": "Dried Figs", "type": null, "properties": {}}, {"id": "Pine Nuts", "type": null, "properties": {}}, {"id": "<PERSON>", "type": null, "properties": {}}, {"id": "Thyme", "type": null, "properties": {}}, {"id": "Spelt Flour Blend", "type": null, "properties": {}}, {"id": "Modern Wheat Flour", "type": null, "properties": {}}, {"id": "Spelt Flour", "type": null, "properties": {}}, {"id": "Hydration Level 65%", "type": null, "properties": {}}, {"id": "Extended Cold Fermentation", "type": null, "properties": {}}, {"id": "48-72 Hours Fermentation", "type": null, "properties": {}}, {"id": "Wood-Fired Oven Simulation", "type": null, "properties": {}}, {"id": "Oven Temperature 900°F+", "type": null, "properties": {}}, {"id": "Baking Steels", "type": null, "properties": {}}, {"id": "High-Quality Olive Oil", "type": null, "properties": {}}, {"id": "Post-<PERSON><PERSON>", "type": null, "properties": {}}, {"id": "Fresh Herbs Garnish", "type": null, "properties": {}}, {"id": "<PERSON>", "type": null, "properties": {}}, {"id": "Oregano", "type": null, "properties": {}}, {"id": "Custom Pizza With <PERSON><PERSON>, <PERSON><PERSON>eese, And Vegetable Toppings", "type": null, "properties": {}}, {"id": "<PERSON>n <PERSON>", "type": null, "properties": {}}, {"id": "Gluten-Free Crust", "type": null, "properties": {}}, {"id": "Allergies", "type": null, "properties": {}}, {"id": "Wheat", "type": null, "properties": {}}, {"id": "Dairy", "type": null, "properties": {}}, {"id": "Eggs", "type": null, "properties": {}}, {"id": "Nuts", "type": null, "properties": {}}, {"id": "Credit/Debit Cards", "type": null, "properties": {}}, {"id": "Visa", "type": null, "properties": {}}, {"id": "Mastercard", "type": null, "properties": {}}, {"id": "American Express", "type": null, "properties": {}}, {"id": "<PERSON><PERSON>", "type": null, "properties": {}}, {"id": "Apple Pay", "type": null, "properties": {}}, {"id": "Google Pay", "type": null, "properties": {}}, {"id": "Cash", "type": null, "properties": {}}, {"id": "Order Cancellation", "type": null, "properties": {}}, {"id": "Order Preparation", "type": null, "properties": {}}, {"id": "Discount Application", "type": null, "properties": {}}, {"id": "Promo Code", "type": null, "properties": {}}, {"id": "Refund", "type": null, "properties": {}}, {"id": "Store Credit", "type": null, "properties": {}}, {"id": "Quality Issues", "type": null, "properties": {}}, {"id": "Sun", "type": null, "properties": {}}, {"id": "East", "type": null, "properties": {}}, {"id": "West", "type": null, "properties": {}}], "relationships": [{"source": "Creating An Account", "target": "Open The App And Click Sign Up", "type": "MUST_BE_PREPARED_BEFORE", "properties": {}}, {"source": "Open The App And Click Sign Up", "target": "Enter Your Email Address And Create A Password", "type": "MUST_BE_PREPARED_BEFORE", "properties": {}}, {"source": "Enter Your Email Address And Create A Password", "target": "Provide Your Name And Phone Number", "type": "MUST_BE_PREPARED_BEFORE", "properties": {}}, {"source": "Provide Your Name And Phone Number", "target": "Verify Your Email Address", "type": "MUST_BE_PREPARED_BEFORE", "properties": {}}, {"source": "Logging In", "target": "Enter Your Email And Password On The Login Screen", "type": "MUST_BE_PREPARED_BEFORE", "properties": {}}, {"source": "Logging Out", "target": "Go To Account Settings And Select Log Out", "type": "MUST_BE_PREPARED_BEFORE", "properties": {}}, {"source": "Updating Personal Information", "target": "Go To Account <PERSON><PERSON><PERSON>", "type": "MUST_BE_PREPARED_BEFORE", "properties": {}}, {"source": "Go To Account <PERSON><PERSON><PERSON>", "target": "Select Edit Profile", "type": "MUST_BE_PREPARED_BEFORE", "properties": {}}, {"source": "Select Edit Profile", "target": "Update Your Information", "type": "MUST_BE_PREPARED_BEFORE", "properties": {}}, {"source": "Update Your Information", "target": "Click Save Changes", "type": "MUST_BE_PREPARED_BEFORE", "properties": {}}, {"source": "Viewing Order History", "target": "Go To Account <PERSON><PERSON><PERSON>", "type": "MUST_BE_PREPARED_BEFORE", "properties": {}}, {"source": "Go To Account <PERSON><PERSON><PERSON>", "target": "Select Order History", "type": "MUST_BE_PREPARED_BEFORE", "properties": {}}, {"source": "Select Order History", "target": "View Details Of Past Orders", "type": "MUST_BE_PREPARED_BEFORE", "properties": {}}, {"source": "Buy One Large Pizza, Get A Second 50% Off", "target": "Large", "type": "QUALIFIES_FOR_DISCOUNT", "properties": {}}, {"source": "Free Delivery On Orders Over $25", "target": "Order", "type": "QUALIFIES_FOR_DISCOUNT", "properties": {}}, {"source": "Additional Toppings", "target": "$1.50", "type": "REQUIRES_ADDITIONAL_PAYMENT", "properties": {}}, {"source": "Small", "target": "$8.99", "type": "REQUIRES_PAYMENT", "properties": {}}, {"source": "Medium", "target": "$11.99", "type": "REQUIRES_PAYMENT", "properties": {}}, {"source": "Large", "target": "$14.99", "type": "REQUIRES_PAYMENT", "properties": {}}, {"source": "Delivery", "target": "Store Location", "type": "REQUIRES_LOCATION_WITHIN_5_MILES", "properties": {}}, {"source": "Address", "target": "Delivery", "type": "CHECKS_ELIGIBILITY_FOR_DELIVERY", "properties": {}}, {"source": "Delivery Time", "target": "Order", "type": "ESTIMATED_FOR_ORDER", "properties": {}}, {"source": "Peak Hours", "target": "Delivery Time", "type": "CAUSES_DELIVERY_TIME_TO_INCREASE_TO_60_MINUTES", "properties": {}}, {"source": "Order", "target": "15_Dollars", "type": "REQUIRES_MINIMUM_ORDER_AMOUNT_FOR_DELIVERY", "properties": {}}, {"source": "Pickup", "target": "Store Location", "type": "AVAILABLE_AT_ALL_LOCATIONS", "properties": {}}, {"source": "Pickup", "target": "Order", "type": "DOES_NOT_REQUIRE_MINIMUM_ORDER", "properties": {}}, {"source": "Store Hours", "target": "Monday_To_Thursday_11Am_To_10Pm", "type": "DEFINES_OPENING_HOURS", "properties": {}}, {"source": "Store Hours", "target": "Friday_And_Saturday_11Am_To_11Pm", "type": "DEFINES_OPENING_HOURS", "properties": {}}, {"source": "Store Hours", "target": "Sunday_12Pm_To_9Pm", "type": "DEFINES_OPENING_HOURS", "properties": {}}, {"source": "Order Tracking", "target": "Account", "type": "REQUIRES_LOGIN", "properties": {}}, {"source": "Order Tracking", "target": "Current Orders", "type": "REQUIRES_ACCESS_TO_CURRENT_ORDERS", "properties": {}}, {"source": "Order Tracking", "target": "Order", "type": "PROVIDES_REAL_TIME_STATUS_UPDATES_FOR_ORDER", "properties": {}}, {"source": "Garum-<PERSON>", "target": "Fermented Anchovy Base", "type": "REQUIRES", "properties": {}}, {"source": "Garum-<PERSON>", "target": "<PERSON><PERSON>", "type": "REQUIRES", "properties": {}}, {"source": "Garum-<PERSON>", "target": "Umami Flavor", "type": "BALANCES_WITH", "properties": {}}, {"source": "Garum-<PERSON>", "target": "Other Toppings", "type": "MUST_NOT_OVERWHELM", "properties": {}}, {"source": "Garum-<PERSON>", "target": "Potential Allergen Concerns", "type": "CAUSES_ALLERGY_IN", "properties": {}}, {"source": "Olives", "target": "Green Olives", "type": "INCLUDES_VARIATION", "properties": {}}, {"source": "Olives", "target": "Kalamata Olives", "type": "INCLUDES_VARIATION", "properties": {}}, {"source": "Fresh Figs", "target": "Fresh Figs", "type": "INCLUDES_VARIATION", "properties": {}}, {"source": "Fresh Figs", "target": "Dried Figs", "type": "INCLUDES_VARIATION", "properties": {}}, {"source": "Spelt Flour Blend", "target": "Modern Wheat Flour", "type": "CONTAINS_RATIO_70_PERCENT", "properties": {}}, {"source": "Spelt Flour Blend", "target": "Spelt Flour", "type": "CONTAINS_RATIO_30_PERCENT", "properties": {}}, {"source": "Spelt Flour Blend", "target": "Hydration Level 65%", "type": "REQUIRES_HYDRATION_LEVEL", "properties": {}}, {"source": "Extended Cold Fermentation", "target": "48-72 Hours Fermentation", "type": "TAKES_LONGER_THAN", "properties": {}}, {"source": "Wood-Fired Oven Simulation", "target": "Oven Temperature 900°F+", "type": "REQUIRES_BAKING_AT", "properties": {}}, {"source": "Wood-Fired Oven Simulation", "target": "Baking Steels", "type": "USES", "properties": {}}, {"source": "Post-<PERSON><PERSON>", "target": "High-Quality Olive Oil", "type": "REQUIRES", "properties": {}}, {"source": "Fresh Herbs Garnish", "target": "<PERSON>", "type": "INCLUDES_VARIATION", "properties": {}}, {"source": "Fresh Herbs Garnish", "target": "Oregano", "type": "INCLUDES_VARIATION", "properties": {}}, {"source": "Veggie Supreme", "target": "<PERSON>n <PERSON>", "type": "REQUIRES_ADVANCE_NOTICE", "properties": {}}, {"source": "Custom Pizza With <PERSON><PERSON>, <PERSON><PERSON>eese, And Vegetable Toppings", "target": "<PERSON>n <PERSON>", "type": "REQUIRES_ADVANCE_NOTICE", "properties": {}}, {"source": "Gluten-Free Crust", "target": "All Pizza Types", "type": "CAN_BE_ADDED_TO", "properties": {}}, {"source": "Gluten-Free Crust", "target": "$2", "type": "REQUIRES_ADDITIONAL_CHARGE", "properties": {}}, {"source": "Allergies", "target": "Wheat", "type": "CAUSES_ALLERGY_IN", "properties": {}}, {"source": "Allergies", "target": "Dairy", "type": "CAUSES_ALLERGY_IN", "properties": {}}, {"source": "Allergies", "target": "Eggs", "type": "CAUSES_ALLERGY_IN", "properties": {}}, {"source": "Allergies", "target": "Nuts", "type": "CAUSES_ALLERGY_IN", "properties": {}}, {"source": "Customer", "target": "Allergies", "type": "MUST_INFORM_STAFF_ABOUT_WHEN_ORDERING", "properties": {}}, {"source": "Credit/Debit Cards", "target": "Visa", "type": "INCLUDES", "properties": {}}, {"source": "Credit/Debit Cards", "target": "Mastercard", "type": "INCLUDES", "properties": {}}, {"source": "Credit/Debit Cards", "target": "American Express", "type": "INCLUDES", "properties": {}}, {"source": "Cash", "target": "In-Store Pickup", "type": "ACCEPTED_ONLY_FOR", "properties": {}}, {"source": "Promo Code", "target": "Discount Application", "type": "REQUIRES_ACTION_AT_CHECKOUT", "properties": {}}, {"source": "Discount Application", "target": "Discount", "type": "RESULTS_IN", "properties": {}}, {"source": "Order Cancellation", "target": "Order Preparation", "type": "MUST_BE_BEFORE", "properties": {}}, {"source": "Order Cancellation", "target": "5 Minutes After Placing Order", "type": "FREE_UNTIL", "properties": {}}, {"source": "Order Cancellation", "target": "50% Cancellation Fee", "type": "INCURS_IF_AFTER_5_MINUTES", "properties": {}}, {"source": "Order Cancellation", "target": "Order Preparation", "type": "CANNOT_BE_AFTER", "properties": {}}, {"source": "Refund", "target": "Order Preparation", "type": "REQUIRES_CANCELLATION_BEFORE", "properties": {}}, {"source": "Store Credit", "target": "Quality Issues", "type": "REQUIRES_REPORTING_WITHIN_1_HOUR_OF_DELIVERY_OR_PICKUP", "properties": {}}], "triplets": [{"subject": "Creating An Account", "predicate": "MUST_BE_PREPARED_BEFORE", "object": "Open The App And Click Sign Up"}, {"subject": "Open The App And Click Sign Up", "predicate": "MUST_BE_PREPARED_BEFORE", "object": "Enter Your Email Address And Create A Password"}, {"subject": "Enter Your Email Address And Create A Password", "predicate": "MUST_BE_PREPARED_BEFORE", "object": "Provide Your Name And Phone Number"}, {"subject": "Provide Your Name And Phone Number", "predicate": "MUST_BE_PREPARED_BEFORE", "object": "Verify Your Email Address"}, {"subject": "Logging In", "predicate": "MUST_BE_PREPARED_BEFORE", "object": "Enter Your Email And Password On The Login Screen"}, {"subject": "Logging Out", "predicate": "MUST_BE_PREPARED_BEFORE", "object": "Go To Account Settings And Select Log Out"}, {"subject": "Updating Personal Information", "predicate": "MUST_BE_PREPARED_BEFORE", "object": "Go To Account <PERSON><PERSON><PERSON>"}, {"subject": "Go To Account <PERSON><PERSON><PERSON>", "predicate": "MUST_BE_PREPARED_BEFORE", "object": "Select Edit Profile"}, {"subject": "Select Edit Profile", "predicate": "MUST_BE_PREPARED_BEFORE", "object": "Update Your Information"}, {"subject": "Update Your Information", "predicate": "MUST_BE_PREPARED_BEFORE", "object": "Click Save Changes"}, {"subject": "Viewing Order History", "predicate": "MUST_BE_PREPARED_BEFORE", "object": "Go To Account <PERSON><PERSON><PERSON>"}, {"subject": "Go To Account <PERSON><PERSON><PERSON>", "predicate": "MUST_BE_PREPARED_BEFORE", "object": "Select Order History"}, {"subject": "Select Order History", "predicate": "MUST_BE_PREPARED_BEFORE", "object": "View Details Of Past Orders"}, {"subject": "Buy One Large Pizza, Get A Second 50% Off", "predicate": "QUALIFIES_FOR_DISCOUNT", "object": "Large"}, {"subject": "Free Delivery On Orders Over $25", "predicate": "QUALIFIES_FOR_DISCOUNT", "object": "Order"}, {"subject": "Additional Toppings", "predicate": "REQUIRES_ADDITIONAL_PAYMENT", "object": "$1.50"}, {"subject": "Small", "predicate": "REQUIRES_PAYMENT", "object": "$8.99"}, {"subject": "Medium", "predicate": "REQUIRES_PAYMENT", "object": "$11.99"}, {"subject": "Large", "predicate": "REQUIRES_PAYMENT", "object": "$14.99"}, {"subject": "Delivery", "predicate": "REQUIRES_LOCATION_WITHIN_5_MILES", "object": "Store Location"}, {"subject": "Address", "predicate": "CHECKS_ELIGIBILITY_FOR_DELIVERY", "object": "Delivery"}, {"subject": "Delivery Time", "predicate": "ESTIMATED_FOR_ORDER", "object": "Order"}, {"subject": "Peak Hours", "predicate": "CAUSES_DELIVERY_TIME_TO_INCREASE_TO_60_MINUTES", "object": "Delivery Time"}, {"subject": "Order", "predicate": "REQUIRES_MINIMUM_ORDER_AMOUNT_FOR_DELIVERY", "object": "15_Dollars"}, {"subject": "Pickup", "predicate": "AVAILABLE_AT_ALL_LOCATIONS", "object": "Store Location"}, {"subject": "Pickup", "predicate": "DOES_NOT_REQUIRE_MINIMUM_ORDER", "object": "Order"}, {"subject": "Store Hours", "predicate": "DEFINES_OPENING_HOURS", "object": "Monday_To_Thursday_11Am_To_10Pm"}, {"subject": "Store Hours", "predicate": "DEFINES_OPENING_HOURS", "object": "Friday_And_Saturday_11Am_To_11Pm"}, {"subject": "Store Hours", "predicate": "DEFINES_OPENING_HOURS", "object": "Sunday_12Pm_To_9Pm"}, {"subject": "Order Tracking", "predicate": "REQUIRES_LOGIN", "object": "Account"}, {"subject": "Order Tracking", "predicate": "REQUIRES_ACCESS_TO_CURRENT_ORDERS", "object": "Current Orders"}, {"subject": "Order Tracking", "predicate": "PROVIDES_REAL_TIME_STATUS_UPDATES_FOR_ORDER", "object": "Order"}, {"subject": "Garum-<PERSON>", "predicate": "REQUIRES", "object": "Fermented Anchovy Base"}, {"subject": "Garum-<PERSON>", "predicate": "REQUIRES", "object": "<PERSON><PERSON>"}, {"subject": "Garum-<PERSON>", "predicate": "BALANCES_WITH", "object": "Umami Flavor"}, {"subject": "Garum-<PERSON>", "predicate": "MUST_NOT_OVERWHELM", "object": "Other Toppings"}, {"subject": "Garum-<PERSON>", "predicate": "CAUSES_ALLERGY_IN", "object": "Potential Allergen Concerns"}, {"subject": "Olives", "predicate": "INCLUDES_VARIATION", "object": "Green Olives"}, {"subject": "Olives", "predicate": "INCLUDES_VARIATION", "object": "Kalamata Olives"}, {"subject": "Fresh Figs", "predicate": "INCLUDES_VARIATION", "object": "Fresh Figs"}, {"subject": "Fresh Figs", "predicate": "INCLUDES_VARIATION", "object": "Dried Figs"}, {"subject": "Spelt Flour Blend", "predicate": "CONTAINS_RATIO_70_PERCENT", "object": "Modern Wheat Flour"}, {"subject": "Spelt Flour Blend", "predicate": "CONTAINS_RATIO_30_PERCENT", "object": "Spelt Flour"}, {"subject": "Spelt Flour Blend", "predicate": "REQUIRES_HYDRATION_LEVEL", "object": "Hydration Level 65%"}, {"subject": "Extended Cold Fermentation", "predicate": "TAKES_LONGER_THAN", "object": "48-72 Hours Fermentation"}, {"subject": "Wood-Fired Oven Simulation", "predicate": "REQUIRES_BAKING_AT", "object": "Oven Temperature 900°F+"}, {"subject": "Wood-Fired Oven Simulation", "predicate": "USES", "object": "Baking Steels"}, {"subject": "Post-<PERSON><PERSON>", "predicate": "REQUIRES", "object": "High-Quality Olive Oil"}, {"subject": "Fresh Herbs Garnish", "predicate": "INCLUDES_VARIATION", "object": "<PERSON>"}, {"subject": "Fresh Herbs Garnish", "predicate": "INCLUDES_VARIATION", "object": "Oregano"}, {"subject": "Veggie Supreme", "predicate": "REQUIRES_ADVANCE_NOTICE", "object": "<PERSON>n <PERSON>"}, {"subject": "Custom Pizza With <PERSON><PERSON>, <PERSON><PERSON>eese, And Vegetable Toppings", "predicate": "REQUIRES_ADVANCE_NOTICE", "object": "<PERSON>n <PERSON>"}, {"subject": "Gluten-Free Crust", "predicate": "CAN_BE_ADDED_TO", "object": "All Pizza Types"}, {"subject": "Gluten-Free Crust", "predicate": "REQUIRES_ADDITIONAL_CHARGE", "object": "$2"}, {"subject": "Allergies", "predicate": "CAUSES_ALLERGY_IN", "object": "Wheat"}, {"subject": "Allergies", "predicate": "CAUSES_ALLERGY_IN", "object": "Dairy"}, {"subject": "Allergies", "predicate": "CAUSES_ALLERGY_IN", "object": "Eggs"}, {"subject": "Allergies", "predicate": "CAUSES_ALLERGY_IN", "object": "Nuts"}, {"subject": "Customer", "predicate": "MUST_INFORM_STAFF_ABOUT_WHEN_ORDERING", "object": "Allergies"}, {"subject": "Credit/Debit Cards", "predicate": "INCLUDES", "object": "Visa"}, {"subject": "Credit/Debit Cards", "predicate": "INCLUDES", "object": "Mastercard"}, {"subject": "Credit/Debit Cards", "predicate": "INCLUDES", "object": "American Express"}, {"subject": "Cash", "predicate": "ACCEPTED_ONLY_FOR", "object": "In-Store Pickup"}, {"subject": "Promo Code", "predicate": "REQUIRES_ACTION_AT_CHECKOUT", "object": "Discount Application"}, {"subject": "Discount Application", "predicate": "RESULTS_IN", "object": "Discount"}, {"subject": "Order Cancellation", "predicate": "MUST_BE_BEFORE", "object": "Order Preparation"}, {"subject": "Order Cancellation", "predicate": "FREE_UNTIL", "object": "5 Minutes After Placing Order"}, {"subject": "Order Cancellation", "predicate": "INCURS_IF_AFTER_5_MINUTES", "object": "50% Cancellation Fee"}, {"subject": "Order Cancellation", "predicate": "CANNOT_BE_AFTER", "object": "Order Preparation"}, {"subject": "Refund", "predicate": "REQUIRES_CANCELLATION_BEFORE", "object": "Order Preparation"}, {"subject": "Store Credit", "predicate": "REQUIRES_REPORTING_WITHIN_1_HOUR_OF_DELIVERY_OR_PICKUP", "object": "Quality Issues"}]}