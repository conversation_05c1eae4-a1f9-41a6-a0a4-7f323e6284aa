"""
Mocks for knowledge graph tests to avoid import issues.
"""

from dataclasses import dataclass
from typing import List, Optional

# Data models for knowledge graph components
@dataclass
class Entity:
    id: str
    name: str
    type: str

@dataclass
class Relationship:
    source: str
    target: str
    type: str

@dataclass
class Triplet:
    subject: Entity
    predicate: str
    object: Entity

@dataclass
class IntentAnalysis:
    intent: str
    related_concepts: List[str]
    negative_concepts: List[str] = None

@dataclass
class KnowledgeGraph:
    entities: List[Entity]
    relationships: List[Relationship]
    triplets: List[Triplet]

@dataclass
class SubGraph:
    entities: List[Entity]
    relationships: List[Relationship]
    triplets: List[Triplet]
    intent_analysis: Optional[IntentAnalysis] = None
    experiment_id: Optional[str] = None

# Mock classes for gap filling
@dataclass
class Island:
    nodes: List[Entity]
    size: int

@dataclass
class TopicAllocation:
    island_index: int
    num_topics: int

@dataclass
class IslandProcessingParams:
    island: Island
    num_topics: int
    existing_topics: List[str]

@dataclass
class IslandResult:
    island: Island
    new_topics: List[str]

@dataclass
class GapFillingResult:
    island_results: List[IslandResult]
    total_new_topics: int

# Mock Topic classes
@dataclass
class Topic:
    name: str
    description: str

@dataclass
class GenerationSource:
    value: str

@dataclass
class PersonaTopic:
    persona: str
    topic: str
    tactics: List[str]
    risk_type: Optional[str] = None
    generation_source: Optional[GenerationSource] = None