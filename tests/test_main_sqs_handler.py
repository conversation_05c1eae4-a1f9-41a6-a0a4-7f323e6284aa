import pytest
import json
from unittest.mock import patch, MagicMock
from src.main_sqs_handler import <PERSON><PERSON>ueue<PERSON>and<PERSON>, try_to_parse
from schemas.auth import AuthInfo
from schemas.generation import GenerateArgs


@pytest.fixture
def handler():
    with patch("src.main_sqs_handler.boto3.Session") as mock_session:
        mock_client = MagicMock()
        mock_session.return_value.client.return_value = mock_client
        with patch(
            "src.main_sqs_handler.os.environ",
            {
                "GEN_MAIN_QUEUE_URL": "queue_url",
                "GEN_MAIN_DL_QUEUE_URL": "dlq_url",
            },
        ):
            yield MainQueueHandler()


def test_try_to_parse_valid():
    obj = '{"foo": "bar"}'
    assert try_to_parse(obj) == {"foo": "bar"}


def test_try_to_parse_invalid():
    assert try_to_parse("not json") is None


def test_delete_message(handler):
    message = {"MessageId": "1", "ReceiptHandle": "abc"}
    handler.delete_message(message)
    handler.sqs.delete_message.assert_called_once()


def test_send_to_dead_letter(handler):
    message = {
        "Body": '{"foo": "bar"}',
        "MessageAttributes": {},
        "MessageId": "1",
        "ReceiptHandle": "abc",
    }
    handler.send_to_dead_letter(message, "reason", "detail")
    handler.sqs.send_message.assert_called_once()
    handler.sqs.delete_message.assert_called_once()


def test_valid_auth_missing_attrs(handler):
    message = {}
    with patch.object(handler, "send_to_dead_letter") as sdl:
        handler._valid_auth(message)
        sdl.assert_called_once()


def test_valid_auth_missing_auth(handler):
    message = {"MessageAttributes": {}}
    with patch.object(handler, "send_to_dead_letter") as sdl:
        handler._valid_auth(message)
        sdl.assert_called_once()


def test_valid_auth_invalid_token(handler):
    message = {
        "MessageAttributes": {"Auth": {"StringValue": json.dumps({"x-api-key": "bad"})}}
    }
    with (
        patch("src.main_sqs_handler.verify_api_key", return_value=None),
        patch.object(handler, "send_to_dead_letter") as sdl,
    ):
        handler._valid_auth(message)
        sdl.assert_called_once()


def test_valid_auth_success(handler):
    message = {
        "MessageAttributes": {
            "Auth": {"StringValue": json.dumps({"x-api-key": "good"})}
        }
    }
    with patch("src.main_sqs_handler.verify_api_key", return_value={"sub": "user"}):
        auth = handler._valid_auth(message)
        assert isinstance(auth, AuthInfo)
        assert auth.user_id == "user"


def test_validate_message_body_structure_missing_fields(handler):
    message = {"Body": json.dumps({"role": "r"})}
    auth_info = AuthInfo("user", {})
    with patch.object(handler, "send_to_dead_letter") as sdl:
        handler._validate_message_body_structure(message, auth_info)
        sdl.assert_called_once()


def test_validate_message_body_structure_success(handler):
    body = {
        "role": "r",
        "id": "id",
        "source_data": {},
        "user_description": "desc",
    }
    message = {"Body": json.dumps(body)}
    auth_info = AuthInfo("user", {"x-api-key": "k"})
    args = handler._validate_message_body_structure(message, auth_info)
    assert isinstance(args, GenerateArgs)
    assert args.user_id == "user"


def test_validate_message_missing_body(handler):
    message = {
        "MessageAttributes": {
            "Auth": {"StringValue": json.dumps({"x-api-key": "good"})}
        }
    }
    with (
        patch("src.main_sqs_handler.verify_api_key", return_value={"sub": "user"}),
        patch.object(handler, "delete_message") as dm,
    ):
        handler.validate_message(message)
        dm.assert_called_once()


def test_validate_message_success(handler):
    body = {
        "role": "r",
        "id": "id",
        "source_data": {},
        "user_description": "desc",
    }
    message = {
        "MessageAttributes": {
            "Auth": {"StringValue": json.dumps({"x-api-key": "good"})}
        },
        "Body": json.dumps(body),
    }
    with patch("src.main_sqs_handler.verify_api_key", return_value={"sub": "user"}):
        args = handler.validate_message(message)
        assert isinstance(args, GenerateArgs)


def test_process_message_success(handler):
    body = {
        "role": "r",
        "id": "id",
        "source_data": {},
        "user_description": "desc",
    }
    message = {
        "MessageAttributes": {
            "Auth": {"StringValue": json.dumps({"x-api-key": "good"})}
        },
        "Body": json.dumps(body),
        "MessageId": "1",
        "ReceiptHandle": "abc",
    }
    with (
        patch("src.main_sqs_handler.verify_api_key", return_value={"sub": "user"}),
        patch(
            "src.main_sqs_handler.persona_topic_generator.generate", return_value=None
        ) as gen,
        patch.object(handler, "delete_message") as dm,
    ):
        import asyncio

        asyncio.run(handler.process_message(message))
        gen.assert_called_once()
        dm.assert_called_once()


def test_process_message_exception(handler):
    message = {"Body": '{"id": "id"}', "MessageId": "1", "ReceiptHandle": "abc"}
    with patch.object(handler, "validate_message", side_effect=Exception("fail")):
        import asyncio

        with pytest.raises(Exception):
            asyncio.run(handler.process_message(message))
