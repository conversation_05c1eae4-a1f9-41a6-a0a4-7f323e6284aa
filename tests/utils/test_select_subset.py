from src.persona_topic import utils


def test_select_subset_basic():
    items = ["apple", "banana", "cherry", "date", "elderberry"]
    count = 3
    context = "fruits for a fruit salad"
    item_type = "fruits"

    # Patch chat_completion to return a simple list
    def fake_chat_completion(*args, **kwargs):
        return "apple\nbanana\ncherry"

    utils.chat_completion = fake_chat_completion
    selected = utils.select_subset(items, count, context, item_type)
    assert len(selected) == count
    assert set(selected).issubset(set(items))


def test_select_subset_fewer_items():
    items = ["apple", "banana"]
    count = 2
    context = "fruits for a fruit salad"
    item_type = "fruits"
    utils.chat_completion = lambda *a, **k: "apple\nbanana"
    selected = utils.select_subset(items, count, context, item_type)
    assert len(selected) == count
    assert set(["apple", "banana"]).issubset(set(selected))


def test_select_subset_empty():
    items = []
    count = 3
    context = "fruits for a fruit salad"
    item_type = "fruits"
    selected = utils.select_subset(items, count, context, item_type)
    assert selected == []
