import pytest
from persona_topic.utils import extract_json_from_text


def test_extract_json_from_text_with_json_tags():
    text = """
    <json>{"a": 1, "b": 2}</json>
    """
    result = extract_json_from_text(text)
    assert result == {"a": 1, "b": 2}


def test_extract_json_from_text_with_json_code_block():
    text = """
    ```json
    {"foo": "bar", "baz": 123}
    ```
    """
    result = extract_json_from_text(text)
    assert result == {"foo": "bar", "baz": 123}


def test_extract_json_from_text_with_plain_code_block():
    text = """
    ```
    {"x": 10, "y": 20}
    ```
    """
    result = extract_json_from_text(text)
    assert result == {"x": 10, "y": 20}


def test_extract_json_from_text_with_raw_json():
    text = '{"hello": "world"}'
    result = extract_json_from_text(text)
    assert result == {"hello": "world"}


def test_extract_json_from_text_with_invalid_json_and_examples_field():
    text = '{"examples": "one, two, three"}'
    result = extract_json_from_text(text)
    assert result == {"examples": "one; two; three"} or result == {
        "examples": "one, two, three"
    }


def test_extract_json_from_text_with_invalid_json():
    text = "{foo: bar}"
    with pytest.raises(Exception):
        extract_json_from_text(text)
