import pytest
from unittest.mock import patch
from src.persona_topic.utils import resize_grouped_list


@pytest.mark.asyncio
async def test_already_correct_count():
    items = [["item1", "item2"], ["item3", "item4"], ["item5", "item6"]]
    count = 3
    result = await resize_grouped_list(items, count)
    assert result == items


@pytest.mark.asyncio
async def test_more_than_needed():
    items = [
        ["item1", "item2"],
        ["item3", "item4"],
        ["item5", "item6"],
        ["item7", "item8"],
    ]
    count = 2

    # Mock random.sample to return predictable results
    with patch("random.sample", return_value=[items[0], items[2]]):
        result = await resize_grouped_list(items, count)
        assert result == [items[0], items[2]]
        assert len(result) == count


@pytest.mark.asyncio
async def test_fewer_than_needed_success():
    items = [["item1", "item2", "item3"], ["item4", "item5", "item6"]]
    count = 4
    missing_count = 2

    # Mock the LLM response and parsing
    mock_response = "mock_response"
    additional_groups = [["item7", "item8", "item9"], ["item10", "item11", "item12"]]

    with (
        patch("src.persona_topic.utils.format_prompt_from_template") as mock_format,
        patch(
            "src.persona_topic.utils.achat_completion", return_value=mock_response
        ) as mock_chat,
        patch(
            "src.persona_topic.utils.parse_grouped_items",
            return_value=additional_groups,
        ) as mock_parse,
    ):
        result = await resize_grouped_list(items, count, "test_items")

        # Verify the correct functions were called
        mock_format.assert_called_with(
            "few_shot_group.jinja2",
            item_type="test_items",
            items=items,
            missing_count=missing_count,
        )
        mock_chat.assert_called_once()
        mock_parse.assert_called_with(mock_response)

        # Verify the result
        assert len(result) == count
        assert result[:2] == items
        assert result[2:] == additional_groups


@pytest.mark.asyncio
async def test_fewer_than_needed_partial_success():
    items = [["item1", "item2", "item3"]]
    count = 4

    # First call returns only one group, second call returns two more
    first_additional = [["item4", "item5", "item6"]]
    second_additional = [["item7", "item8", "item9"], ["item10", "item11", "item12"]]

    with (
        patch("src.persona_topic.utils.format_prompt_from_template") as mock_format,
        patch(
            "src.persona_topic.utils.achat_completion",
            side_effect=["response1", "response2"],
        ) as mock_chat,
        patch(
            "src.persona_topic.utils.parse_grouped_items",
            side_effect=[first_additional, second_additional],
        ) as mock_parse,
    ):
        result = await resize_grouped_list(items, count, "test_items")

        # Verify format_prompt was called twice with different missing counts
        assert mock_format.call_count == 2
        assert mock_chat.call_count == 2
        assert mock_parse.call_count == 2

        # Verify the result
        assert len(result) == count
        assert result[0] == items[0]
        assert result[1] == first_additional[0]
        assert result[2:] == second_additional


@pytest.mark.asyncio
async def test_fewer_than_needed_failure():
    items = [["item1", "item2", "item3", "item4"]]
    count = 3

    # Simulate LLM failures
    with (
        patch("src.persona_topic.utils.format_prompt_from_template"),
        patch(
            "src.persona_topic.utils.achat_completion",
            side_effect=Exception("LLM error"),
        ),
        patch("src.persona_topic.utils.logger.error") as mock_logger,
        patch("random.choice", return_value=items[0]),
    ):
        result = await resize_grouped_list(items, count)

        # Verify error was logged
        assert mock_logger.call_count == 3  # Once for each attempt

        # Verify the result has duplicated items to reach the count
        assert len(result) == count
        assert all(group == items[0] for group in result)


@pytest.mark.asyncio
async def test_too_many_generated_groups():
    items = [["item1", "item2", "item3"]]
    count = 2

    # Generate more groups than needed
    additional_groups = [
        ["item4", "item5", "item6"],
        ["item7", "item8", "item9"],
        ["item10", "item11", "item12"],
    ]

    with (
        patch("src.persona_topic.utils.format_prompt_from_template"),
        patch("src.persona_topic.utils.achat_completion", return_value="response"),
        patch(
            "src.persona_topic.utils.parse_grouped_items",
            return_value=additional_groups,
        ),
        patch("random.sample", return_value=[additional_groups[0]]) as mock_sample,
    ):
        result = await resize_grouped_list(items, count)

        # Verify random.sample was called to reduce the number of groups
        mock_sample.assert_called_with(additional_groups, 1)

        # Verify the result
        assert len(result) == count
        assert result[0] == items[0]
        assert result[1] == additional_groups[0]
