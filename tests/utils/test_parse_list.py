from src.persona_topic.utils import parse_list


def test_parse_list_empty_input():
    assert parse_list("") == []
    assert parse_list(None) == []


def test_parse_list_basic():
    # Basic newline-separated list
    text = "item1\nitem2\nitem3"
    assert parse_list(text) == ["item1", "item2", "item3"]

    # With custom delimiter
    text = "item1,item2,item3"
    assert parse_list(text, delimiter=",") == ["item1", "item2", "item3"]


def test_parse_list_with_whitespace():
    # Whitespace should be stripped
    text = "  item1  \n item2 \n\titem3\t"
    assert parse_list(text) == ["item1", "item2", "item3"]

    # Empty lines should be skipped
    text = "item1\n\n\nitem2\n\nitem3"
    assert parse_list(text) == ["item1", "item2", "item3"]


def test_parse_list_with_prefixes():
    # Test bullet points
    text = "- item1\n* item2\n• item3"
    assert parse_list(text) == ["item1", "item2", "item3"]

    # Test numbered lists
    text = "1. item1\n2. item2\n3) item3"
    assert parse_list(text) == ["item1", "item2", "item3"]

    # Test special characters
    text = "→ item1\n➢ item2\n» item3"
    assert parse_list(text) == ["item1", "item2", "item3"]


def test_parse_list_with_punctuation():
    # Trailing punctuation should be removed
    text = "item1.\nitem2,\nitem3;"
    assert parse_list(text) == ["item1", "item2", "item3"]

    # But internal punctuation should be preserved
    text = "this is item 1.\nthis, is item 2\nthis: is item 3"
    assert parse_list(text) == ["this is item 1", "this, is item 2", "this: is item 3"]


def test_parse_list_mixed_formats():
    # Mix of different formats
    text = "1. First item\n- Second item\n* Third item\n→ Fourth item\na. Fifth item"
    assert parse_list(text) == [
        "First item",
        "Second item",
        "Third item",
        "Fourth item",
        "Fifth item",
    ]


def test_parse_list_with_leading_trailing_text():
    # Text with leading paragraph before list
    text = "This is a leading paragraph.\n1. First item\n2. Second item\n3. Third item"
    assert parse_list(text) == [
        "This is a leading paragraph",
        "First item",
        "Second item",
        "Third item",
    ]

    # Text with trailing paragraph after list
    text = "- Item one\n- Item two\n- Item three\nThis is a trailing paragraph."
    assert parse_list(text) == [
        "Item one",
        "Item two",
        "Item three",
        "This is a trailing paragraph",
    ]

    # Text with both leading and trailing paragraphs
    text = "Leading text here.\n• First point\n• Second point\nTrailing text here."
    assert parse_list(text) == [
        "Leading text here",
        "First point",
        "Second point",
        "Trailing text here",
    ]

    # Multi-line leading and trailing text
    text = "First line of intro.\nSecond line of intro.\n- List item 1\n- List item 2\nConclusion paragraph.\nFinal thoughts."
    assert parse_list(text) == [
        "First line of intro",
        "Second line of intro",
        "List item 1",
        "List item 2",
        "Conclusion paragraph",
        "Final thoughts",
    ]


def test_parse_list_with_numbered_categories():
    # Test parsing a numbered list of categories like the one in file_context_0
    text = """# 10 User Categories for a Chris Voss-Style Negotiation Chatbot

1. Business Executives - Senior decision-makers seeking negotiation strategies for high-stakes business deals, acquisitions, and partnerships

2. Sales Professionals - People looking to improve their ability to close deals, overcome objections, and negotiate better terms with clients

3. Human Resource Managers - Professionals handling employee relations, compensation negotiations, and conflict resolution in workplace settings"""

    expected = [
        "Business Executives - Senior decision-makers seeking negotiation strategies for high-stakes business deals, acquisitions, and partnerships",
        "Sales Professionals - People looking to improve their ability to close deals, overcome objections, and negotiate better terms with clients",
        "Human Resource Managers - Professionals handling employee relations, compensation negotiations, and conflict resolution in workplace settings",
    ]

    assert parse_list(text) == expected

    # Test with a longer list that has a title and description
    text = """# 10 User Categories for a Chris Voss-Style Negotiation Chatbot

1. Business Executives - Senior decision-makers seeking negotiation strategies for high-stakes business deals, acquisitions, and partnerships

2. Sales Professionals - People looking to improve their ability to close deals, overcome objections, and negotiate better terms with clients

3. Human Resource Managers - Professionals handling employee relations, compensation negotiations, and conflict resolution in workplace settings

4. Real Estate Agents/Investors - Individuals seeking tactical advantages in property negotiations, offers, and closings

5. Career Advancement Seekers - People preparing for job interviews, salary negotiations, or promotion discussions"""

    expected = [
        "Business Executives - Senior decision-makers seeking negotiation strategies for high-stakes business deals, acquisitions, and partnerships",
        "Sales Professionals - People looking to improve their ability to close deals, overcome objections, and negotiate better terms with clients",
        "Human Resource Managers - Professionals handling employee relations, compensation negotiations, and conflict resolution in workplace settings",
        "Real Estate Agents/Investors - Individuals seeking tactical advantages in property negotiations, offers, and closings",
        "Career Advancement Seekers - People preparing for job interviews, salary negotiations, or promotion discussions",
    ]

    assert parse_list(text) == expected
