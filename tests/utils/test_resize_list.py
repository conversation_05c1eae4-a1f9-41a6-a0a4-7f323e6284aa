from src.persona_topic.utils import resize_list

import pytest
from unittest.mock import patch


@pytest.mark.asyncio
async def test_resize_list_empty():
    """Test that resize_list raises ValueError when given an empty list."""
    with pytest.raises(ValueError, match="Cannot resize an empty list of test_items"):
        await resize_list([], 5, "test_items")


@pytest.mark.asyncio
async def test_resize_list_exact_count():
    """Test that resize_list returns the original list when count matches list length."""
    items = ["item1", "item2", "item3"]
    result = await resize_list(items, 3, "test_items")
    assert len(result) == 3
    assert set(result) == set(items)


@pytest.mark.asyncio
async def test_resize_list_reduce():
    """Test that resize_list correctly samples down when original list is too large."""
    items = ["item1", "item2", "item3", "item4", "item5"]
    result = await resize_list(items, 3, "test_items")
    assert len(result) == 3
    assert set(result).issubset(set(items))


@pytest.mark.asyncio
async def test_resize_list_small_increase():
    """Test that resize_list correctly handles small increases (missing_count <= len(items))."""
    items = ["item1", "item2", "item3"]

    result = await resize_list(items, 5, "dummy test items")
    assert len(result) == 5


@pytest.mark.asyncio
@patch("src.persona_topic.utils.format_prompt_from_template")
@patch("src.persona_topic.utils.chat_completion")
@patch("src.persona_topic.utils.parse_list")
async def test_resize_list_large_increase_success(
    mock_parse_list, mock_chat_completion, mock_format_prompt
):
    """Test that resize_list correctly handles large increases using LLM."""
    items = ["item1", "item2"]
    mock_format_prompt.return_value = "test prompt"
    mock_chat_completion.return_value = "generated response"
    mock_parse_list.return_value = ["new_item1", "new_item2", "new_item3", "new_item4"]

    result = await resize_list(items, 6, "test_items")
    assert len(result) == 6, f"Expected 6 items, got {len(result)}"
    assert mock_parse_list.called


@pytest.mark.asyncio
@patch("src.persona_topic.utils.format_prompt_from_template")
@patch("src.persona_topic.utils.chat_completion")
@patch("src.persona_topic.utils.parse_list")
async def test_resize_list_partial_generation(
    mock_parse_list, mock_chat_completion, mock_format_prompt
):
    """Test that resize_list handles partial generation correctly."""
    items = ["item1", "item2"]
    mock_format_prompt.return_value = "test prompt"
    mock_chat_completion.return_value = "generated response"

    # First call returns 2 items, second call returns 1 more
    mock_parse_list.side_effect = [["new_item1", "new_item2"], ["new_item3"]]

    result = await resize_list(items, 5, "test_items")

    assert len(result) == 5


@pytest.mark.asyncio
@patch("src.persona_topic.utils.format_prompt_from_template")
@patch("src.persona_topic.utils.chat_completion")
@patch("src.persona_topic.utils.parse_list")
@patch("src.persona_topic.utils.logger")
async def test_resize_list_generation_failure(
    mock_logger, mock_parse_list, mock_chat_completion, mock_format_prompt
):
    """Test that resize_list handles LLM generation failures gracefully."""
    items = ["item1", "item2"]
    mock_format_prompt.return_value = "test prompt"

    # Simulate 3 failed attempts
    mock_chat_completion.side_effect = [Exception("API error")] * 3

    result = await resize_list(items, 5, "test_items")

    assert len(result) == 5
    assert all(
        item in items for item in result
    )  # All items should be from original list
