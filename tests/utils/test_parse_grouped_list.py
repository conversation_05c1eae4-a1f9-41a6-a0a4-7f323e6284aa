from src.persona_topic.utils import parse_grouped_items


def test_basic_parsing():
    text = """item1
item2
---
item3
item4
---
item5
item6"""
    result = parse_grouped_items(text)
    expected = [["item1", "item2"], ["item3", "item4"], ["item5", "item6"]]
    assert result == expected


def test_empty_input():
    text = ""
    result = parse_grouped_items(text)
    assert result == []


def test_single_group():
    text = """item1
item2
item3"""
    result = parse_grouped_items(text)
    expected = [["item1", "item2", "item3"]]
    assert result == expected


def test_with_empty_lines():
    text = """item1

item2
---

item3
item4

---
item5
item6"""
    result = parse_grouped_items(text)
    expected = [["item1", "item2"], ["item3", "item4"], ["item5", "item6"]]
    assert result == expected


def test_with_trailing_separator():
    text = """item1
item2
---
item3
item4
---"""
    result = parse_grouped_items(text)
    expected = [["item1", "item2"], ["item3", "item4"]]
    assert result == expected


def test_with_leading_separator():
    text = """---
item1
item2
---
item3
item4"""
    result = parse_grouped_items(text)
    expected = [["item1", "item2"], ["item3", "item4"]]
    assert result == expected
