import pytest
from unittest.mock import patch
from src.persona_topic import utils


def test_read_documents_for_experiment_local(tmp_path):
    # Create fake local files
    file1 = tmp_path / "doc1.txt"
    file2 = tmp_path / "doc2.txt"
    file1.write_text("hello world")
    file2.write_text("goodbye world")
    files = [str(file1), str(file2)]
    result = utils.read_documents_for_experiment(
        files, org_id="irrelevant", store="local", aws_s3_document_bucket=None
    )
    assert result == ["hello world", "goodbye world"]


def test_read_documents_for_experiment_s3_authorized():
    doc_ids = ["org1/doc1.txt", "org1/doc2.txt"]
    org_id = "org1"
    bucket = "bucket"
    with patch("src.persona_topic.utils.get_documents_from_s3") as mock_get_docs:
        mock_get_docs.return_value = ["doc1 content", "doc2 content"]
        result = utils.read_documents_for_experiment(
            doc_ids, org_id, store="s3", aws_s3_document_bucket=bucket
        )
        assert result == ["doc1 content", "doc2 content"]
        mock_get_docs.assert_called_once_with(bucket, doc_ids)


def test_read_documents_for_experiment_s3_unauthorized():
    doc_ids = ["org1/doc1.txt", "org2/doc2.txt"]
    org_id = "org1"
    bucket = "bucket"
    with pytest.raises(ValueError) as exc:
        utils.read_documents_for_experiment(
            doc_ids, org_id, store="s3", aws_s3_document_bucket=bucket
        )
    assert "User does not have access" in str(exc.value)


def test_read_documents_for_experiment_invalid_store():
    with pytest.raises(ValueError) as exc:
        utils.read_documents_for_experiment(
            ["foo"], org_id="org1", store="invalid", aws_s3_document_bucket=None
        )
    assert "Invalid document store type" in str(exc.value)
