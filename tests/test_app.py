import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock, AsyncMock
import os

os.environ["GEN_TESTS_QUEUE_URL"] = "https://dummy-tests-queue-url"
os.environ["GEN_MAIN_QUEUE_URL"] = "https://dummy-main-queue-url"
os.environ["GEN_MAIN_DL_QUEUE_URL"] = "https://dummy-dlq-queue-url"
os.environ["SQS_REGION"] = "us-east-1"
from src.app import app

client = TestClient(app)


# Helper to mock authentication
@pytest.fixture(autouse=True)
def mock_auth(monkeypatch):
    monkeypatch.setattr("src.app.get_user", lambda request: "test-user")
    monkeypatch.setattr(
        "src.app.get_verified_token",
        lambda request: {
            "sub": "test-user",
            "permissions": ["threat_tester_invite:true"],
        },
    )


def test_health_check():
    response = client.get("/health")
    assert response.status_code == 200
    assert response.json() == {"status": "ok"}


def test_get_cache_file_not_found(tmp_path, monkeypatch):
    monkeypatch.setattr("src.app.OUTPUT_DIR", str(tmp_path))
    response = client.get("/cache/nonexistent")
    assert response.status_code == 404


def test_post_and_get_cache(tmp_path, monkeypatch):
    monkeypatch.setattr("src.app.OUTPUT_DIR", str(tmp_path))
    data = {"foo": "bar"}
    response = client.post("/cache/test", json=data)
    assert response.status_code == 200
    response = client.get("/cache/test")
    assert response.status_code == 200
    assert response.json()["foo"] == "bar"


def test_patch_cache_merges(tmp_path, monkeypatch):
    monkeypatch.setattr("src.app.OUTPUT_DIR", str(tmp_path))
    client.post("/cache/patchtest", json={"a": 1})
    response = client.patch("/cache/patchtest", json={"b": 2})
    assert response.status_code == 200
    response = client.get("/cache/patchtest")
    assert response.json() == {"a": 1, "b": 2}


@patch("src.app.httpx.AsyncClient")
@patch("src.app.persona_topic_generator.queue_prompt_group_generation")
def test_create_topic(mock_queue, mock_httpx):
    mock_client = MagicMock()
    mock_httpx.return_value.__aenter__.return_value = mock_client
    mock_client.get = AsyncMock()
    mock_response = MagicMock()
    mock_response.json.return_value = {
        "role": "test-role",
        "source_data": {
            "personas": ["p1"],
            "evaluation_configuration": {"risk1": {}},
            "generation_configuration": {
                "branching_factor": 1,
                "max_conversation_length": 4,
            },
        },
    }
    mock_client.get.return_value = mock_response
    body = {"new_topic": "topic1", "is_adapted_conversation": False}
    response = client.post("/experiments/exp1/topics", json=body)
    assert (
        response.status_code == 200 or response.status_code == 422
    )  # 422 if missing required fields
    assert mock_queue.called


@patch("src.app.httpx.AsyncClient")
@patch("src.app.persona_topic_generator.queue_prompt_group_generation")
def test_create_personas(mock_queue, mock_httpx):
    mock_client = MagicMock()
    mock_httpx.return_value.__aenter__.return_value = mock_client
    mock_client.get = AsyncMock()
    mock_response = MagicMock()
    mock_response.json.return_value = {
        "role": "test-role",
        "source_data": {
            "topics": ["t1"],
            "evaluation_configuration": {"risk1": {}},
            "generation_configuration": {
                "branching_factor": 1,
                "max_conversation_length": 4,
            },
        },
    }
    mock_client.get.return_value = mock_response
    body = {"new_persona": "persona1", "is_adapted_conversation": False}
    response = client.post("/experiments/exp1/personas", json=body)
    assert response.status_code == 200 or response.status_code == 422
    assert mock_queue.called


@patch("src.app.httpx.AsyncClient")
@patch("src.app.persona_topic_generator.queue_prompt_group_generation")
def test_create_tests(mock_queue, mock_httpx):
    mock_client = MagicMock()
    mock_httpx.return_value.__aenter__.return_value = mock_client
    mock_client.get = AsyncMock()
    mock_response = MagicMock()
    mock_response.json.return_value = {
        "role": "test-role",
        "source_data": {
            "evaluation_configuration": {"risk1": {}},
            "generation_configuration": {"branching_factor": 1},
        },
    }
    mock_client.get.return_value = mock_response
    body = {"personas": ["p1"], "topics": ["t1"], "is_adapted_conversation": False}
    response = client.post("/experiments/exp1/topics-and-personas", json=body)
    assert response.status_code == 200 or response.status_code == 422
    assert mock_queue.called


@patch("src.app.persona_topic_generator.generate")
def test_create_experiment(mock_generate):
    body = {
        "role": "test-role",
        "id": "expid",
        "source_data": {},
        "user_description": "desc",
    }
    response = client.post("/experiment", json=body)
    assert response.status_code == 200
    assert response.json()["status"] == "ok"
    assert mock_generate.called
