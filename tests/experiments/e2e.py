import requests
from datetime import datetime
import os


# PIZZA_BOT_APPLICATION_ID = "7e7309ab-001a-47f4-977f-663ad75b9f5f"
PIZZA_BOT_APPLICATION_ID = "12b9299d-126e-4187-84a2-359ba765ce09"
ZAPIER_BOT_APPLICATION_ID = "4ebc2370-4c11-4ea5-a07b-3ab2cfe9a2ec"
CV_BOT_APPLICATION_ID = "4e5827c6-a5d6-4c28-9e65-2ba2f8185624"


def create_experiment(
    *,
    application_id: str,
    experiment_name: str,
    role: str,
    experiment_intent: str,
    max_personas: int,
    num_turns: int,
    conversation_variations: int,
):
    url = "http://localhost:8080/api/experiments"

    TOKEN = os.getenv("GUARDRAILS_TOKEN")
    headers = {"x-api-key": TOKEN, "content-type": "application/json"}

    current_time = datetime.now()
    timestamp = current_time.strftime("%b %d, %Y, %I:%M %p")
    experiment_name = f"{experiment_name} {timestamp}"

    data = {
        "name": experiment_name,
        "role": role,
        "user_description": "",
        "use_cases": "",
        "generation_status": "pending",
        "evaluation_status": "pending",
        "validation_status": "pending",
        "source_data": {
            "docs": {"misc": [], "knowledge_base": [], "historical_data": []},
            "evaluation_configuration": {},
            "generation_configuration": {
                "experiment_intent": experiment_intent,
                "max_topics": 1,
                "max_personas": max_personas,
                "branching_factor": conversation_variations,
                "max_conversations": max_personas * conversation_variations,
                "min_conversation_length": num_turns,
                "max_conversation_length": num_turns,
                "continue_conversations_from_adapted_messages": False,
                "persona_topic_generators": [
                    {
                        "name": "app_description_system_prompt",
                        "settings": {"max_personas": max_personas},
                    }
                ],
                # "conversation": {
                #     "min_length": num_turns,
                #     "max_length": num_turns,
                # },
                "data_gen_mode": "custom",
            },
        },
        "application_id": application_id,
        "app_id": application_id,
        "is_template": False,
    }

    response = requests.post(url, headers=headers, json=data)
    return response


if __name__ == "__main__":
    response = create_experiment(
        application_id=PIZZA_BOT_APPLICATION_ID,
        experiment_name="pizza bot (diversity, different personas)",
        role="pizza chatbot for alfredo's pizza cafe\n",
        experiment_intent="typical customer",
        max_personas=10,
        num_turns=2,
        conversation_variations=5,
    )
    response = create_experiment(
        application_id=PIZZA_BOT_APPLICATION_ID,
        experiment_name="pizza bot (diversity, same persona)",
        role="pizza voice chatbot for alfredo's pizza cafe\n",
        experiment_intent="typical customer",
        max_personas=10,
        num_turns=2,
        conversation_variations=5,
    )
    response = create_experiment(
        application_id=PIZZA_BOT_APPLICATION_ID,
        experiment_name="pizza bot (diversity, same persona)",
        role="pizza voice chatbot for alfredo's pizza cafe\n",
        experiment_intent="customer only wants to talk about ingredients",
        max_personas=10,
        num_turns=2,
        conversation_variations=5,
    )
    response = create_experiment(
        application_id=PIZZA_BOT_APPLICATION_ID,
        experiment_name="pizza bot (diversity, same persona)",
        role="pizza voice chatbot for alfredo's pizza cafe\n",
        experiment_intent="what happens when a customer is angry",
        max_personas=10,
        num_turns=2,
        conversation_variations=5,
    )
    response = create_experiment(
        application_id=PIZZA_BOT_APPLICATION_ID,
        experiment_name="pizza bot (diversity, same persona)",
        role="pizza voice chatbot for alfredo's pizza cafe\n",
        experiment_intent="customer is interested in talking about the following topics: pizza, alfredo's pizza, pizza history, pizza ingredients, pizza recipes, pizza toppings, pizza crust, pizza sauce, pizza cheese, pizza toppings, pizza crust, pizza sauce, pizza cheese",
        max_personas=10,
        num_turns=2,
        conversation_variations=5,
    )
    # response = create_experiment(
    #     application_id=PIZZA_BOT_APPLICATION_ID,
    #     experiment_name="pizza bot (experiment intent)",
    #     role="pizza chatbot for alfredo's pizza cafe\n",
    #     experiment_intent="questions about ingredients, not ordering",
    #     max_personas=1,
    #     num_turns=5,
    #     conversation_variations=5,
    # )

    # CV_ROLE = "The voice chatbot is a consumer product where everyday people can seek advice and has broad appeal. It's like a phone call for a consulting session with Chris Voss."
    # response = create_experiment(
    #     application_id=CV_BOT_APPLICATION_ID,
    #     experiment_name="cv bot (diversity, different personas)",
    #     role=CV_ROLE,
    #     experiment_intent="typical consumer",
    #     max_personas=50,
    #     num_turns=3,
    #     conversation_variations=1,
    # )
    # response = create_experiment(
    #     application_id=CV_BOT_APPLICATION_ID,
    #     experiment_name="cv bot (diversity, same persona)",
    #     role=CV_ROLE,
    #     experiment_intent="typical consumer",
    #     max_personas=1,
    #     num_turns=5,
    #     conversation_variations=10,
    # )
    # response = create_experiment(
    #     application_id=CV_BOT_APPLICATION_ID,
    #     experiment_name="cv bot (experiment intent)",
    #     role=CV_ROLE,
    #     experiment_intent="parents trying to get their kids to do their homework",
    #     max_personas=1,
    #     num_turns=1,
    #     conversation_variations=5,
    # )

    # The voice chatbot is a consumer product where everyday people can seek advice and has broad appeal. It's like a phone call for a consulting session with Chris Voss.

    print(response.json())
