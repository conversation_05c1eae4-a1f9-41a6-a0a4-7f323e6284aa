import pytest
from unittest.mock import patch, AsyncMock

from src.persona_topic.app_description.generator import AppDescription<PERSON>enerator
from src.persona_topic.models import PersonaTopic, DataGenSource
from src.schemas.experiments import SourceData


@pytest.fixture
def app_description_generator():
    generator = AppDescriptionGenerator()
    generator._generate_persona_categories_from_role = AsyncMock(
        return_value=["Category 1", "Category 2"]
    )
    return generator


@pytest.mark.asyncio
async def test_generate_personas_from_persona_categories(app_description_generator):
    # Mock dependencies
    with (
        patch(
            "src.persona_topic.app_description.generator.format_prompt_from_template"
        ) as mock_format_prompt,
        patch(
            "src.persona_topic.app_description.generator.achat_completion"
        ) as mock_achat_completion,
        patch(
            "src.persona_topic.app_description.generator.parse_list"
        ) as mock_parse_list,
        patch(
            "src.persona_topic.app_description.generator.resize_list"
        ) as mock_resize_list,
    ):
        # Setup mocks
        mock_format_prompt.return_value = "formatted prompt"
        mock_achat_completion.return_value = "AI response"
        mock_parse_list.return_value = ["Persona 1"]
        mock_resize_list.return_value = ["Persona 1"]

        # Call the method
        result = (
            await app_description_generator._generate_personas_from_persona_categories(
                role="Software Developer",
                selected_category="Category 1",
                negative_categories=["Category 2"],
                count=1,
            )
        )

        mock_achat_completion.assert_called_once()
        mock_parse_list.assert_called_once_with("AI response")
        assert result == ["Persona 1"]


@pytest.mark.asyncio
async def test_generate_topics_from_persona(app_description_generator):
    # Mock dependencies
    with (
        patch(
            "src.persona_topic.app_description.generator.format_prompt_from_template"
        ) as mock_format_prompt,
        patch(
            "src.persona_topic.app_description.generator.achat_completion"
        ) as mock_achat_completion,
        patch(
            "src.persona_topic.app_description.generator.parse_list"
        ) as mock_parse_list,
        patch(
            "src.persona_topic.app_description.generator.resize_list"
        ) as mock_resize_list,
    ):
        # Setup mocks
        mock_format_prompt.return_value = "formatted prompt"
        mock_achat_completion.return_value = "AI response"
        mock_parse_list.return_value = ["Topic 1"]
        mock_resize_list.return_value = ["Topic 1"]

        # Call the method
        result = await app_description_generator._generate_topics_from_persona(
            role="Software Developer", persona="Persona 1", count=1
        )

        mock_achat_completion.assert_called_once()
        mock_parse_list.assert_called_once_with("AI response")
        assert result == ["Topic 1"]


@pytest.mark.asyncio
async def test_generate(app_description_generator):
    # Mock dependencies
    app_description_generator._generate_personas_from_persona_categories = AsyncMock(
        return_value=["Persona 1"]
    )
    app_description_generator._generate_topics_from_persona = AsyncMock(
        return_value=["Topic 1"]
    )

    # Call the method
    source_data = SourceData(app_description="Test app description")
    result = await app_description_generator.generate(
        role="Software Developer", max_personas=2, max_topics=1, source_data=source_data
    )

    # Assertions
    assert len(result) == 2  # 2 personas * 1 topic each
    assert isinstance(result[0], PersonaTopic)
    assert result[0].persona == "Persona 1"
    assert result[0].topic == "Topic 1"
    assert result[0].generation_source == DataGenSource.APP_DESCRIPTION


@pytest.mark.asyncio
async def test_generate_with_multiple_personas_and_topics(app_description_generator):
    # Mock dependencies
    app_description_generator._generate_personas_from_persona_categories = AsyncMock(
        side_effect=[["Persona 1"], ["Persona 2"]]
    )
    app_description_generator._generate_topics_from_persona = AsyncMock(
        side_effect=[["Topic 1", "Topic 2"], ["Topic 3", "Topic 4"]]
    )

    # Call the method
    source_data = SourceData(app_description="Test app description")
    result = await app_description_generator.generate(
        role="Software Developer", max_personas=2, max_topics=2, source_data=source_data
    )

    # Assertions
    assert len(result) == 4  # 2 personas * 2 topics each

    # Check first persona with its topics
    assert result[0].persona == "Persona 1"
    assert result[0].topic == "Topic 1"
    assert result[1].persona == "Persona 1"
    assert result[1].topic == "Topic 2"

    # Check second persona with its topics
    assert result[2].persona == "Persona 2"
    assert result[2].topic == "Topic 3"
    assert result[3].persona == "Persona 2"
    assert result[3].topic == "Topic 4"

    # Check all have correct generation source
    for pt in result:
        assert pt.generation_source == DataGenSource.APP_DESCRIPTION


@pytest.mark.asyncio
async def test_generate_persona_categories_from_role(app_description_generator):
    # Remove the mock to test the actual LLM call
    app_description_generator._generate_persona_categories_from_role = (
        AppDescriptionGenerator()._generate_persona_categories_from_role
    )

    # Call the method
    categories = await app_description_generator._generate_persona_categories_from_role(
        role="Software Developer", count=3
    )

    # Assertions
    assert len(categories) == 3
    assert all(isinstance(category, str) for category in categories)
    assert all(len(category) > 0 for category in categories)
    # Check that categories are meaningful and different from each other
    assert len(set(categories)) == 3  # All categories should be unique


@pytest.mark.asyncio
async def test_generate_personas_from_persona_categories_with_llm(
    app_description_generator,
):
    # Remove the mock to test the actual LLM call
    app_description_generator._generate_personas_from_persona_categories = (
        AppDescriptionGenerator()._generate_personas_from_persona_categories
    )

    # Call the method
    personas = (
        await app_description_generator._generate_personas_from_persona_categories(
            role="Software Developer",
            selected_category="Junior developers with limited experience",
            negative_categories=["Senior developers with extensive experience"],
            count=2,
        )
    )

    # Assertions
    assert len(personas) == 2
    assert all(isinstance(persona, str) for persona in personas)
    assert all(len(persona) > 0 for persona in personas)


@pytest.mark.asyncio
async def test_generate_topics_from_persona_with_llm(app_description_generator):
    # Remove the mock to test the actual LLM call
    app_description_generator._generate_topics_from_persona = (
        AppDescriptionGenerator()._generate_topics_from_persona
    )

    # Call the method
    topics = await app_description_generator._generate_topics_from_persona(
        role="Software Developer",
        persona="A junior developer who recently graduated from a coding bootcamp and is struggling with imposter syndrome",
        count=2,
    )

    # Assertions
    assert len(topics) == 2
    assert all(isinstance(topic, str) for topic in topics)
    assert all(len(topic) > 0 for topic in topics)


@pytest.mark.asyncio
async def test_end_to_end_generation(app_description_generator):
    # Use a real AppDescriptionGenerator instance for end-to-end testing
    real_generator = AppDescriptionGenerator()

    # Call the method
    source_data = SourceData(
        app_description="A code review application that helps developers identify bugs and security vulnerabilities in their code"
    )
    result = await real_generator.generate(
        role="Software Developer", max_personas=2, max_topics=2, source_data=source_data
    )

    # Assertions
    assert len(result) == 4  # 2 personas * 2 topics each
    assert all(isinstance(pt, PersonaTopic) for pt in result)
    assert all(pt.generation_source == DataGenSource.APP_DESCRIPTION for pt in result)

    # Check that we have 2 unique personas
    unique_personas = {pt.persona for pt in result}
    assert len(unique_personas) == 2

    # Check that each persona has 2 topics
    for persona in unique_personas:
        persona_topics = [pt for pt in result if pt.persona == persona]
        assert len(persona_topics) == 2
        assert len({pt.topic for pt in persona_topics}) == 2  # Ensure topics are unique
