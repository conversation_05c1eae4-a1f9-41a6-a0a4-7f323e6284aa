import pytest
import os
os.environ["GEN_TESTS_QUEUE_URL"] = "https://dummy-tests-queue-url"
os.environ["GEN_MAIN_QUEUE_URL"] = "https://dummy-main-queue-url"
os.environ["GEN_MAIN_DL_QUEUE_URL"] = "https://dummy-dlq-queue-url"
os.environ["SQS_REGION"] = "us-east-1"
from unittest.mock import MagicMock, patch
from src.persona_topic_generator import PersonaTopicGenerator


@pytest.fixture
def persona_topic_generator():
    with patch("src.persona_topic_generator.boto3.Session") as mock_session:
        mock_sqs = MagicMock()
        mock_session.return_value.client.return_value = mock_sqs
        with patch.dict("os.environ", {"GEN_TESTS_QUEUE_URL": "dummy_url"}):
            yield PersonaTopicGenerator()


@patch("src.persona_topic_generator.random.randint", return_value=3)
def test_send_test_messages_non_persona(mock_randint, persona_topic_generator):
    persona_topic_generator.sqs.send_message = MagicMock()
    num = persona_topic_generator.send_test_messages(
        generation_method="manyturn",
        branching_factor=2,
        role="user",
        persona="persona1",
        topic="topic1",
        experiment_id="exp1",
        source_tactics=[],
        tactics=["t1"],
        auth_header={"Authorization": "Bearer token"},
        risk_type=None,
        disable_probability=False,
        current_depth=0,
        min_depth=2,
        max_depth=5,
        is_adapted_conversation=False,
    )
    assert num == 2
    assert persona_topic_generator.sqs.send_message.call_count == 2


@patch("src.persona_topic_generator.random.randint", return_value=4)
def test_send_test_messages_persona_disable_probability(
    mock_randint, persona_topic_generator
):
    persona_topic_generator.sqs.send_message = MagicMock()
    num = persona_topic_generator.send_test_messages(
        generation_method="persona",
        branching_factor=2,
        role="user",
        persona="persona2",
        topic="topic2",
        experiment_id="exp2",
        source_tactics=[],
        tactics=["t2"],
        auth_header={"Authorization": "Bearer token"},
        risk_type=None,
        disable_probability=True,
        current_depth=1,
        min_depth=2,
        max_depth=6,
        is_adapted_conversation=True,
    )
    assert num == 1
    assert persona_topic_generator.sqs.send_message.call_count == 1


@patch("src.persona_topic_generator.random.randint", return_value=5)
def test_send_test_messages_persona_enable_probability(
    mock_randint, persona_topic_generator
):
    persona_topic_generator.sqs.send_message = MagicMock()
    num = persona_topic_generator.send_test_messages(
        generation_method="persona",
        branching_factor=2,
        role="user",
        persona="persona3",
        topic="topic3",
        experiment_id="exp3",
        source_tactics=[],
        tactics=["t3"],
        auth_header={"Authorization": "Bearer token"},
        risk_type=None,
        disable_probability=False,
        current_depth=2,
        min_depth=2,
        max_depth=7,
        is_adapted_conversation=False,
    )
    assert num == 0
    assert persona_topic_generator.sqs.send_message.call_count == 0


@patch(
    "src.persona_topic_generator.PersonaTopicGenerator.send_test_messages",
    return_value=2,
)
@patch("builtins.open")
def test_queue_prompt_group_generation_persona_topics(
    mock_open, mock_send, persona_topic_generator
):
    # Patch file reading for tactics
    mock_open.return_value.__enter__.return_value = ["tactic1\ttactic2\n"]

    # Create a mock PersonaTopic
    class MockPersonaTopic:
        persona = "personaA"
        topic = "topicA"
        tactics = ["t1"]
        risk_type = None
        generation_source = type("gs", (), {"value": "foo"})()

    persona_topics = [MockPersonaTopic()]
    result = persona_topic_generator.queue_prompt_group_generation(
        experiment_id="expid",
        role="user",
        risks=[],
        branching_factor=1,
        min_depth=2,
        max_depth=3,
        personas=[],
        topics=[],
        auth_header={"Authorization": "Bearer token"},
        disable_probability=False,
        generation_source=None,
        persona_topics=persona_topics,
        max_conversations=-1,
        is_adapted_conversation=False,
        messages=0,
    )
    assert result == 2
    assert mock_send.call_count == 1


@patch(
    "src.persona_topic_generator.PersonaTopicGenerator.send_test_messages",
    return_value=3,
)
@patch("builtins.open")
def test_queue_prompt_group_generation_personas_topics(
    mock_open, mock_send, persona_topic_generator
):
    mock_open.return_value.__enter__.return_value = ["tactic1\ttactic2\n"]
    result = persona_topic_generator.queue_prompt_group_generation(
        experiment_id="expid",
        role="user",
        risks=[],
        branching_factor=1,
        min_depth=2,
        max_depth=3,
        personas=["personaB"],
        topics=["topicB"],
        auth_header={"Authorization": "Bearer token"},
        disable_probability=False,
        generation_source=None,
        persona_topics=[],
        max_conversations=-1,
        is_adapted_conversation=False,
        messages=0,
    )
    assert result == 3
    assert mock_send.call_count == 1
