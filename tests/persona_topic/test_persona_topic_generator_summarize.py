import pytest
import os
os.environ["GEN_TESTS_QUEUE_URL"] = "https://dummy-tests-queue-url"
os.environ["GEN_MAIN_QUEUE_URL"] = "https://dummy-main-queue-url"
os.environ["GEN_MAIN_DL_QUEUE_URL"] = "https://dummy-dlq-queue-url"
os.environ["SQS_REGION"] = "us-east-1"
from unittest.mock import patch, AsyncMock, MagicMock
from src.persona_topic_generator import PersonaTopicGenerator


@pytest.fixture
def persona_topic_generator():
    with patch("src.persona_topic_generator.boto3.Session") as mock_session:
        mock_sqs = MagicMock()
        mock_session.return_value.client.return_value = mock_sqs
        with patch.dict("os.environ", {"GEN_TESTS_QUEUE_URL": "dummy_url"}):
            yield PersonaTopicGenerator()


@pytest.mark.asyncio
@patch("src.persona_topic_generator.achat_completion", new_callable=AsyncMock)
async def test_summarize_personas_basic(mock_achat_completion, persona_topic_generator):
    # Arrange
    personas = [
        "Tech-savvy Graduate Student: <PERSON> is a PhD candidate in computational linguistics who meticulously tracks research outputs across multiple platforms.",
        "Retired Army Colonel: Pat is a disciplined, strategic thinker who enjoys mentoring young professionals in leadership roles.",
        "Startup Founder: Alex is an ambitious entrepreneur focused on AI-driven healthcare solutions.",
    ]
    # Simulate AI returning one summary per persona, one per line
    mock_achat_completion.return_value = "Research Student\nMentor\nHealthcare Founder"

    # Act
    ptg = persona_topic_generator
    summaries = await ptg.summarize_personas(personas)

    # Assert
    assert summaries == ["Research Student", "Mentor", "Healthcare Founder"]
    assert mock_achat_completion.await_count == 1


@pytest.mark.asyncio
@patch("src.persona_topic_generator.achat_completion", new_callable=AsyncMock)
async def test_summarize_personas_batching(
    mock_achat_completion, persona_topic_generator
):
    # Arrange: 7 personas, batch_size=5, so 2 batches
    personas = [f"Persona {i}: Description {i}" for i in range(7)]
    # First batch returns 5, second batch returns 2
    mock_achat_completion.side_effect = [
        "Summary1\nSummary2\nSummary3\nSummary4\nSummary5",
        "Summary6\nSummary7",
    ]
    ptg = persona_topic_generator
    # Act
    summaries = await ptg.summarize_personas(personas)
    # Assert
    assert summaries == [f"Summary{i + 1}" for i in range(7)]
    assert mock_achat_completion.await_count == 2


@pytest.mark.asyncio
@patch("src.persona_topic_generator.achat_completion", new_callable=AsyncMock)
async def test_summarize_personas_handles_short_response(
    mock_achat_completion, persona_topic_generator
):
    # Arrange: 3 personas, but only 2 summaries returned
    personas = ["A", "B", "C"]
    mock_achat_completion.return_value = "SummaryA\nSummaryB"
    ptg = persona_topic_generator
    # Act
    summaries = await ptg.summarize_personas(personas)
    # Assert: Should pad with 'Generic User'
    assert summaries == ["SummaryA", "SummaryB", "Generic User"]


@pytest.mark.asyncio
@patch("src.persona_topic_generator.achat_completion", new_callable=AsyncMock)
async def test_summarize_personas_handles_long_response(
    mock_achat_completion, persona_topic_generator
):
    # Arrange: 2 personas, but 3 summaries returned
    personas = ["A", "B"]
    mock_achat_completion.return_value = "SummaryA\nSummaryB\nSummaryC"
    ptg = persona_topic_generator
    # Act
    summaries = await ptg.summarize_personas(personas)
    # Assert: Should trim to 2
    assert summaries == ["SummaryA", "SummaryB"]
