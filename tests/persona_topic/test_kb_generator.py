import pytest
import os
from unittest.mock import <PERSON>Mock

from src.persona_topic.kb.generator import KnowledgeBaseGenerator
from src.persona_topic.models import PersonaTopic, DataGenSource
from src.persona_topic.topic_tree import TopicTree
from src.schemas.experiments import SourceData


@pytest.fixture
def kb_generator():
    generator = KnowledgeBaseGenerator()
    return generator


@pytest.fixture
def sample_kb_data():
    # Create a list of sample KB data paths
    # In a real test, these would be actual file paths
    current_dir = os.path.dirname(os.path.abspath(__file__))
    test_data_dir = os.path.join(current_dir, "test_data")

    # Create test directory if it doesn't exist
    if not os.path.exists(test_data_dir):
        os.makedirs(test_data_dir)

    # Create a sample test file if it doesn't exist
    test_file_path = os.path.join(test_data_dir, "sample_kb.txt")
    if not os.path.exists(test_file_path):
        with open(test_file_path, "w") as f:
            f.write("This is sample knowledge base content for testing.\n")
            f.write(
                "It contains information about AI assistants and their capabilities.\n"
            )
            f.write(
                "Users might ask about machine learning, natural language processing, and data analysis."
            )

    return [test_file_path]


@pytest.fixture
def topic_tree(sample_kb_data):
    # Create a mock TopicTree with predefined topics
    mock_tree = MagicMock(spec=TopicTree)
    mock_tree.get_unique_topics.return_value = [
        "AI capabilities",
        "Machine learning concepts",
        "Natural language processing",
    ]
    return mock_tree


@pytest.mark.asyncio
async def test_seed_topic_to_scenario(kb_generator):
    # Test converting a seed topic to a scenario
    role = "AI Assistant Expert"
    seed_topic = "Machine learning concepts"

    scenario = await kb_generator._seed_topic_to_scenario(role, seed_topic)

    # Assertions
    assert isinstance(scenario, str)
    assert len(scenario) > 0
    assert len(scenario.split()) <= 60  # Roughly 50 words with some margin


@pytest.mark.asyncio
async def test_scenario_to_persona(kb_generator):
    # Test deriving a persona from a scenario
    role = "AI Assistant Expert"
    scenario = "A software developer is struggling to understand how to implement a neural network for their project and needs guidance on choosing the right architecture."

    persona = await kb_generator._scenario_to_persona(role, scenario)

    # Assertions
    assert isinstance(persona, str)
    assert len(persona) > 0


@pytest.mark.asyncio
async def test_expand_scenarios(kb_generator):
    # Test expanding a scenario into multiple related scenarios
    role = "AI Assistant Expert"
    seed_topic = "Machine learning concepts"
    persona = "Curious software developer"
    initial_scenario = "A software developer is struggling to understand neural networks for their project."
    count = 3

    scenarios = await kb_generator._expand_scenarios(
        role, seed_topic, persona, initial_scenario, count
    )

    # Assertions
    assert isinstance(scenarios, list)
    assert (
        len(scenarios) <= count
    )  # May be less if LLM couldn't generate enough unique ones
    assert all(isinstance(s, str) for s in scenarios)
    assert all(len(s) > 0 for s in scenarios)


@pytest.mark.asyncio
async def test_process_seed_topic(kb_generator):
    # Test processing a single seed topic
    role = "AI Assistant Expert"
    seed_topic = "Machine learning concepts"
    max_topics = 2

    persona_topics = await kb_generator._process_seed_topic(
        role, seed_topic, max_topics
    )

    # Assertions
    assert isinstance(persona_topics, list)
    assert len(persona_topics) <= max_topics
    assert all(isinstance(pt, PersonaTopic) for pt in persona_topics)
    assert all(
        pt.generation_source == DataGenSource.KNOWLEDGE_BASE for pt in persona_topics
    )
    assert all(pt.risk_type is None for pt in persona_topics)


@pytest.mark.asyncio
async def test_generate_with_topic_tree(kb_generator, topic_tree, sample_kb_data):
    # Test the main generate method
    role = "AI Assistant Expert"
    max_personas = 2
    max_topics = 2

    source_data = SourceData(
        docs={"knowledge_base": sample_kb_data, "misc": [], "historical_data": []}
    )

    result = await kb_generator.generate(
        role=role,
        max_personas=max_personas,
        max_topics=max_topics,
        source_data=source_data,
        user_id="test_user",
        topic_tree=topic_tree,
    )

    # Assertions
    assert isinstance(result, list)
    assert len(result) > 0
    assert all(isinstance(pt, PersonaTopic) for pt in result)
    assert all(pt.generation_source == DataGenSource.KNOWLEDGE_BASE for pt in result)

    # Check that we have the right number of results (up to max_personas * max_topics)
    assert len(result) <= max_personas * max_topics


@pytest.mark.asyncio
async def test_generate_with_empty_topic_tree(kb_generator, sample_kb_data):
    # Test with an empty topic tree
    empty_topic_tree = MagicMock(spec=TopicTree)
    empty_topic_tree.get_unique_topics.return_value = []

    role = "AI Assistant Expert"
    max_personas = 2
    max_topics = 2

    source_data = SourceData(
        docs={"knowledge_base": sample_kb_data, "misc": [], "historical_data": []}
    )

    result = await kb_generator.generate(
        role=role,
        max_personas=max_personas,
        max_topics=max_topics,
        source_data=source_data,
        user_id="test_user",
        topic_tree=empty_topic_tree,
    )

    # Assertions
    assert isinstance(result, list)
    assert len(result) == 0  # Should return empty list when no seed topics


@pytest.mark.asyncio
async def test_end_to_end_kb_generation(kb_generator, topic_tree, sample_kb_data):
    # End-to-end test with real LLM calls
    role = "AI Assistant that helps with programming questions"
    max_personas = 1
    max_topics = 2

    source_data = SourceData(
        docs={"knowledge_base": sample_kb_data, "misc": [], "historical_data": []}
    )

    result = await kb_generator.generate(
        role=role,
        max_personas=max_personas,
        max_topics=max_topics,
        source_data=source_data,
        user_id="test_user",
        topic_tree=topic_tree,
    )

    # Assertions
    assert len(result) > 0
    assert all(isinstance(pt, PersonaTopic) for pt in result)
    assert all(pt.generation_source == DataGenSource.KNOWLEDGE_BASE for pt in result)

    # Check that we have at least one persona with topics
    unique_personas = {pt.persona for pt in result}
    assert len(unique_personas) > 0

    # Check that each persona has topics
    for persona in unique_personas:
        persona_topics = [pt for pt in result if pt.persona == persona]
        assert len(persona_topics) > 0
        assert len({pt.topic for pt in persona_topics}) > 0  # Ensure topics exist
