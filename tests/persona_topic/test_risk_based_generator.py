import pytest

from src.persona_topic.risk_based.generator import RiskBasedGenerator

from src.persona_topic.models import PersonaTopic, DataGenSource
from src.schemas.experiments import SourceData


@pytest.fixture
def risk_based_generator():
    generator = RiskBasedGenerator()
    return generator


@pytest.mark.asyncio
async def test_generate_risk_based_persona_topics(risk_based_generator):
    # Call the method with real LLM
    result = await risk_based_generator._generate_risk_based_persona_topics(
        role="Software Developer",
        risk_name="Harmful Content",
        risk_description="Generating harmful or dangerous content",
        max_personas=1,
        max_topics=2,
    )

    # Assertions
    assert len(result) > 0
    assert isinstance(result[0][0], str)  # Persona is a string
    assert isinstance(result[0][1], list)  # Topics are a list
    assert len(result[0][1]) <= 2  # Max 2 topics
    assert all(isinstance(topic, str) for topic in result[0][1])


@pytest.mark.asyncio
async def test_generate_with_single_risk(risk_based_generator):
    # Create source data with a single risk
    source_data = SourceData(
        evaluation_configuration={
            "Harmful Content": {
                "name": "Harmful Content",
                "description": "Generating harmful or dangerous content",
            }
        }
    )

    # Call the method with real LLM
    result = await risk_based_generator.generate(
        role="Software Developer", max_personas=1, max_topics=2, source_data=source_data
    )

    # Assertions
    assert len(result) > 0
    assert isinstance(result[0], PersonaTopic)
    assert result[0].generation_source == DataGenSource.RISKS_SELECTED
    assert result[0].risk_type == "Harmful Content"

    # Check that we have the right number of results (up to max_personas * max_topics)
    assert len(result) <= 2  # 1 persona * 2 topics


@pytest.mark.asyncio
async def test_generate_with_multiple_risks(risk_based_generator):
    # Create source data with multiple risks
    source_data = SourceData(
        evaluation_configuration={
            "Harmful Content": {
                "name": "Harmful Content",
                "description": "Generating harmful or dangerous content",
            },
            "Privacy Violation": {
                "name": "Privacy Violation",
                "description": "Revealing private information",
            },
        }
    )

    # Call the method with real LLM
    result = await risk_based_generator.generate(
        role="Software Developer", max_personas=2, max_topics=2, source_data=source_data
    )

    # Assertions
    assert len(result) > 0

    # Check that we have results from both risk types
    risk_types = {pt.risk_type for pt in result}
    assert "Harmful Content" in risk_types
    assert "Privacy Violation" in risk_types

    # Check all have correct generation source
    for pt in result:
        assert pt.generation_source == DataGenSource.RISKS_SELECTED


@pytest.mark.asyncio
async def test_generate_with_skip_list(risk_based_generator):
    # Create generator with skip list
    generator_with_skip = RiskBasedGenerator(skip_list=["Harmful Content"])

    # Create source data with multiple risks
    source_data = SourceData(
        evaluation_configuration={
            "Harmful Content": {
                "name": "Harmful Content",
                "description": "Generating harmful or dangerous content",
            },
            "Privacy Violation": {
                "name": "Privacy Violation",
                "description": "Revealing private information",
            },
        }
    )

    # Call the method with real LLM
    result = await generator_with_skip.generate(
        role="Software Developer", max_personas=1, max_topics=2, source_data=source_data
    )

    # Assertions
    assert len(result) > 0
    assert all(pt.risk_type == "Privacy Violation" for pt in result)
    assert all(pt.risk_type != "Harmful Content" for pt in result)


@pytest.mark.asyncio
async def test_generate_with_empty_risks_after_filtering(risk_based_generator):
    # Create generator with skip list that filters out all risks
    generator_with_skip = RiskBasedGenerator(
        skip_list=["Harmful Content", "Privacy Violation"]
    )

    # Create source data with risks that will all be skipped
    source_data = SourceData(
        evaluation_configuration={
            "Harmful Content": {
                "name": "Harmful Content",
                "description": "Generating harmful or dangerous content",
            },
            "Privacy Violation": {
                "name": "Privacy Violation",
                "description": "Revealing private information",
            },
        }
    )

    # Call the method with real LLM
    result = await generator_with_skip.generate(
        role="Software Developer", max_personas=2, max_topics=2, source_data=source_data
    )

    # Assertions
    assert len(result) == 0  # Should return empty list when all risks are skipped


@pytest.mark.asyncio
async def test_generate_with_error_in_risk_generation(risk_based_generator):
    # Create a generator with a mock for one risk to simulate an error
    generator = RiskBasedGenerator()

    # Create a partial mock that only affects one specific risk
    original_method = generator._generate_risk_based_persona_topics

    async def mock_generate(*args, **kwargs):
        if args[1] == "Harmful Content":  # If it's the first risk
            raise ValueError("Error generating topics")
        else:
            return await original_method(*args, **kwargs)

    generator._generate_risk_based_persona_topics = mock_generate

    # Create source data with multiple risks
    source_data = SourceData(
        evaluation_configuration={
            "Harmful Content": {
                "name": "Harmful Content",
                "description": "Generating harmful or dangerous content",
            },
            "Privacy Violation": {
                "name": "Privacy Violation",
                "description": "Revealing private information",
            },
        }
    )

    # Call the method
    result = await generator.generate(
        role="Software Developer", max_personas=2, max_topics=2, source_data=source_data
    )

    # Assertions
    assert len(result) > 0
    assert all(pt.risk_type == "Privacy Violation" for pt in result)


@pytest.mark.asyncio
async def test_end_to_end_risk_based_generation(risk_based_generator):
    # Use a real RiskBasedGenerator instance for end-to-end testing
    real_generator = RiskBasedGenerator()

    # Create source data with a risk
    source_data = SourceData(
        evaluation_configuration={
            "Harmful Content": {
                "name": "Harmful Content",
                "description": "Generating harmful or dangerous content that could hurt people",
            }
        }
    )

    # Call the method with real LLM
    result = await real_generator.generate(
        role="Software Developer", max_personas=1, max_topics=2, source_data=source_data
    )

    # Assertions
    assert len(result) > 0
    assert all(isinstance(pt, PersonaTopic) for pt in result)
    assert all(pt.generation_source == DataGenSource.RISKS_SELECTED for pt in result)
    assert all(pt.risk_type == "Harmful Content" for pt in result)

    # Check that we have at least one persona with topics
    unique_personas = {pt.persona for pt in result}
    assert len(unique_personas) > 0

    # Check that each persona has topics
    for persona in unique_personas:
        persona_topics = [pt for pt in result if pt.persona == persona]
        assert len(persona_topics) > 0
        assert len({pt.topic for pt in persona_topics}) > 0  # Ensure topics exist
