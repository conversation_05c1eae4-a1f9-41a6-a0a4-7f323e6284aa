import pytest
import os

from src.persona_topic.historical_based.generator import HistoricalDataGenerator
from src.persona_topic.models import PersonaTopic, DataGenSource
from src.schemas.experiments import SourceData


@pytest.fixture
def historical_generator():
    generator = HistoricalDataGenerator()
    return generator


@pytest.fixture
def sample_historical_data():
    # Create a list of sample historical conversation data
    current_dir = os.path.dirname(os.path.abspath(__file__))
    test_data_dir = os.path.join(current_dir, "test_data")

    # Create test directory if it doesn't exist
    if not os.path.exists(test_data_dir):
        os.makedirs(test_data_dir)

    # Create a sample test conversation file if it doesn't exist
    test_file_path = os.path.join(test_data_dir, "sample_conversation.txt")
    if not os.path.exists(test_file_path):
        with open(test_file_path, "w") as f:
            f.write(
                "User: I'm trying to learn about machine learning but I'm not sure where to start.\n"
            )
            f.write(
                "Assistant: I'd be happy to help you get started with machine learning! It's a fascinating field. Would you like to know about the basic concepts first, or are you interested in practical applications?\n"
            )
            f.write(
                "User: I think I need to understand the basic concepts first. What are the main types of machine learning?\n"
            )
            f.write(
                "Assistant: Great approach! Understanding the fundamentals will give you a solid foundation. There are three main types of machine learning:\n\n1. Supervised Learning: The algorithm learns from labeled training data, and makes predictions based on that data. Examples include classification and regression problems.\n\n2. Unsupervised Learning: The algorithm works with unlabeled data and tries to find patterns or structures within it. Clustering and association are common examples.\n\n3. Reinforcement Learning: The algorithm learns by interacting with an environment and receiving feedback in the form of rewards or penalties.\n\nWhich of these areas sounds most interesting to you?\n"
            )
            f.write(
                "User: Supervised learning sounds most practical. What kind of real-world problems can it solve?\n"
            )

    return [test_file_path]


@pytest.mark.asyncio
async def test_extract_topic_from_conversation(
    historical_generator, sample_historical_data
):
    # Test extracting a topic from a conversation
    with open(sample_historical_data[0], "r") as f:
        conversation = f.read()

    role = "AI Learning Assistant"

    # Fix: directly await the coroutine instead of using async_run
    topic = await historical_generator.extract_topic_from_conversation(
        conversation, role
    )

    # Assertions
    assert isinstance(topic, str)
    assert len(topic) > 0


@pytest.mark.asyncio
async def test_mine_tactics_from_conversation(
    historical_generator, sample_historical_data
):
    # Test mining tactics from a conversation
    with open(sample_historical_data[0], "r") as f:
        conversation = f.read()

    # Fix: directly await the coroutine instead of using async_run
    tactics = await historical_generator.mine_tactics_from_conversation(conversation)

    # Assertions
    assert isinstance(tactics, list)
    assert len(tactics) > 0
    assert all(isinstance(tactic, dict) for tactic in tactics)

    # Check that each tactic has the required fields
    required_fields = [
        "response_type",
        "tone",
        "style",
        "examples",
        "context",
        "length_in_words",
    ]
    for tactic in tactics:
        for field in required_fields:
            assert field in tactic
        assert isinstance(tactic["examples"], list)


@pytest.mark.asyncio
async def test_expand_topics(historical_generator):
    # Test expanding topics
    role = "AI Learning Assistant"
    persona = "Curious computer science student"
    topic = "Machine learning fundamentals"
    count = 3

    expanded_topics = await historical_generator._expand_topics(
        role, persona, topic, count
    )

    # Assertions
    assert isinstance(expanded_topics, list)
    assert len(expanded_topics) <= count
    assert all(isinstance(t, str) for t in expanded_topics)
    assert all(len(t) > 0 for t in expanded_topics)


@pytest.mark.asyncio
async def test_backfill_persona_from_topic(historical_generator):
    # Test generating a persona from a topic
    role = "AI Learning Assistant"
    topic = "Machine learning fundamentals"

    persona = await historical_generator._backfill_persona_from_topic(role, topic)

    # Assertions
    assert isinstance(persona, str)
    assert len(persona) > 0


@pytest.mark.asyncio
async def test_generate_with_historical_data(
    historical_generator, sample_historical_data
):
    # Test the main generate method
    role = "AI Learning Assistant"
    max_personas = 1
    max_topics = 2

    source_data = SourceData(
        docs={
            "knowledge_base": [],
            "misc": [],
            "historical_data": sample_historical_data,
        }
    )

    result = await historical_generator.generate(
        role=role,
        max_personas=max_personas,
        max_topics=max_topics,
        source_data=source_data,
        user_id="test_user",
        historical_data=sample_historical_data,
    )

    # Assertions
    assert isinstance(result, list)
    assert len(result) > 0
    assert all(isinstance(pt, PersonaTopic) for pt in result)
    assert all(pt.generation_source == DataGenSource.HISTORICAL_DATA for pt in result)

    # Check that we have the right number of results (up to max_personas * max_topics)
    assert len(result) <= max_personas * max_topics

    # Check that each PersonaTopic has tactics
    for pt in result:
        assert hasattr(pt, "tactics")
        assert isinstance(pt.tactics, list)


@pytest.mark.asyncio
async def test_generate_with_empty_historical_data(historical_generator):
    # Test with empty historical data
    role = "AI Learning Assistant"
    max_personas = 2
    max_topics = 2

    source_data = SourceData(
        docs={"knowledge_base": [], "misc": [], "historical_data": []}
    )

    result = await historical_generator.generate(
        role=role,
        max_personas=max_personas,
        max_topics=max_topics,
        source_data=source_data,
        user_id="test_user",
        historical_data=[],
    )

    # Assertions
    assert isinstance(result, list)
    assert len(result) == 0  # Should return empty list when no historical data


@pytest.mark.asyncio
async def test_end_to_end_historical_generation(
    historical_generator, sample_historical_data
):
    # End-to-end test with real LLM calls
    role = "AI Assistant that helps with learning programming concepts"
    max_personas = 1
    max_topics = 2

    source_data = SourceData(
        docs={
            "knowledge_base": [],
            "misc": [],
            "historical_data": sample_historical_data,
        }
    )

    result = await historical_generator.generate(
        role=role,
        max_personas=max_personas,
        max_topics=max_topics,
        source_data=source_data,
        user_id="test_user",
        historical_data=sample_historical_data,
    )

    # Assertions
    assert len(result) > 0
    assert all(isinstance(pt, PersonaTopic) for pt in result)
    assert all(pt.generation_source == DataGenSource.HISTORICAL_DATA for pt in result)

    # Check that we have at least one persona with topics
    unique_personas = {pt.persona for pt in result}
    assert len(unique_personas) > 0

    # Check that each persona has topics
    for persona in unique_personas:
        persona_topics = [pt for pt in result if pt.persona == persona]
        assert len(persona_topics) > 0
        assert len({pt.topic for pt in persona_topics}) > 0  # Ensure topics exist

        # Check that tactics were extracted
        for pt in persona_topics:
            assert hasattr(pt, "tactics")
            assert isinstance(pt.tactics, list)
