import pytest
import os
os.environ["GEN_TESTS_QUEUE_URL"] = "https://dummy-tests-queue-url"
os.environ["GEN_MAIN_QUEUE_URL"] = "https://dummy-main-queue-url"
os.environ["GEN_MAIN_DL_QUEUE_URL"] = "https://dummy-dlq-queue-url"
os.environ["SQS_REGION"] = "us-east-1"
from unittest.mock import patch, AsyncMock
from src.persona_topic_generator import PersonaTopicGenerator
from schemas.generation import GenerateArgs
from schemas.experiments import SourceData, DocumentReferences


@pytest.mark.asyncio
@patch("src.persona_topic_generator.httpx.AsyncClient", autospec=True)
@patch(
    "src.persona_topic_generator.patch_experiment_persona_topics",
    new_callable=AsyncMock,
)
@patch("src.persona_topic_generator.PersonaTopicGeneratorFactory.create_generator")
@patch("src.persona_topic_generator.read_documents_for_experiment", return_value=[])
@patch("src.persona_topic_generator.TopicTree")
async def test_generate_minimal(
    mock_topic_tree,
    mock_read_docs,
    mock_create_generator,
    mock_patch_experiment_persona_topics,
    mock_httpx_client,
):
    # Setup
    persona_topic_generator = PersonaTopicGenerator()
    mock_client_instance = AsyncMock()
    mock_httpx_client.return_value.__aenter__.return_value = mock_client_instance
    # Mock /api/users/me and /api/experiments/{id} patch
    mock_client_instance.get.return_value.status_code = 200

    def mock_json():
        return {"id": "user1", "organizations": [{"id": "org1"}]}

    mock_client_instance.get.return_value.json = mock_json
    mock_client_instance.patch.return_value.status_code = 200
    mock_client_instance.patch.return_value.text = "ok"
    # Mock generator
    mock_generator = AsyncMock()
    mock_generator.generate.return_value = []
    mock_create_generator.return_value = mock_generator
    # Args
    args = GenerateArgs(
        user_id="user1",
        role="user",
        experiment_id="expid",
        auth_header={"Authorization": "Bearer token"},
        dry_run=True,
        source_data=SourceData(
            docs=DocumentReferences(),
            generation_configuration={},
            evaluation_configuration={},
        ),
        user_description="This is a test user description",
    )
    # Run
    result = await persona_topic_generator.generate(args)
    # Check
    assert isinstance(result, list)
    assert result == []
