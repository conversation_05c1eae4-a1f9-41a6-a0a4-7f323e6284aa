import pytest
import json
import tempfile
import os
from unittest.mock import patch, AsyncMock, MagicMock
from langchain_core.documents import Document
from langchain_community.graphs.graph_document import Node

from src.persona_topic.knowledge_graph_system_prompt.knowledge_graph import (
    graph_to_triplets,
    load_knowledge_graph_from_file,
    process_documents,
    generate_knowledge_graph
)
from src.persona_topic.knowledge_graph_system_prompt.models import (
    Entity, Relationship, Triplet, KnowledgeGraph, EntityResolutionResult
)


class TestGraphToTriplets:
    """Test cases for the graph_to_triplets function."""

    def test_graph_to_triplets_with_relationships(self):
        """Test converting relationships to triplets."""
        nodes = [
            Node(id="Company A"),
            Node(id="Company B")
        ]
        
        relationships = [
            Relationship(
                source_id="Company A",
                target_id="Company B",
                type="partnership"
            ),
            Relationship(
                source_id="Company B",
                target_id="Company A",
                type="collaboration"
            )
        ]
        
        triplets = graph_to_triplets(nodes, relationships)
        
        # Should have 2 triplets from relationships
        relationship_triplets = [t for t in triplets if not t.predicate.startswith("has_")]
        assert len(relationship_triplets) == 2
        
        # Check first triplet
        assert relationship_triplets[0].subject == "Company A"
        assert relationship_triplets[0].predicate == "partnership"
        assert relationship_triplets[0].object == "Company B"
        
        # Check second triplet
        assert relationship_triplets[1].subject == "Company B"
        assert relationship_triplets[1].predicate == "collaboration"
        assert relationship_triplets[1].object == "Company A"

    def test_graph_to_triplets_with_node_properties(self):
        """Test converting node properties to triplets."""
        nodes = [
            Node(id="Company A", properties={"type": "corporation", "sector": "tech"}),
            Node(id="Person B", properties={"role": "CEO", "age": 45})
        ]
        
        relationships = []
        
        triplets = graph_to_triplets(nodes, relationships)
        
        # Should have 4 triplets from node properties
        assert len(triplets) == 4
        
        # Check Company A properties
        company_triplets = [t for t in triplets if t.subject == "Company A"]
        assert len(company_triplets) == 2
        
        type_triplet = next(t for t in company_triplets if t.predicate == "has_type")
        assert type_triplet.object == "corporation"
        
        sector_triplet = next(t for t in company_triplets if t.predicate == "has_sector")
        assert sector_triplet.object == "tech"
        
        # Check Person B properties
        person_triplets = [t for t in triplets if t.subject == "Person B"]
        assert len(person_triplets) == 2
        
        role_triplet = next(t for t in person_triplets if t.predicate == "has_role")
        assert role_triplet.object == "CEO"
        
        age_triplet = next(t for t in person_triplets if t.predicate == "has_age")
        assert age_triplet.object == "45"  # Should be converted to string

    def test_graph_to_triplets_mixed(self):
        """Test converting both relationships and node properties."""
        nodes = [
            Node(id="Alice", properties={"type": "person"}),
            Node(id="Company X", properties={"industry": "finance"})
        ]
        
        relationships = [
            Relationship(
                source_id="Alice",
                target_id="Company X",
                type="works_for"
            )
        ]
        
        triplets = graph_to_triplets(nodes, relationships)
        
        # Should have 3 triplets: 1 relationship + 2 properties
        assert len(triplets) == 3
        
        # Check relationship triplet
        rel_triplets = [t for t in triplets if not t.predicate.startswith("has_")]
        assert len(rel_triplets) == 1
        assert rel_triplets[0].subject == "Alice"
        assert rel_triplets[0].predicate == "works_for"
        assert rel_triplets[0].object == "Company X"
        
        # Check property triplets
        prop_triplets = [t for t in triplets if t.predicate.startswith("has_")]
        assert len(prop_triplets) == 2

    def test_graph_to_triplets_empty(self):
        """Test with empty nodes and relationships."""
        triplets = graph_to_triplets([], [])
        assert triplets == []

    def test_graph_to_triplets_nodes_without_properties(self):
        """Test with nodes that have no properties."""
        nodes = [
            Node(id="Entity A"),
            Node(id="Entity B", properties={})
        ]
        
        relationships = [
            Relationship(
                source_id="Entity A",
                target_id="Entity B",
                type="related_to"
            )
        ]
        
        triplets = graph_to_triplets(nodes, relationships)
        
        # Should only have 1 triplet from the relationship
        assert len(triplets) == 1
        assert triplets[0].subject == "Entity A"
        assert triplets[0].predicate == "related_to"
        assert triplets[0].object == "Entity B"


class TestLoadKnowledgeGraphFromFile:
    """Test cases for the load_knowledge_graph_from_file function."""

    def test_load_knowledge_graph_success(self):
        """Test successful loading of a knowledge graph from file."""
        # Create test data
        test_data = {
            "metadata": {
                "entity_count": 2,
                "relationship_count": 1,
                "triplet_count": 1,
                "entity_types": ["company"],
                "relationship_types": ["partnership"],
                "creation_time": "2023-01-01T00:00:00"
            },
            "entities": [
                {
                    "id": "Company A",
                    "type": "company",
                    "properties": {"sector": "tech"},
                    "relevance": "core",
                    "relevance_score": 0.9
                },
                {
                    "id": "Company B",
                    "type": "company",
                    "properties": {"sector": "finance"},
                    "relevance": "context",
                    "relevance_score": 0.7
                }
            ],
            "relationships": [
                {
                    "source": "Company A",
                    "target": "Company B",
                    "type": "partnership",
                    "properties": {"since": "2020"}
                }
            ],
            "triplets": [
                {
                    "subject": "Company A",
                    "predicate": "partnership",
                    "object": "Company B"
                }
            ]
        }
        
        # Create temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(test_data, f)
            temp_path = f.name
        
        try:
            # Load the knowledge graph
            kg = load_knowledge_graph_from_file(temp_path)
            
            # Verify the loaded data
            assert isinstance(kg, KnowledgeGraph)
            assert kg.entity_count == 2
            assert kg.relationship_count == 1
            assert kg.triplet_count == 1
            
            # Check entities
            assert len(kg.entities) == 2
            assert kg.entities[0].id == "Company A"
            assert kg.entities[0].type == "company"
            assert kg.entities[0].properties == {"sector": "tech"}
            
            # Check relationships
            assert len(kg.relationships) == 1
            assert kg.relationships[0].source_id == "Company A"
            assert kg.relationships[0].target_id == "Company B"
            assert kg.relationships[0].type == "partnership"
            
            # Check triplets
            assert len(kg.triplets) == 1
            assert kg.triplets[0].subject == "Company A"
            assert kg.triplets[0].predicate == "partnership"
            assert kg.triplets[0].object == "Company B"
            
        finally:
            # Clean up
            os.unlink(temp_path)

    def test_load_knowledge_graph_file_not_found(self):
        """Test loading from non-existent file."""
        with pytest.raises(FileNotFoundError, match="Knowledge graph file not found"):
            load_knowledge_graph_from_file("non_existent_file.json")

    def test_load_knowledge_graph_invalid_json(self):
        """Test loading from file with invalid JSON."""
        # Create temporary file with invalid JSON
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            f.write("invalid json content {")
            temp_path = f.name
        
        try:
            with pytest.raises(json.JSONDecodeError):
                load_knowledge_graph_from_file(temp_path)
        finally:
            os.unlink(temp_path)

    def test_load_knowledge_graph_invalid_structure(self):
        """Test loading from file with invalid knowledge graph structure."""
        # Create temporary file with invalid structure that will cause issues
        invalid_data = {"entities": "not_a_list", "relationships": "not_a_list"}

        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(invalid_data, f)
            temp_path = f.name

        try:
            # This should raise an exception when trying to iterate over non-list entities
            with pytest.raises((TypeError, AttributeError, ValueError)):
                load_knowledge_graph_from_file(temp_path)
        finally:
            os.unlink(temp_path)


class TestAsyncKnowledgeGraphFunctions:
    """Test cases for async knowledge graph functions."""

    @pytest.mark.asyncio
    async def test_process_documents_success(self):
        """Test successful document processing."""
        # Create test documents
        documents = [
            Document(page_content="Company A partners with Company B.", metadata={"source": "doc1"}),
            Document(page_content="Company B is a tech company.", metadata={"source": "doc2"})
        ]

        # Mock the dependencies
        mock_nodes = [
            Node(id="Company A", properties={"type": "company"}),
            Node(id="Company B", properties={"type": "company"})
        ]

        mock_relationships = [
            Relationship(source_id="Company A", target_id="Company B", type="partnership")
        ]

        mock_resolution_result = EntityResolutionResult(
            original_count=2,
            merged_count=2,
            mapping={}
        )

        # Mock the LLM transformer
        mock_graph_doc = MagicMock()
        mock_graph_doc.nodes = mock_nodes
        mock_graph_doc.relationships = [
            MagicMock(source=MagicMock(id="Company A"), target=MagicMock(id="Company B"),
                     type="partnership", properties={})
        ]

        mock_transformer = MagicMock()
        mock_transformer.aconvert_to_graph_documents = AsyncMock(return_value=[mock_graph_doc])

        with patch('src.persona_topic.knowledge_graph_system_prompt.knowledge_graph.ChatOpenAI'):
            with patch('src.persona_topic.knowledge_graph_system_prompt.knowledge_graph.LLMGraphTransformer', return_value=mock_transformer):
                with patch('src.persona_topic.knowledge_graph_system_prompt.knowledge_graph.format_prompt_from_template', return_value="mock prompt"):
                    with patch('src.persona_topic.knowledge_graph_system_prompt.knowledge_graph.merge_entities', new_callable=AsyncMock, return_value=(mock_nodes, mock_resolution_result)):
                        with patch('src.persona_topic.knowledge_graph_system_prompt.knowledge_graph.update_relationships', return_value=mock_relationships):

                            merged_nodes, updated_relationships = await process_documents(documents, "test_role", metadata={"test": "data"})

                            # Verify results
                            assert len(merged_nodes) == 2
                            assert len(updated_relationships) == 1
                            assert merged_nodes[0].id == "Company A"
                            assert updated_relationships[0].source_id == "Company A"

    @pytest.mark.asyncio
    async def test_generate_knowledge_graph_success(self):
        """Test successful knowledge graph generation."""
        document_contents = [
            "Apple Inc. is a technology company.",
            "Microsoft Corporation develops software."
        ]

        # Mock the process_documents function
        mock_nodes = [
            Node(id="Apple Inc.", properties={"type": "company", "industry": "technology"}),
            Node(id="Microsoft Corporation", properties={"type": "company", "industry": "software"})
        ]

        mock_relationships = [
            Relationship(source_id="Apple Inc.", target_id="Microsoft Corporation", type="competes_with")
        ]

        with patch('src.persona_topic.knowledge_graph_system_prompt.knowledge_graph.process_documents', new_callable=AsyncMock, return_value=(mock_nodes, mock_relationships)):

            kg = await generate_knowledge_graph(document_contents, "analyst")

            # Verify the knowledge graph
            assert isinstance(kg, KnowledgeGraph)
            assert kg.entity_count == 2
            assert kg.relationship_count == 1
            assert kg.metadata["role"] == "analyst"
            assert kg.metadata["document_count"] == 2

            # Check entities
            assert len(kg.entities) == 2
            assert kg.entities[0].id == "Apple Inc."
            assert kg.entities[0].type == "company"

            # Check relationships
            assert len(kg.relationships) == 1
            assert kg.relationships[0].source_id == "Apple Inc."
            assert kg.relationships[0].target_id == "Microsoft Corporation"

            # Check triplets were created
            assert len(kg.triplets) > 0

    @pytest.mark.asyncio
    async def test_generate_knowledge_graph_with_output_file(self):
        """Test knowledge graph generation with file output."""
        document_contents = ["Test content for knowledge graph generation."]

        # Mock the process_documents function
        mock_nodes = [Node(id="Test Entity", properties={"type": "test"})]
        mock_relationships = []

        with patch('src.persona_topic.knowledge_graph_system_prompt.knowledge_graph.process_documents', new_callable=AsyncMock, return_value=(mock_nodes, mock_relationships)):

            # Create temporary output file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
                temp_path = f.name

            try:
                kg = await generate_knowledge_graph(document_contents, "test_role", output_path=temp_path)

                # Verify the knowledge graph was created
                assert isinstance(kg, KnowledgeGraph)

                # Verify files were created
                assert os.path.exists(temp_path)

                # Verify JSON file content
                with open(temp_path, 'r') as f:
                    saved_data = json.load(f)
                assert "entities" in saved_data
                assert "relationships" in saved_data
                assert "triplets" in saved_data

                # Verify triplets file was created
                triplets_path = temp_path.replace('.json', '_triplets.txt')
                assert os.path.exists(triplets_path)

                # Clean up triplets file
                os.unlink(triplets_path)

            finally:
                # Clean up main file
                if os.path.exists(temp_path):
                    os.unlink(temp_path)

    @pytest.mark.asyncio
    async def test_generate_knowledge_graph_with_metadata(self):
        """Test knowledge graph generation with additional metadata."""
        document_contents = ["Content with metadata."]

        mock_nodes = [Node(id="Entity", properties={})]
        mock_relationships = []

        with patch('src.persona_topic.knowledge_graph_system_prompt.knowledge_graph.process_documents', new_callable=AsyncMock, return_value=(mock_nodes, mock_relationships)):

            custom_metadata = {"experiment_id": "test_123", "version": "1.0"}
            kg = await generate_knowledge_graph(document_contents, "researcher", metadata=custom_metadata)

            # Verify metadata was included
            assert kg.metadata["role"] == "researcher"
            assert kg.metadata["document_count"] == 1
            assert kg.metadata["experiment_id"] == "test_123"
            assert kg.metadata["version"] == "1.0"

    @pytest.mark.asyncio
    async def test_generate_knowledge_graph_empty_documents(self):
        """Test knowledge graph generation with empty document list."""
        document_contents = []

        mock_nodes = []
        mock_relationships = []

        with patch('src.persona_topic.knowledge_graph_system_prompt.knowledge_graph.process_documents', new_callable=AsyncMock, return_value=(mock_nodes, mock_relationships)):

            kg = await generate_knowledge_graph(document_contents, "test_role")

            # Verify empty knowledge graph
            assert isinstance(kg, KnowledgeGraph)
            assert kg.entity_count == 0
            assert kg.relationship_count == 0
            assert kg.triplet_count == 0
            assert kg.metadata["document_count"] == 0
