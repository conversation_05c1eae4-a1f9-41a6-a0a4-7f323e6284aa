import pytest
import json
import tempfile
import os
from unittest.mock import patch, AsyncMock, MagicMock, mock_open
from datetime import datetime
from io import StringIO

from src.persona_topic.knowledge_graph_system_prompt.subgraph_selection import (
    get_node_connections,
    filter_triplets,
    finalize_classifications,
    write_report,
    save_subgraph,
    refine_classifications,
    analyze_intent_pattern,
    classify_nodes_batch,
    classify_nodes,
    create_subgraph
)
from src.persona_topic.knowledge_graph_system_prompt.models import (
    Entity, Relationship, Triplet, KnowledgeGraph, SubGraph, IntentAnalysis
)


class TestGetNodeConnections:
    """Test cases for the get_node_connections function."""

    def test_get_node_connections_outgoing(self):
        """Test getting outgoing connections for a node."""
        triplets = [
            Triplet(subject="Company A", predicate="partners_with", object="Company B"),
            Triplet(subject="Company A", predicate="owns", object="Company C"),
            Triplet(subject="Company D", predicate="competes_with", object="Company E")
        ]
        
        connections = get_node_connections("Company A", triplets)
        
        assert "partners_with" in connections
        assert "owns" in connections
        assert len(connections["partners_with"]) == 1
        assert connections["partners_with"][0] == ("Company B", "outgoing")
        assert connections["owns"][0] == ("Company C", "outgoing")

    def test_get_node_connections_incoming(self):
        """Test getting incoming connections for a node."""
        triplets = [
            Triplet(subject="Company A", predicate="partners_with", object="Company B"),
            Triplet(subject="Company C", predicate="owns", object="Company B"),
            Triplet(subject="Company D", predicate="competes_with", object="Company E")
        ]
        
        connections = get_node_connections("Company B", triplets)
        
        assert "partners_with" in connections
        assert "owns" in connections
        assert connections["partners_with"][0] == ("Company A", "incoming")
        assert connections["owns"][0] == ("Company C", "incoming")

    def test_get_node_connections_bidirectional(self):
        """Test getting both incoming and outgoing connections."""
        triplets = [
            Triplet(subject="Company A", predicate="partners_with", object="Company B"),
            Triplet(subject="Company B", predicate="owns", object="Company C"),
            Triplet(subject="Company D", predicate="partners_with", object="Company B")
        ]
        
        connections = get_node_connections("Company B", triplets)
        
        assert "partners_with" in connections
        assert "owns" in connections
        assert len(connections["partners_with"]) == 2
        assert ("Company A", "incoming") in connections["partners_with"]
        assert ("Company D", "incoming") in connections["partners_with"]
        assert connections["owns"][0] == ("Company C", "outgoing")

    def test_get_node_connections_no_connections(self):
        """Test node with no connections."""
        triplets = [
            Triplet(subject="Company A", predicate="partners_with", object="Company B")
        ]
        
        connections = get_node_connections("Company C", triplets)
        
        assert connections == {}

    def test_get_node_connections_empty_triplets(self):
        """Test with empty triplets list."""
        connections = get_node_connections("Company A", [])
        assert connections == {}


class TestFilterTriplets:
    """Test cases for the filter_triplets function."""

    def test_filter_triplets_relationship_both_kept(self):
        """Test filtering relationship triplets when both nodes are kept."""
        triplets = [
            Triplet(subject="Company A", predicate="partners_with", object="Company B"),
            Triplet(subject="Company C", predicate="owns", object="Company D")
        ]
        nodes_to_keep = {"Company A", "Company B"}
        
        filtered = filter_triplets(triplets, nodes_to_keep)
        
        assert len(filtered) == 1
        assert filtered[0].subject == "Company A"
        assert filtered[0].predicate == "partners_with"
        assert filtered[0].object == "Company B"

    def test_filter_triplets_relationship_one_removed(self):
        """Test filtering relationship triplets when one node is removed."""
        triplets = [
            Triplet(subject="Company A", predicate="partners_with", object="Company B"),
            Triplet(subject="Company C", predicate="owns", object="Company D")
        ]
        nodes_to_keep = {"Company A", "Company C"}
        
        filtered = filter_triplets(triplets, nodes_to_keep)
        
        assert len(filtered) == 0

    def test_filter_triplets_property_kept(self):
        """Test filtering property triplets (has_ prefix)."""
        triplets = [
            Triplet(subject="Company A", predicate="has_type", object="corporation"),
            Triplet(subject="Company B", predicate="has_sector", object="technology"),
            Triplet(subject="Company A", predicate="partners_with", object="Company B")
        ]
        nodes_to_keep = {"Company A"}
        
        filtered = filter_triplets(triplets, nodes_to_keep)
        
        assert len(filtered) == 1
        assert filtered[0].subject == "Company A"
        assert filtered[0].predicate == "has_type"
        assert filtered[0].object == "corporation"

    def test_filter_triplets_mixed(self):
        """Test filtering mixed relationship and property triplets."""
        triplets = [
            Triplet(subject="Company A", predicate="has_type", object="corporation"),
            Triplet(subject="Company A", predicate="partners_with", object="Company B"),
            Triplet(subject="Company B", predicate="has_sector", object="technology"),
            Triplet(subject="Company C", predicate="owns", object="Company D")
        ]
        nodes_to_keep = {"Company A", "Company B"}
        
        filtered = filter_triplets(triplets, nodes_to_keep)
        
        assert len(filtered) == 3
        # Should include: Company A has_type, Company A partners_with Company B, Company B has_sector
        subjects = [t.subject for t in filtered]
        predicates = [t.predicate for t in filtered]
        assert "Company A" in subjects
        assert "Company B" in subjects
        assert "has_type" in predicates
        assert "partners_with" in predicates
        assert "has_sector" in predicates

    def test_filter_triplets_empty(self):
        """Test filtering with empty inputs."""
        assert filter_triplets([], set()) == []
        assert filter_triplets([], {"Company A"}) == []
        
        triplets = [Triplet(subject="Company A", predicate="has_type", object="corporation")]
        assert filter_triplets(triplets, set()) == []


class TestFinalizeClassifications:
    """Test cases for the finalize_classifications function."""

    def test_finalize_classifications_basic(self):
        """Test basic finalization of classifications."""
        classifications = {
            "core": {"Company B", "Company A"},
            "context": {"Company D", "Company C"},
            "irrelevant": {"Company F", "Company E"}
        }
        
        result = finalize_classifications(classifications)
        
        assert result["core"] == ["Company A", "Company B"]  # Sorted
        assert result["context"] == ["Company C", "Company D"]  # Sorted
        assert result["irrelevant"] == ["Company E", "Company F"]  # Sorted

    def test_finalize_classifications_empty_categories(self):
        """Test finalization with empty categories."""
        classifications = {
            "core": {"Company A"},
            "context": set(),
            "irrelevant": {"Company B"}
        }
        
        result = finalize_classifications(classifications)
        
        assert result["core"] == ["Company A"]
        assert result["context"] == []
        assert result["irrelevant"] == ["Company B"]

    def test_finalize_classifications_all_empty(self):
        """Test finalization with all empty categories."""
        classifications = {
            "core": set(),
            "context": set(),
            "irrelevant": set()
        }
        
        result = finalize_classifications(classifications)
        
        assert result["core"] == []
        assert result["context"] == []
        assert result["irrelevant"] == []


class TestWriteReport:
    """Test cases for the write_report function."""

    def test_write_report_basic(self):
        """Test basic report writing."""
        subgraph_dict = {
            "metadata": {
                "experiment_intent": "Test experiment intent",
                "intent_specificity": "SPECIFIC",
                "intent_pattern": "process",
                "approach": "intent-classification",
                "creation_time": "2023-01-01T00:00:00",
                "entity_count": 10,
                "core_entity_count": 5,
                "context_entity_count": 5,
                "original_graph_nodes": 20,
                "removed_nodes_count": 10,
                "reduction_percentage": "50.0%",
                "node_categories": {
                    "core": ["Company A", "Company B"],
                    "context": ["Company C", "Company D"]
                },
                "removed_nodes": ["Company E", "Company F"],
                "intent_analysis": {
                    "pattern": "process",
                    "key_concepts": ["efficiency", "optimization"],
                    "classification_criteria": {
                        "core": "Directly mentioned",
                        "context": "Related concepts"
                    },
                    "important_relationships": ["improves", "optimizes"]
                }
            }
        }
        
        output = StringIO()
        write_report(output, subgraph_dict)
        report_content = output.getvalue()
        
        # Check that key sections are present
        assert "Intent-Driven Classification Subgraph Report" in report_content
        assert "Test experiment intent" in report_content
        assert "SPECIFIC" in report_content
        assert "process" in report_content
        assert "INTENT ANALYSIS" in report_content
        assert "STATISTICS" in report_content
        assert "CORE NODES" in report_content
        assert "CONTEXT NODES" in report_content
        assert "REMOVED NODES SUMMARY" in report_content
        assert "Company A" in report_content
        assert "efficiency" in report_content

    def test_write_report_minimal_metadata(self):
        """Test report writing with minimal metadata."""
        subgraph_dict = {
            "metadata": {
                "experiment_intent": "Minimal test",
                "approach": "intent-classification",
                "creation_time": "2023-01-01T00:00:00",
                "entity_count": 5,
                "core_entity_count": 2,
                "context_entity_count": 3
            }
        }
        
        output = StringIO()
        write_report(output, subgraph_dict)
        report_content = output.getvalue()
        
        assert "Minimal test" in report_content
        assert "STATISTICS" in report_content
        assert "Selected nodes: 5" in report_content


class TestRefineClassifications:
    """Test cases for the refine_classifications function."""

    def test_refine_classifications_specific_intent(self):
        """Test refinement with specific intent."""
        initial_classifications = {
            "core": {"Company A"},
            "context": {"Company B"},
            "irrelevant": {"Company C"}
        }

        triplets = [
            Triplet(subject="Company A", predicate="partners_with", object="Company C"),
            Triplet(subject="Company B", predicate="has_type", object="corporation")
        ]

        intent_analysis = IntentAnalysis(
            pattern="process",
            key_concepts=["efficiency"],
            classification_criteria={},
            important_relationships=["partners_with"],
            intent_specificity="SPECIFIC"
        )

        entities = [
            Entity(id="Company A", relevance="core"),
            Entity(id="Company B", relevance="context"),
            Entity(id="Company C", relevance="irrelevant")
        ]

        refined = refine_classifications(initial_classifications, triplets, intent_analysis, entities)

        # Company C should be promoted from irrelevant to context due to important relationship
        assert "Company A" in refined["core"]
        assert "Company B" in refined["context"]
        assert "Company C" in refined["context"]  # Promoted
        assert "Company C" not in refined["irrelevant"]

    def test_refine_classifications_general_intent(self):
        """Test refinement with general intent."""
        initial_classifications = {
            "core": {"Company A"},
            "context": set(),
            "irrelevant": {"Company B"}
        }

        triplets = [
            Triplet(subject="Company A", predicate="owns", object="Company B")
        ]

        intent_analysis = IntentAnalysis(
            pattern="general",
            key_concepts=[],
            classification_criteria={},
            important_relationships=[],
            intent_specificity="GENERAL"
        )

        entities = [
            Entity(id="Company A", relevance="core"),
            Entity(id="Company B", relevance="irrelevant")
        ]

        refined = refine_classifications(initial_classifications, triplets, intent_analysis, entities)

        # Company B should be promoted due to general intent being more inclusive
        assert "Company A" in refined["core"]
        assert "Company B" in refined["context"]  # Promoted
        assert "Company B" not in refined["irrelevant"]

    def test_refine_classifications_behavioral_intent(self):
        """Test refinement with behavioral intent."""
        initial_classifications = {
            "core": {"Company A"},
            "context": {"Company B"},
            "irrelevant": {"Company C"}
        }

        triplets = [
            Triplet(subject="Company A", predicate="influences", object="Company B"),
            Triplet(subject="Company A", predicate="affects", object="Company C")
        ]

        intent_analysis = IntentAnalysis(
            pattern="behavioral",
            key_concepts=["behavior"],
            classification_criteria={},
            important_relationships=["influences"],
            intent_specificity="BEHAVIORAL"
        )

        entities = [
            Entity(id="Company A", relevance="core"),
            Entity(id="Company B", relevance="context"),
            Entity(id="Company C", relevance="irrelevant")
        ]

        refined = refine_classifications(initial_classifications, triplets, intent_analysis, entities)

        # For behavioral intents, should promote more aggressively
        assert "Company A" in refined["core"]
        # Company B might be promoted to core, Company C to context
        assert len(refined["core"]) >= 1
        assert len(refined["context"]) >= 1

    def test_refine_classifications_no_connections(self):
        """Test refinement when there are no relevant connections."""
        initial_classifications = {
            "core": {"Company A"},
            "context": {"Company B"},
            "irrelevant": {"Company C"}
        }

        triplets = [
            Triplet(subject="Company D", predicate="unrelated", object="Company E")
        ]

        intent_analysis = IntentAnalysis(
            pattern="process",
            key_concepts=[],
            classification_criteria={},
            important_relationships=["partners_with"],
            intent_specificity="SPECIFIC"
        )

        entities = [
            Entity(id="Company A", relevance="core"),
            Entity(id="Company B", relevance="context"),
            Entity(id="Company C", relevance="irrelevant")
        ]

        refined = refine_classifications(initial_classifications, triplets, intent_analysis, entities)

        # Should remain unchanged
        assert refined["core"] == {"Company A"}
        assert refined["context"] == {"Company B"}
        assert refined["irrelevant"] == {"Company C"}


class TestAsyncSubgraphFunctions:
    """Test cases for async subgraph selection functions."""

    @pytest.mark.asyncio
    async def test_analyze_intent_pattern_success(self):
        """Test successful intent pattern analysis."""
        mock_response = json.dumps({
            "pattern": "process",
            "key_concepts": ["efficiency", "optimization", "workflow"],
            "classification_criteria": {
                "core": "Directly involved in the process",
                "context": "Supporting the process",
                "irrelevant": "Unrelated to the process"
            },
            "important_relationships": ["improves", "optimizes", "streamlines"],
            "intent_specificity": "SPECIFIC"
        })

        with patch('src.persona_topic.knowledge_graph_system_prompt.subgraph_selection.format_prompt_from_template', return_value="mock prompt"):
            with patch('src.persona_topic.knowledge_graph_system_prompt.subgraph_selection.achat_completion', new_callable=AsyncMock, return_value=mock_response):
                with patch('src.persona_topic.knowledge_graph_system_prompt.subgraph_selection.extract_json_from_text', return_value=json.loads(mock_response)):

                    result = await analyze_intent_pattern(
                        "How can we improve our workflow efficiency?",
                        "A project management software",
                        {"test": "metadata"}
                    )

                    assert isinstance(result, IntentAnalysis)
                    assert result.pattern == "process"
                    assert "efficiency" in result.key_concepts
                    assert "optimization" in result.key_concepts
                    assert result.intent_specificity == "SPECIFIC"
                    assert "improves" in result.important_relationships

    @pytest.mark.asyncio
    async def test_analyze_intent_pattern_error_handling(self):
        """Test intent pattern analysis error handling."""
        with patch('src.persona_topic.knowledge_graph_system_prompt.subgraph_selection.format_prompt_from_template', return_value="mock prompt"):
            with patch('src.persona_topic.knowledge_graph_system_prompt.subgraph_selection.achat_completion', new_callable=AsyncMock, side_effect=Exception("API Error")):

                result = await analyze_intent_pattern("Test intent")

                # Should return default analysis on error
                assert isinstance(result, IntentAnalysis)
                assert result.pattern == "unknown"
                assert result.key_concepts == []
                assert result.intent_specificity == "GENERAL"

    @pytest.mark.asyncio
    async def test_classify_nodes_batch_success(self):
        """Test successful batch node classification."""
        entities = [
            Entity(id="Company A", properties={"type": "corporation"}),
            Entity(id="Company B", properties={"type": "startup"}),
            Entity(id="Company C", properties={"type": "nonprofit"})
        ]

        intent_analysis = IntentAnalysis(
            pattern="business",
            key_concepts=["profit", "growth"],
            classification_criteria={},
            important_relationships=[],
            intent_specificity="GENERAL"
        )

        mock_response = json.dumps({
            "core": ["Company A"],
            "context": ["Company B"],
            "irrelevant": ["Company C"]
        })

        with patch('src.persona_topic.knowledge_graph_system_prompt.subgraph_selection.format_prompt_from_template', return_value="mock prompt"):
            with patch('src.persona_topic.knowledge_graph_system_prompt.subgraph_selection.achat_completion', new_callable=AsyncMock, return_value=mock_response):
                with patch('src.persona_topic.knowledge_graph_system_prompt.subgraph_selection.extract_json_from_text', return_value=json.loads(mock_response)):

                    result = await classify_nodes_batch(entities, intent_analysis, {"test": "metadata"})

                    assert result["core"] == ["Company A"]
                    assert result["context"] == ["Company B"]
                    assert result["irrelevant"] == ["Company C"]

                    # Check that entity relevance was updated
                    assert entities[0].relevance == "core"
                    assert entities[0].relevance_score == 1.0
                    assert entities[1].relevance == "context"
                    assert entities[1].relevance_score == 0.5
                    assert entities[2].relevance == "irrelevant"
                    assert entities[2].relevance_score == 0.0

    @pytest.mark.asyncio
    async def test_classify_nodes_batch_missing_nodes(self):
        """Test batch classification when some nodes are missing from response."""
        entities = [
            Entity(id="Company A"),
            Entity(id="Company B"),
            Entity(id="Company C")
        ]

        intent_analysis = IntentAnalysis(
            pattern="test",
            key_concepts=[],
            classification_criteria={},
            important_relationships=[],
            intent_specificity="GENERAL"
        )

        # Response missing Company C
        mock_response = json.dumps({
            "core": ["Company A"],
            "context": ["Company B"]
        })

        with patch('src.persona_topic.knowledge_graph_system_prompt.subgraph_selection.format_prompt_from_template', return_value="mock prompt"):
            with patch('src.persona_topic.knowledge_graph_system_prompt.subgraph_selection.achat_completion', new_callable=AsyncMock, return_value=mock_response):
                with patch('src.persona_topic.knowledge_graph_system_prompt.subgraph_selection.extract_json_from_text', return_value=json.loads(mock_response)):

                    result = await classify_nodes_batch(entities, intent_analysis)

                    assert result["core"] == ["Company A"]
                    assert result["context"] == ["Company B"]
                    assert "Company C" in result["irrelevant"]  # Should be added to irrelevant

    @pytest.mark.asyncio
    async def test_classify_nodes_batch_error_handling(self):
        """Test batch classification error handling."""
        entities = [Entity(id="Company A")]
        intent_analysis = IntentAnalysis(
            pattern="test",
            key_concepts=[],
            classification_criteria={},
            important_relationships=[],
            intent_specificity="GENERAL"
        )

        with patch('src.persona_topic.knowledge_graph_system_prompt.subgraph_selection.format_prompt_from_template', return_value="mock prompt"):
            with patch('src.persona_topic.knowledge_graph_system_prompt.subgraph_selection.achat_completion', new_callable=AsyncMock, side_effect=Exception("API Error")):

                result = await classify_nodes_batch(entities, intent_analysis)

                # Should return all nodes as irrelevant on error
                assert result["core"] == []
                assert result["context"] == []
                assert result["irrelevant"] == ["Company A"]

    @pytest.mark.asyncio
    async def test_classify_nodes_integration(self):
        """Test the main classify_nodes function with batch processing."""
        entities = [
            Entity(id="Company A"),
            Entity(id="Company B"),
            Entity(id="Company C"),
            Entity(id="Company D")
        ]

        intent_analysis = IntentAnalysis(
            pattern="test",
            key_concepts=[],
            classification_criteria={},
            important_relationships=[],
            intent_specificity="GENERAL"
        )

        # Mock batch_gather to return batch results
        mock_batch_results = [
            {"core": ["Company A"], "context": ["Company B"], "irrelevant": []},
            {"core": [], "context": ["Company C"], "irrelevant": ["Company D"]}
        ]

        with patch('src.persona_topic.knowledge_graph_system_prompt.subgraph_selection.batch_gather', new_callable=AsyncMock, return_value=mock_batch_results):

            result = await classify_nodes(entities, intent_analysis, batch_size=2)

            assert "Company A" in result["core"]
            assert "Company B" in result["context"]
            assert "Company C" in result["context"]
            assert "Company D" in result["irrelevant"]

    @pytest.mark.asyncio
    async def test_create_subgraph_integration(self):
        """Test the main create_subgraph function."""
        # Create test knowledge graph
        entities = [
            Entity(id="Company A", properties={"type": "corporation"}),
            Entity(id="Company B", properties={"type": "startup"}),
            Entity(id="Company C", properties={"type": "nonprofit"})
        ]

        triplets = [
            Triplet(subject="Company A", predicate="partners_with", object="Company B"),
            Triplet(subject="Company A", predicate="has_type", object="corporation"),
            Triplet(subject="Company B", predicate="has_type", object="startup")
        ]

        kg = KnowledgeGraph(
            entities=entities,
            relationships=[],
            triplets=triplets,
            metadata={"test": "data"}
        )

        # Mock the async functions
        mock_intent_analysis = IntentAnalysis(
            pattern="business",
            key_concepts=["partnership"],
            classification_criteria={},
            important_relationships=["partners_with"],
            intent_specificity="SPECIFIC"
        )

        mock_classifications = {
            "core": {"Company A"},
            "context": {"Company B"},
            "irrelevant": {"Company C"}
        }

        with patch('src.persona_topic.knowledge_graph_system_prompt.subgraph_selection.analyze_intent_pattern', new_callable=AsyncMock, return_value=mock_intent_analysis):
            with patch('src.persona_topic.knowledge_graph_system_prompt.subgraph_selection.classify_nodes', new_callable=AsyncMock, return_value=mock_classifications):

                subgraph = await create_subgraph(
                    kg,
                    "How can we improve business partnerships?",
                    "A business management platform",
                    {"test": "metadata"}
                )

                assert isinstance(subgraph, SubGraph)
                assert len(subgraph.entities) == 2  # Company A and B
                assert subgraph.metadata["experiment_intent"] == "How can we improve business partnerships?"
                assert subgraph.metadata["intent_pattern"] == "business"
                assert subgraph.metadata["intent_specificity"] == "SPECIFIC"
                assert len(subgraph.focus_entities) == 1  # Company A (core)


class TestSaveSubgraph:
    """Test cases for the save_subgraph function."""

    def test_save_subgraph_basic(self):
        """Test basic subgraph saving functionality."""
        # Create test subgraph
        entities = [Entity(id="Company A", properties={"type": "corporation"})]
        subgraph = SubGraph(
            entities=entities,
            relationships=[],
            relevance_score=0.9,
            focus_entities=["Company A"],
            metadata={
                "experiment_intent": "Test saving functionality",
                "creation_time": "2023-01-01T00:00:00",
                "entity_count": 1,
                "core_entity_count": 1,
                "context_entity_count": 0,
                "approach": "intent-classification"
            }
        )

        with tempfile.TemporaryDirectory() as temp_dir:
            # Mock the to_dict method
            mock_dict = {
                "entities": [{"id": "Company A", "type": None, "properties": {"type": "corporation"}}],
                "metadata": subgraph.metadata
            }

            with patch.object(subgraph, 'to_dict', return_value=mock_dict):
                output_path = save_subgraph(subgraph, output_dir=temp_dir)

                # Verify files were created
                assert os.path.exists(output_path)
                assert output_path.endswith('.json')

                # Verify JSON content
                with open(output_path, 'r') as f:
                    saved_data = json.load(f)
                assert "entities" in saved_data
                assert "metadata" in saved_data

                # Verify report was created
                report_path = output_path.replace('.json', '_report.txt')
                assert os.path.exists(report_path)

    def test_save_subgraph_with_custom_path(self):
        """Test saving subgraph with custom output path."""
        entities = [Entity(id="Test Entity")]
        subgraph = SubGraph(
            entities=entities,
            relationships=[],
            relevance_score=1.0,
            focus_entities=["Test Entity"],
            metadata={
                "experiment_intent": "Custom path test",
                "creation_time": "2023-01-01T00:00:00",
                "entity_count": 1,
                "core_entity_count": 1,
                "context_entity_count": 0,
                "approach": "intent-classification"
            }
        )

        with tempfile.TemporaryDirectory() as temp_dir:
            custom_path = os.path.join(temp_dir, "custom_subgraph.json")

            mock_dict = {
                "entities": [{"id": "Test Entity"}],
                "metadata": subgraph.metadata
            }

            with patch.object(subgraph, 'to_dict', return_value=mock_dict):
                output_path = save_subgraph(subgraph, output_dir=temp_dir, output_path=custom_path)

                assert output_path == custom_path
                assert os.path.exists(custom_path)

    def test_save_subgraph_directory_creation(self):
        """Test that output directory is created if it doesn't exist."""
        entities = [Entity(id="Test Entity")]
        subgraph = SubGraph(
            entities=entities,
            relationships=[],
            relevance_score=1.0,
            focus_entities=["Test Entity"],
            metadata={
                "experiment_intent": "Directory creation test",
                "creation_time": "2023-01-01T00:00:00",
                "entity_count": 1,
                "core_entity_count": 1,
                "context_entity_count": 0,
                "approach": "intent-classification"
            }
        )

        with tempfile.TemporaryDirectory() as temp_dir:
            new_dir = os.path.join(temp_dir, "new_subgraph_dir")
            assert not os.path.exists(new_dir)

            mock_dict = {
                "entities": [{"id": "Test Entity"}],
                "metadata": subgraph.metadata
            }

            with patch.object(subgraph, 'to_dict', return_value=mock_dict):
                output_path = save_subgraph(subgraph, output_dir=new_dir)

                assert os.path.exists(new_dir)
                assert os.path.exists(output_path)
