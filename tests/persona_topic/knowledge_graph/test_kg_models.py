import pytest
from datetime import datetime
from src.persona_topic.knowledge_graph_system_prompt.models import (
    Entity, Relationship, Triplet, EntityResolutionResult
)


class TestTriplet:
    """Test cases for the Triplet class."""

    def test_triplet_creation(self):
        """Test that a triplet can be created with required fields."""
        triplet = Triplet(
            subject="Alice",
            predicate="knows",
            object="Bob"
        )
        
        assert triplet.subject == "Alice"
        assert triplet.predicate == "knows"
        assert triplet.object == "Bob"

    def test_triplet_to_dict(self):
        """Test that triplet can be converted to dictionary."""
        triplet = Triplet(
            subject="Company A",
            predicate="acquired",
            object="Company B"
        )
        
        expected_dict = {
            "subject": "Company A",
            "predicate": "acquired",
            "object": "Company B"
        }
        
        assert triplet.to_dict() == expected_dict

    def test_triplet_str_representation(self):
        """Test the string representation of a triplet."""
        triplet = Triplet(
            subject="Python",
            predicate="is_a",
            object="programming_language"
        )
        
        expected_str = "(Python) --[is_a]--> (programming_language)"
        assert str(triplet) == expected_str


class TestEntity:
    """Test cases for the Entity class."""

    def test_entity_creation_minimal(self):
        """Test entity creation with only required fields."""
        entity = Entity(id="entity_1")
        
        assert entity.id == "entity_1"
        assert entity.type is None
        assert entity.properties == {}
        assert entity.relevance is None
        assert entity.relevance_score == 0.0

    def test_entity_creation_full(self):
        """Test entity creation with all fields."""
        properties = {"name": "Alice", "age": 30}
        entity = Entity(
            id="person_1",
            type="Person",
            properties=properties,
            relevance="core",
            relevance_score=0.9
        )
        
        assert entity.id == "person_1"
        assert entity.type == "Person"
        assert entity.properties == properties
        assert entity.relevance == "core"
        assert entity.relevance_score == 0.9

    def test_entity_to_dict(self):
        """Test entity conversion to dictionary."""
        entity = Entity(
            id="company_1",
            type="Company",
            properties={"name": "TechCorp", "employees": 100},
            relevance="context",
            relevance_score=0.7
        )
        
        expected_dict = {
            "id": "company_1",
            "type": "Company",
            "properties": {"name": "TechCorp", "employees": 100},
            "relevance": "context",
            "relevance_score": 0.7
        }
        
        assert entity.to_dict() == expected_dict


class TestRelationship:
    """Test cases for the Relationship class."""

    def test_relationship_creation_minimal(self):
        """Test relationship creation with required fields only."""
        relationship = Relationship(
            source_id="entity_1",
            target_id="entity_2",
            type="knows"
        )
        
        assert relationship.source_id == "entity_1"
        assert relationship.target_id == "entity_2"
        assert relationship.type == "knows"
        assert relationship.properties == {}

    def test_relationship_creation_with_properties(self):
        """Test relationship creation with properties."""
        properties = {"since": "2020", "strength": "strong"}
        relationship = Relationship(
            source_id="alice",
            target_id="bob",
            type="friendship",
            properties=properties
        )
        
        assert relationship.source_id == "alice"
        assert relationship.target_id == "bob"
        assert relationship.type == "friendship"
        assert relationship.properties == properties

    def test_relationship_to_dict(self):
        """Test relationship conversion to dictionary."""
        relationship = Relationship(
            source_id="company_a",
            target_id="company_b",
            type="partnership",
            properties={"duration": "5 years", "type": "strategic"}
        )
        
        expected_dict = {
            "source": "company_a",
            "target": "company_b",
            "type": "partnership",
            "properties": {"duration": "5 years", "type": "strategic"}
        }
        
        assert relationship.to_dict() == expected_dict


class TestEntityResolutionResult:
    """Test cases for the EntityResolutionResult class."""

    def test_entity_resolution_result_creation(self):
        """Test creation of EntityResolutionResult."""
        mapping = {"entity_1": "merged_entity_1", "entity_2": "merged_entity_1"}
        result = EntityResolutionResult(
            original_count=10,
            merged_count=8,
            mapping=mapping
        )
        
        assert result.original_count == 10
        assert result.merged_count == 8
        assert result.mapping == mapping

    def test_reduction_count_property(self):
        """Test the reduction_count property calculation."""
        result = EntityResolutionResult(
            original_count=15,
            merged_count=12
        )
        
        assert result.reduction_count == 3

    def test_reduction_percentage_property(self):
        """Test the reduction_percentage property calculation."""
        result = EntityResolutionResult(
            original_count=20,
            merged_count=15
        )
        
        expected_percentage = (5 / 20) * 100  # 25.0
        assert result.reduction_percentage == expected_percentage

    def test_reduction_percentage_zero_original(self):
        """Test reduction_percentage when original_count is zero."""
        result = EntityResolutionResult(
            original_count=0,
            merged_count=0
        )
        
        assert result.reduction_percentage == 0.0

    def test_entity_resolution_result_to_dict(self):
        """Test conversion to dictionary."""
        mapping = {"old_1": "new_1", "old_2": "new_1"}
        result = EntityResolutionResult(
            original_count=10,
            merged_count=8,
            mapping=mapping
        )
        
        expected_dict = {
            "original_count": 10,
            "merged_count": 8,
            "reduction_count": 2,
            "reduction_percentage": 20.0,
            "mapping": mapping
        }
        
        assert result.to_dict() == expected_dict
