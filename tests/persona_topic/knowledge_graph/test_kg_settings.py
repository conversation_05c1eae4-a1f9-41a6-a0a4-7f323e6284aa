import pytest
from src.persona_topic.knowledge_graph_system_prompt.settings import KnowledgeGraphSystemPromptGeneratorSettings


class TestKnowledgeGraphSystemPromptGeneratorSettings:
    """Test cases for KnowledgeGraphSystemPromptGeneratorSettings."""

    def test_default_values(self):
        """Test that default values are set correctly."""
        settings = KnowledgeGraphSystemPromptGeneratorSettings()
        
        assert settings.max_personas == 5
        assert settings.max_topics_per_persona == 5
        assert settings.max_tokens == 500
        assert settings.temperature == 0.7
        assert settings.model == "gpt-4.1-mini"
        assert settings.include_context is True
        assert settings.additional_settings is None

    def test_custom_values(self):
        """Test that custom values can be set."""
        custom_settings = {
            "max_personas": 10,
            "max_topics_per_persona": 8,
            "max_tokens": 1000,
            "temperature": 0.5,
            "model": "gpt-4",
            "include_context": False,
            "additional_settings": {"custom_key": "custom_value"}
        }
        
        settings = KnowledgeGraphSystemPromptGeneratorSettings(**custom_settings)
        
        assert settings.max_personas == 10
        assert settings.max_topics_per_persona == 8
        assert settings.max_tokens == 1000
        assert settings.temperature == 0.5
        assert settings.model == "gpt-4"
        assert settings.include_context is False
        assert settings.additional_settings == {"custom_key": "custom_value"}

    def test_partial_custom_values(self):
        """Test that partial custom values work with defaults."""
        settings = KnowledgeGraphSystemPromptGeneratorSettings(
            max_personas=15,
            temperature=0.9
        )
        
        # Custom values
        assert settings.max_personas == 15
        assert settings.temperature == 0.9
        
        # Default values should remain
        assert settings.max_topics_per_persona == 5
        assert settings.max_tokens == 500
        assert settings.model == "gpt-4.1-mini"
        assert settings.include_context is True
        assert settings.additional_settings is None

    def test_field_validation(self):
        """Test that field validation works correctly."""
        # Test that negative values are handled (Pydantic should allow them unless we add validators)
        settings = KnowledgeGraphSystemPromptGeneratorSettings(max_personas=-1)
        assert settings.max_personas == -1
        
        # Test that string values for numeric fields raise validation errors
        with pytest.raises(ValueError):
            KnowledgeGraphSystemPromptGeneratorSettings(max_personas="invalid")
        
        with pytest.raises(ValueError):
            KnowledgeGraphSystemPromptGeneratorSettings(temperature="invalid")

    def test_model_serialization(self):
        """Test that the model can be serialized to dict."""
        settings = KnowledgeGraphSystemPromptGeneratorSettings(
            max_personas=10,
            additional_settings={"test": "value"}
        )
        
        settings_dict = settings.model_dump()
        
        expected_dict = {
            "max_personas": 10,
            "max_topics_per_persona": 5,
            "max_tokens": 500,
            "temperature": 0.7,
            "model": "gpt-4.1-mini",
            "include_context": True,
            "additional_settings": {"test": "value"}
        }
        
        assert settings_dict == expected_dict
