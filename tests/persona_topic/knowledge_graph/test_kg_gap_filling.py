import pytest
import asyncio
from unittest.mock import patch, AsyncMock, MagicMock
from datetime import datetime
from typing import Dict, List, Set

from src.persona_topic.knowledge_graph_system_prompt.gap_filling import (
    identify_islands,
    match_topics_to_nodes,
    allocate_topics,
    generate_topics_for_island,
    process_island_parallel,
    generate_gap_filling_topics
)
from src.persona_topic.knowledge_graph_system_prompt.gap_filling_models import (
    Island,
    TopicAllocation,
    IslandProcessingParams,
    IslandResult,
    GapFillingResult
)
from src.persona_topic.knowledge_graph_system_prompt.models import (
    Entity,
    Relationship,
    Triplet,
    SubGraph
)


class TestIslandModel:
    """Test cases for the Island data model."""

    def test_island_creation_minimal(self):
        """Test island creation with minimal data."""
        nodes = {"node1", "node2", "node3"}
        island = Island(nodes=nodes)
        
        assert island.nodes == nodes
        assert island.core_nodes == set()
        assert island.context_nodes == set()
        assert island.triplets == []
        assert island.coverage == 0.0
        assert island.index == 0

    def test_island_creation_full(self):
        """Test island creation with all data."""
        nodes = {"node1", "node2", "node3"}
        core_nodes = {"node1", "node2"}
        context_nodes = {"node3"}
        triplets = [
            Triplet(subject="node1", predicate="relates_to", object="node2")
        ]
        
        island = Island(
            nodes=nodes,
            core_nodes=core_nodes,
            context_nodes=context_nodes,
            triplets=triplets,
            coverage=0.75,
            index=1
        )
        
        assert island.nodes == nodes
        assert island.core_nodes == core_nodes
        assert island.context_nodes == context_nodes
        assert island.triplets == triplets
        assert island.coverage == 0.75
        assert island.index == 1

    def test_island_properties(self):
        """Test island computed properties."""
        nodes = {"node1", "node2", "node3", "node4"}
        core_nodes = {"node1", "node2"}
        context_nodes = {"node3", "node4"}
        
        island = Island(
            nodes=nodes,
            core_nodes=core_nodes,
            context_nodes=context_nodes
        )
        
        assert island.size == 4
        assert island.core_node_count == 2
        assert island.context_node_count == 2


class TestTopicAllocation:
    """Test cases for the TopicAllocation data model."""

    def test_topic_allocation_creation(self):
        """Test topic allocation creation."""
        allocation = TopicAllocation(
            island_index=0,
            island_size=5,
            allocated_topics=3,
            coverage=0.6,
            weight=0.8
        )
        
        assert allocation.island_index == 0
        assert allocation.island_size == 5
        assert allocation.allocated_topics == 3
        assert allocation.coverage == 0.6
        assert allocation.weight == 0.8

    def test_topic_allocation_default_weight(self):
        """Test topic allocation with default weight."""
        allocation = TopicAllocation(
            island_index=1,
            island_size=3,
            allocated_topics=2,
            coverage=0.4
        )
        
        assert allocation.weight == 0.0


class TestIslandProcessingParams:
    """Test cases for the IslandProcessingParams data model."""

    def test_island_processing_params_creation(self):
        """Test island processing params creation."""
        island = Island(nodes={"node1", "node2"})
        node_topic_map = {"node1": ["topic1"], "node2": ["topic2"]}
        
        params = IslandProcessingParams(
            island_idx=0,
            island=island,
            num_topics=3,
            node_topic_map=node_topic_map,
            experiment_intent="test intent",
            product_description="test product"
        )
        
        assert params.island_idx == 0
        assert params.island == island
        assert params.num_topics == 3
        assert params.node_topic_map == node_topic_map
        assert params.experiment_intent == "test intent"
        assert params.product_description == "test product"

    def test_island_processing_params_no_product_description(self):
        """Test island processing params without product description."""
        island = Island(nodes={"node1"})
        
        params = IslandProcessingParams(
            island_idx=0,
            island=island,
            num_topics=2,
            node_topic_map={},
            experiment_intent="test intent"
        )
        
        assert params.product_description is None


class TestIslandResult:
    """Test cases for the IslandResult data model."""

    def test_island_result_creation(self):
        """Test island result creation."""
        result = IslandResult(
            island_index=0,
            core_nodes=["node1", "node2"],
            all_nodes=["node1", "node2", "node3"],
            new_topics=["topic1", "topic2"],
            node_count=3,
            core_node_count=2
        )
        
        assert result.island_index == 0
        assert result.core_nodes == ["node1", "node2"]
        assert result.all_nodes == ["node1", "node2", "node3"]
        assert result.new_topics == ["topic1", "topic2"]
        assert result.node_count == 3
        assert result.core_node_count == 2

    def test_island_result_to_dict(self):
        """Test island result conversion to dictionary."""
        result = IslandResult(
            island_index=1,
            core_nodes=["core1"],
            all_nodes=["core1", "context1"],
            new_topics=["new_topic"],
            node_count=2,
            core_node_count=1
        )
        
        expected_dict = {
            "island_index": 1,
            "core_nodes": ["core1"],
            "all_nodes": ["core1", "context1"],
            "new_topics": ["new_topic"],
            "node_count": 2,
            "core_node_count": 1
        }
        
        assert result.to_dict() == expected_dict


class TestGapFillingResult:
    """Test cases for the GapFillingResult data model."""

    def test_gap_filling_result_creation(self):
        """Test gap filling result creation."""
        island_result = IslandResult(
            island_index=0,
            core_nodes=["node1"],
            all_nodes=["node1", "node2"],
            new_topics=["topic1", "topic2"],
            node_count=2,
            core_node_count=1
        )
        
        result = GapFillingResult(
            new_topics=["topic1", "topic2"],
            islands=[island_result],
            total_new_topics=2,
            total_islands=1,
            total_core_nodes=1
        )
        
        assert result.new_topics == ["topic1", "topic2"]
        assert len(result.islands) == 1
        assert result.total_new_topics == 2
        assert result.total_islands == 1
        assert result.total_core_nodes == 1
        assert isinstance(result.creation_time, datetime)

    def test_gap_filling_result_to_dict(self):
        """Test gap filling result conversion to dictionary."""
        island_result = IslandResult(
            island_index=0,
            core_nodes=["node1"],
            all_nodes=["node1"],
            new_topics=["topic1"],
            node_count=1,
            core_node_count=1
        )
        
        creation_time = datetime(2023, 1, 1, 12, 0, 0)
        result = GapFillingResult(
            new_topics=["topic1"],
            islands=[island_result],
            total_new_topics=1,
            total_islands=1,
            total_core_nodes=1,
            creation_time=creation_time
        )
        
        result_dict = result.to_dict()
        
        assert result_dict["new_topics"] == ["topic1"]
        assert len(result_dict["islands"]) == 1
        assert result_dict["count"] == 1
        assert result_dict["total_islands"] == 1
        assert result_dict["total_core_nodes"] == 1
        assert result_dict["timestamp"] == "20230101_120000"


class TestIdentifyIslands:
    """Test cases for the identify_islands function."""

    def test_identify_islands_single_component(self):
        """Test island identification with a single connected component."""
        # Create entities
        entities = [
            Entity(id="node1", type="test"),
            Entity(id="node2", type="test"),
            Entity(id="node3", type="test")
        ]

        # Create relationships that connect all nodes
        relationships = [
            Relationship(source_id="node1", target_id="node2", type="connects"),
            Relationship(source_id="node2", target_id="node3", type="connects")
        ]

        subgraph = SubGraph(
            entities=entities,
            relationships=relationships,
            focus_entities=["node1", "node2"],
            metadata={}
        )

        core_node_ids = ["node1", "node2"]
        islands = identify_islands(subgraph, core_node_ids)

        # Should have one island containing all nodes
        assert len(islands) == 1
        assert islands[0].nodes == {"node1", "node2", "node3"}
        assert islands[0].core_nodes == {"node1", "node2"}
        assert islands[0].context_nodes == {"node3"}

    def test_identify_islands_multiple_components(self):
        """Test island identification with multiple disconnected components."""
        # Create entities
        entities = [
            Entity(id="node1", type="test"),
            Entity(id="node2", type="test"),
            Entity(id="node3", type="test"),
            Entity(id="node4", type="test")
        ]

        # Create relationships for two separate components
        relationships = [
            Relationship(source_id="node1", target_id="node2", type="connects"),
            Relationship(source_id="node3", target_id="node4", type="connects")
        ]

        subgraph = SubGraph(
            entities=entities,
            relationships=relationships,
            focus_entities=["node1", "node3"],
            metadata={}
        )

        core_node_ids = ["node1", "node3"]
        islands = identify_islands(subgraph, core_node_ids)

        # Should have two islands
        assert len(islands) == 2

        # Sort islands by size for consistent testing
        islands.sort(key=lambda x: len(x.nodes))

        # Each island should have 2 nodes
        assert islands[0].size == 2
        assert islands[1].size == 2

        # Check that core nodes are properly assigned
        all_core_nodes = islands[0].core_nodes.union(islands[1].core_nodes)
        assert all_core_nodes == {"node1", "node3"}

    def test_identify_islands_empty_subgraph(self):
        """Test island identification with empty subgraph."""
        subgraph = SubGraph(
            entities=[],
            relationships=[],
            focus_entities=[],
            metadata={}
        )

        islands = identify_islands(subgraph, [])
        assert islands == []

    def test_identify_islands_isolated_nodes(self):
        """Test island identification with isolated nodes."""
        # Create entities with no relationships
        entities = [
            Entity(id="node1", type="test"),
            Entity(id="node2", type="test"),
            Entity(id="node3", type="test")
        ]

        subgraph = SubGraph(
            entities=entities,
            relationships=[],
            focus_entities=["node1", "node2"],
            metadata={}
        )

        core_node_ids = ["node1", "node2"]
        islands = identify_islands(subgraph, core_node_ids)

        # Should have 3 islands (one for each isolated node)
        assert len(islands) == 3

        # Each island should have exactly one node
        for island in islands:
            assert island.size == 1

        # Check that core nodes are properly identified
        all_core_nodes = set()
        for island in islands:
            all_core_nodes.update(island.core_nodes)
        assert all_core_nodes == {"node1", "node2"}


class TestMatchTopicsToNodes:
    """Test cases for the match_topics_to_nodes function."""

    def test_match_topics_to_nodes_basic(self):
        """Test basic topic to node matching."""
        topics = [
            "machine learning algorithms",
            "data processing techniques",
            "neural network architectures"
        ]

        entities = [
            Entity(id="machine learning", type="concept"),
            Entity(id="data processing", type="concept"),
            Entity(id="neural networks", type="concept"),
            Entity(id="unrelated concept", type="concept")
        ]

        subgraph = SubGraph(
            entities=entities,
            relationships=[],
            focus_entities=[],
            metadata={}
        )

        node_topic_map = match_topics_to_nodes(topics, subgraph)

        # Check that relevant nodes have been matched to topics
        assert "machine learning" in node_topic_map
        assert "data processing" in node_topic_map
        assert "neural networks" in node_topic_map

        # Check that unrelated nodes are not matched
        assert "unrelated concept" not in node_topic_map or len(node_topic_map["unrelated concept"]) == 0

    def test_match_topics_to_nodes_empty_topics(self):
        """Test topic matching with empty topics list."""
        entities = [Entity(id="node1", type="test")]
        subgraph = SubGraph(
            entities=entities,
            relationships=[],
            focus_entities=[],
            metadata={}
        )

        node_topic_map = match_topics_to_nodes([], subgraph)
        # Should return empty dict or dict with empty lists for nodes
        assert len(node_topic_map) == 0 or all(len(topics) == 0 for topics in node_topic_map.values())

    def test_match_topics_to_nodes_empty_entities(self):
        """Test topic matching with empty entities list."""
        topics = ["topic1", "topic2"]
        subgraph = SubGraph(
            entities=[],
            relationships=[],
            focus_entities=[],
            metadata={}
        )

        node_topic_map = match_topics_to_nodes(topics, subgraph)
        assert node_topic_map == {}


class TestAllocateTopics:
    """Test cases for the allocate_topics function."""

    def test_allocate_topics_equal_distribution(self):
        """Test topic allocation with equal-sized islands."""
        # Create three islands of equal size
        islands = [
            Island(nodes={"n1", "n2"}, core_nodes={"n1"}, index=0),
            Island(nodes={"n3", "n4"}, core_nodes={"n3"}, index=1),
            Island(nodes={"n5", "n6"}, core_nodes={"n5"}, index=2)
        ]

        node_topic_map = {}  # No existing coverage
        k = 6  # Total topics to allocate

        allocations = allocate_topics(islands, k, node_topic_map)

        # Should have 3 allocations
        assert len(allocations) == 3

        # Each island should get 2 topics (equal distribution)
        total_allocated = sum(alloc.allocated_topics for alloc in allocations)
        assert total_allocated == k

        # Check that each allocation has reasonable values
        for alloc in allocations:
            assert alloc.allocated_topics >= 1
            assert alloc.island_size == 2

    def test_allocate_topics_size_based_distribution(self):
        """Test topic allocation based on island sizes."""
        # Create islands of different sizes
        islands = [
            Island(nodes={"n1"}, core_nodes={"n1"}, index=0),  # Small island
            Island(nodes={"n2", "n3", "n4", "n5"}, core_nodes={"n2", "n3"}, index=1)  # Large island
        ]

        node_topic_map = {}
        k = 5

        allocations = allocate_topics(islands, k, node_topic_map)

        # Should have 2 allocations
        assert len(allocations) == 2

        # Total should equal k
        total_allocated = sum(alloc.allocated_topics for alloc in allocations)
        assert total_allocated == k

        # Larger island should get more topics
        alloc_by_index = {alloc.island_index: alloc for alloc in allocations}
        assert alloc_by_index[1].allocated_topics > alloc_by_index[0].allocated_topics

    def test_allocate_topics_zero_topics(self):
        """Test topic allocation with zero topics."""
        islands = [Island(nodes={"n1"}, core_nodes={"n1"}, index=0)]
        node_topic_map = {}

        allocations = allocate_topics(islands, 0, node_topic_map)
        # Should return one allocation with 0 topics
        assert len(allocations) == 1
        assert allocations[0].allocated_topics == 0

    def test_allocate_topics_empty_islands(self):
        """Test topic allocation with empty islands list."""
        allocations = allocate_topics([], 5, {})
        assert allocations == []


class TestAsyncFunctions:
    """Test cases for async gap filling functions."""

    @pytest.mark.asyncio
    async def test_generate_topics_for_island_success(self):
        """Test successful topic generation for an island."""
        island = Island(
            nodes={"node1", "node2"},
            core_nodes={"node1"},
            context_nodes={"node2"},
            index=0
        )

        node_topic_map = {"node1": ["existing topic"]}
        num_topics = 2
        experiment_intent = "test intent"

        # Mock the LLM response
        mock_response = '["new topic 1", "new topic 2"]'

        with patch('src.persona_topic.knowledge_graph_system_prompt.gap_filling.format_prompt_from_template', return_value="mock prompt"):
            with patch('src.persona_topic.knowledge_graph_system_prompt.gap_filling.achat_completion', new_callable=AsyncMock, return_value=mock_response):
                with patch('src.persona_topic.knowledge_graph_system_prompt.gap_filling.extract_json_from_text', return_value=["new topic 1", "new topic 2"]):

                    topics = await generate_topics_for_island(
                        island, node_topic_map, num_topics, experiment_intent
                    )

                    assert topics == ["new topic 1", "new topic 2"]
                    assert len(topics) == num_topics

    @pytest.mark.asyncio
    async def test_generate_topics_for_island_zero_topics(self):
        """Test topic generation with zero topics requested."""
        island = Island(nodes={"node1"}, index=0)

        topics = await generate_topics_for_island(
            island, {}, 0, "test intent"
        )

        assert topics == []

    @pytest.mark.asyncio
    async def test_generate_topics_for_island_llm_error(self):
        """Test topic generation when LLM fails."""
        island = Island(
            nodes={"node1"},
            core_nodes={"node1"},
            index=0
        )

        num_topics = 2

        with patch('src.persona_topic.knowledge_graph_system_prompt.gap_filling.format_prompt_from_template', return_value="mock prompt"):
            with patch('src.persona_topic.knowledge_graph_system_prompt.gap_filling.achat_completion', new_callable=AsyncMock, side_effect=Exception("LLM Error")):

                topics = await generate_topics_for_island(
                    island, {}, num_topics, "test intent"
                )

                # Should return placeholder topics on error
                assert len(topics) == num_topics
                assert all("island topic" in topic for topic in topics)

    @pytest.mark.asyncio
    async def test_generate_topics_for_island_invalid_json(self):
        """Test topic generation with invalid JSON response."""
        island = Island(
            nodes={"node1"},
            core_nodes={"node1"},
            index=0
        )

        num_topics = 2
        mock_response = "invalid json response"

        with patch('src.persona_topic.knowledge_graph_system_prompt.gap_filling.format_prompt_from_template', return_value="mock prompt"):
            with patch('src.persona_topic.knowledge_graph_system_prompt.gap_filling.achat_completion', new_callable=AsyncMock, return_value=mock_response):
                with patch('src.persona_topic.knowledge_graph_system_prompt.gap_filling.extract_json_from_text', return_value=[]):

                    topics = await generate_topics_for_island(
                        island, {}, num_topics, "test intent"
                    )

                    # Should return placeholder topics when JSON is invalid
                    assert len(topics) == num_topics

    @pytest.mark.asyncio
    async def test_generate_topics_for_island_wrong_count(self):
        """Test topic generation when LLM returns wrong number of topics."""
        island = Island(
            nodes={"node1"},
            core_nodes={"node1"},
            index=0
        )

        num_topics = 3
        mock_response = '["topic1", "topic2"]'  # Only 2 topics instead of 3

        with patch('src.persona_topic.knowledge_graph_system_prompt.gap_filling.format_prompt_from_template', return_value="mock prompt"):
            with patch('src.persona_topic.knowledge_graph_system_prompt.gap_filling.achat_completion', new_callable=AsyncMock, return_value=mock_response):
                with patch('src.persona_topic.knowledge_graph_system_prompt.gap_filling.extract_json_from_text', return_value=["topic1", "topic2"]):

                    topics = await generate_topics_for_island(
                        island, {}, num_topics, "test intent"
                    )

                    # Should pad with additional topics to reach the requested count
                    assert len(topics) == num_topics
                    assert "topic1" in topics
                    assert "topic2" in topics

    @pytest.mark.asyncio
    async def test_process_island_parallel_success(self):
        """Test successful parallel island processing."""
        island = Island(
            nodes={"node1", "node2"},
            core_nodes={"node1"},
            index=0
        )

        params = IslandProcessingParams(
            island_idx=0,
            island=island,
            num_topics=2,
            node_topic_map={"node1": ["existing topic"]},
            experiment_intent="test intent"
        )

        # Mock the generate_topics_for_island function
        with patch('src.persona_topic.knowledge_graph_system_prompt.gap_filling.generate_topics_for_island', new_callable=AsyncMock, return_value=["new topic 1", "new topic 2"]):

            result = await process_island_parallel(params)

            assert isinstance(result, IslandResult)
            assert result.island_index == 0
            assert result.new_topics == ["new topic 1", "new topic 2"]
            assert result.node_count == 2
            assert result.core_node_count == 1

    @pytest.mark.asyncio
    async def test_generate_gap_filling_topics_success(self):
        """Test successful gap filling topic generation."""
        # Create test subgraph
        entities = [
            Entity(id="node1", type="test"),
            Entity(id="node2", type="test"),
            Entity(id="node3", type="test")
        ]

        relationships = [
            Relationship(source_id="node1", target_id="node2", type="connects")
        ]

        subgraph = SubGraph(
            entities=entities,
            relationships=relationships,
            focus_entities=["node1", "node2"],
            metadata={"experiment_intent": "test intent"}
        )

        existing_topics = ["existing topic 1"]
        k = 3

        # Mock the individual functions
        mock_islands = [
            Island(nodes={"node1", "node2"}, core_nodes={"node1", "node2"}, index=0),
            Island(nodes={"node3"}, core_nodes=set(), context_nodes={"node3"}, index=1)
        ]

        mock_node_topic_map = {"node1": ["existing topic 1"]}

        mock_allocations = [
            TopicAllocation(island_index=0, island_size=2, allocated_topics=2, coverage=0.5),
            TopicAllocation(island_index=1, island_size=1, allocated_topics=1, coverage=0.0)
        ]

        mock_island_results = [
            IslandResult(
                island_index=0,
                core_nodes=["node1", "node2"],
                all_nodes=["node1", "node2"],
                new_topics=["new topic 1", "new topic 2"],
                node_count=2,
                core_node_count=2
            ),
            IslandResult(
                island_index=1,
                core_nodes=[],
                all_nodes=["node3"],
                new_topics=["new topic 3"],
                node_count=1,
                core_node_count=0
            )
        ]

        with patch('src.persona_topic.knowledge_graph_system_prompt.gap_filling.identify_islands', return_value=mock_islands):
            with patch('src.persona_topic.knowledge_graph_system_prompt.gap_filling.match_topics_to_nodes', return_value=mock_node_topic_map):
                with patch('src.persona_topic.knowledge_graph_system_prompt.gap_filling.allocate_topics', return_value=mock_allocations):
                    with patch('src.persona_topic.knowledge_graph_system_prompt.gap_filling.batch_gather', new_callable=AsyncMock, return_value=mock_island_results):

                        result = await generate_gap_filling_topics(subgraph, existing_topics, k)

                        assert isinstance(result, GapFillingResult)
                        assert result.total_new_topics == 3
                        assert result.total_islands == 2
                        assert result.total_core_nodes == 2
                        assert len(result.new_topics) == 3
                        assert len(result.islands) == 2

    @pytest.mark.asyncio
    async def test_generate_gap_filling_topics_empty_subgraph(self):
        """Test gap filling with empty subgraph."""
        subgraph = SubGraph(
            entities=[],
            relationships=[],
            focus_entities=[],
            metadata={}
        )

        result = await generate_gap_filling_topics(subgraph, [], 5)

        assert isinstance(result, GapFillingResult)
        assert result.total_new_topics == 0
        assert result.total_islands == 0
        assert result.total_core_nodes == 0
        assert result.new_topics == []
        assert result.islands == []

    @pytest.mark.asyncio
    async def test_generate_gap_filling_topics_no_core_nodes(self):
        """Test gap filling with no core nodes."""
        entities = [Entity(id="node1", type="test")]

        subgraph = SubGraph(
            entities=entities,
            relationships=[],
            focus_entities=[],  # No core nodes
            metadata={}
        )

        result = await generate_gap_filling_topics(subgraph, [], 3)

        assert isinstance(result, GapFillingResult)
        assert result.total_core_nodes == 0

    @pytest.mark.asyncio
    async def test_generate_gap_filling_topics_with_product_description(self):
        """Test gap filling with product description."""
        entities = [Entity(id="node1", type="test")]

        subgraph = SubGraph(
            entities=entities,
            relationships=[],
            focus_entities=["node1"],
            metadata={"experiment_intent": "test intent"}
        )

        # Mock the functions to verify product_description is passed through
        mock_islands = [Island(nodes={"node1"}, core_nodes={"node1"}, index=0)]
        mock_allocations = [TopicAllocation(island_index=0, island_size=1, allocated_topics=1, coverage=0.0)]
        mock_results = [IslandResult(
            island_index=0,
            core_nodes=["node1"],
            all_nodes=["node1"],
            new_topics=["topic1"],
            node_count=1,
            core_node_count=1
        )]

        with patch('src.persona_topic.knowledge_graph_system_prompt.gap_filling.identify_islands', return_value=mock_islands):
            with patch('src.persona_topic.knowledge_graph_system_prompt.gap_filling.match_topics_to_nodes', return_value={}):
                with patch('src.persona_topic.knowledge_graph_system_prompt.gap_filling.allocate_topics', return_value=mock_allocations):
                    with patch('src.persona_topic.knowledge_graph_system_prompt.gap_filling.batch_gather', new_callable=AsyncMock, return_value=mock_results):

                        result = await generate_gap_filling_topics(
                            subgraph, [], 1, product_description="test product"
                        )

                        assert isinstance(result, GapFillingResult)
                        assert result.total_new_topics == 1
