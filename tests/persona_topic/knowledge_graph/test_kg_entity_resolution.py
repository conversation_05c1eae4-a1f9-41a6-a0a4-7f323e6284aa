import pytest
import asyncio
from unittest.mock import patch, AsyncMock, MagicMock
import json

from langchain_community.graphs.graph_document import Node, Relationship as LCRelationship

from src.persona_topic.knowledge_graph_system_prompt.entity_resolution import (
    normalize_entity,
    exact_match_resolution,
    update_relationships,
    process_entity_batch,
    batch_resolve_with_llm,
    merge_entities
)
from src.persona_topic.knowledge_graph_system_prompt.models import Relationship, EntityResolutionResult


class TestNormalizeEntity:
    """Test cases for the normalize_entity function."""

    def test_basic_normalization(self):
        """Test basic normalization - lowercase and whitespace."""
        result = normalize_entity("  COMPANY NAME  ")
        assert result == "company name"

    def test_remove_punctuation(self):
        """Test removal of common punctuation."""
        result = normalize_entity("Company, Inc.; Division:")
        assert result == "company incorporated division"

    def test_expand_abbreviations(self):
        """Test expansion of common abbreviations."""
        test_cases = [
            ("TechCorp Inc.", "techcorp incorporated"),
            ("ABC Corp.", "abc corporation"),
            ("XYZ Ltd.", "xyz limited"),
            ("MyCompany Co.", "mycompany company"),
            ("StartupLLC", "startuplimited liability company"),  # LLC gets expanded
            ("Global Intl.", "global international"),
            ("US Govt.", "us government"),
            ("IT Dept.", "it department"),
            ("State Univ.", "state university"),
            ("Trade Assoc.", "trade association"),
            ("Non-profit Org.", "non-profit organization")
        ]
        
        for input_name, expected in test_cases:
            result = normalize_entity(input_name)
            assert result == expected, f"Failed for '{input_name}': expected '{expected}', got '{result}'"

    def test_multiple_whitespace(self):
        """Test handling of multiple whitespace characters."""
        result = normalize_entity("Company    Name   With    Spaces")
        assert result == "company name with spaces"

    def test_complex_normalization(self):
        """Test complex case with multiple normalizations."""
        result = normalize_entity("  ACME Corp.,   Inc.;  International  ")
        assert result == "acme corporation incorporated international"

    def test_empty_and_edge_cases(self):
        """Test edge cases like empty strings."""
        assert normalize_entity("") == ""
        assert normalize_entity("   ") == ""
        assert normalize_entity("a") == "a"


class TestExactMatchResolution:
    """Test cases for the exact_match_resolution function."""

    def test_no_matches(self):
        """Test when there are no exact matches."""
        nodes = [
            Node(id="Company A"),
            Node(id="Company B"),
            Node(id="Company C")
        ]
        
        result = exact_match_resolution(nodes)
        assert result == {}

    def test_single_exact_match_group(self):
        """Test when there's one group of exact matches."""
        nodes = [
            Node(id="ACME Corp."),
            Node(id="acme corporation"),
            Node(id="Different Company")
        ]
        
        result = exact_match_resolution(nodes)
        
        # Both should map to the first one (canonical)
        expected_canonical = "ACME Corp."
        assert result["ACME Corp."] == expected_canonical
        assert result["acme corporation"] == expected_canonical
        assert "Different Company" not in result

    def test_multiple_exact_match_groups(self):
        """Test when there are multiple groups of exact matches."""
        nodes = [
            Node(id="ACME Corp."),
            Node(id="acme corporation"),
            Node(id="TechCorp Inc."),
            Node(id="techcorp incorporated"),
            Node(id="Unique Company")
        ]
        
        result = exact_match_resolution(nodes)
        
        # Check ACME group
        acme_canonical = "ACME Corp."
        assert result["ACME Corp."] == acme_canonical
        assert result["acme corporation"] == acme_canonical
        
        # Check TechCorp group
        tech_canonical = "TechCorp Inc."
        assert result["TechCorp Inc."] == tech_canonical
        assert result["techcorp incorporated"] == tech_canonical
        
        # Unique company should not be in mapping
        assert "Unique Company" not in result

    def test_three_way_match(self):
        """Test when three entities match."""
        nodes = [
            Node(id="ABC Inc."),
            Node(id="abc incorporated"),
            Node(id="ABC   Inc."),  # Extra whitespace
        ]
        
        result = exact_match_resolution(nodes)
        
        canonical = "ABC Inc."
        assert result["ABC Inc."] == canonical
        assert result["abc incorporated"] == canonical
        assert result["ABC   Inc."] == canonical

    def test_empty_node_list(self):
        """Test with empty node list."""
        result = exact_match_resolution([])
        assert result == {}

    def test_single_node(self):
        """Test with single node."""
        nodes = [Node(id="Single Company")]
        result = exact_match_resolution(nodes)
        assert result == {}


class TestUpdateRelationships:
    """Test cases for the update_relationships function."""

    def test_no_updates_needed(self):
        """Test when no relationship updates are needed."""
        # Create actual Node objects
        source_node = Node(id="Company A")
        target_node = Node(id="Company B")

        # Create LangChain relationship
        lc_rel = LCRelationship(
            source=source_node,
            target=target_node,
            type="partnership",
            properties={"since": "2020"}
        )
        
        relationships = [lc_rel]
        node_mapping = {}  # No mappings
        
        result = update_relationships(relationships, node_mapping)
        
        assert len(result) == 1
        assert isinstance(result[0], Relationship)
        assert result[0].source_id == "Company A"
        assert result[0].target_id == "Company B"
        assert result[0].type == "partnership"
        assert result[0].properties == {"since": "2020"}

    def test_source_node_updated(self):
        """Test when source node needs to be updated."""
        source_node = Node(id="ACME Corp.")
        target_node = Node(id="Company B")

        lc_rel = LCRelationship(
            source=source_node,
            target=target_node,
            type="partnership"
        )
        
        relationships = [lc_rel]
        node_mapping = {"ACME Corp.": "acme corporation"}
        
        result = update_relationships(relationships, node_mapping)
        
        assert len(result) == 1
        assert result[0].source_id == "acme corporation"
        assert result[0].target_id == "Company B"
        assert result[0].type == "partnership"

    def test_target_node_updated(self):
        """Test when target node needs to be updated."""
        source_node = Node(id="Company A")
        target_node = Node(id="TechCorp Inc.")

        lc_rel = LCRelationship(
            source=source_node,
            target=target_node,
            type="acquisition"
        )
        
        relationships = [lc_rel]
        node_mapping = {"TechCorp Inc.": "techcorp incorporated"}
        
        result = update_relationships(relationships, node_mapping)
        
        assert len(result) == 1
        assert result[0].source_id == "Company A"
        assert result[0].target_id == "techcorp incorporated"
        assert result[0].type == "acquisition"

    def test_both_nodes_updated(self):
        """Test when both source and target nodes need to be updated."""
        source_node = Node(id="ACME Corp.")
        target_node = Node(id="TechCorp Inc.")

        lc_rel = LCRelationship(
            source=source_node,
            target=target_node,
            type="merger",
            properties={"date": "2023-01-01"}
        )
        
        relationships = [lc_rel]
        node_mapping = {
            "ACME Corp.": "acme corporation",
            "TechCorp Inc.": "techcorp incorporated"
        }
        
        result = update_relationships(relationships, node_mapping)
        
        assert len(result) == 1
        assert result[0].source_id == "acme corporation"
        assert result[0].target_id == "techcorp incorporated"
        assert result[0].type == "merger"
        assert result[0].properties == {"date": "2023-01-01"}

    def test_multiple_relationships(self):
        """Test updating multiple relationships."""
        # Create multiple relationships
        source1 = Node(id="Company A")
        target1 = Node(id="ACME Corp.")

        source2 = Node(id="TechCorp Inc.")
        target2 = Node(id="Company D")

        rel1 = LCRelationship(source=source1, target=target1, type="partnership")
        rel2 = LCRelationship(source=source2, target=target2, type="acquisition")
        
        relationships = [rel1, rel2]
        node_mapping = {
            "ACME Corp.": "acme corporation",
            "TechCorp Inc.": "techcorp incorporated"
        }
        
        result = update_relationships(relationships, node_mapping)
        
        assert len(result) == 2
        
        # Check first relationship
        assert result[0].source_id == "Company A"
        assert result[0].target_id == "acme corporation"
        assert result[0].type == "partnership"
        
        # Check second relationship
        assert result[1].source_id == "techcorp incorporated"
        assert result[1].target_id == "Company D"
        assert result[1].type == "acquisition"

    def test_empty_relationships(self):
        """Test with empty relationships list."""
        result = update_relationships([], {"some": "mapping"})
        assert result == []

    def test_relationship_with_properties_preserved(self):
        """Test that relationship properties are preserved during updates."""
        source_node = Node(id="Company A")
        target_node = Node(id="Company B")

        properties = {
            "start_date": "2020-01-01",
            "strength": "strong",
            "type": "strategic",
            "metadata": {"source": "annual_report"}
        }

        lc_rel = LCRelationship(
            source=source_node,
            target=target_node,
            type="partnership",
            properties=properties
        )

        relationships = [lc_rel]
        node_mapping = {"Company A": "company_a_canonical"}

        result = update_relationships(relationships, node_mapping)

        assert len(result) == 1
        assert result[0].source_id == "company_a_canonical"
        assert result[0].target_id == "Company B"
        assert result[0].type == "partnership"
        assert result[0].properties == properties

    def test_relationship_no_properties(self):
        """Test relationship update when original has no properties."""
        source_node = Node(id="Entity1")
        target_node = Node(id="Entity2")

        # Create relationship without properties
        lc_rel = LCRelationship(
            source=source_node,
            target=target_node,
            type="related_to"
        )

        relationships = [lc_rel]
        node_mapping = {"Entity1": "entity_1_merged"}

        result = update_relationships(relationships, node_mapping)

        assert len(result) == 1
        assert result[0].source_id == "entity_1_merged"
        assert result[0].target_id == "Entity2"
        assert result[0].type == "related_to"
        assert result[0].properties == {}  # Should default to empty dict


class TestEntityResolutionEdgeCases:
    """Test edge cases and integration scenarios for entity resolution."""

    def test_normalize_entity_with_unicode(self):
        """Test normalize_entity with unicode characters."""
        result = normalize_entity("Café Inc.")
        assert result == "café incorporated"

    def test_normalize_entity_with_numbers(self):
        """Test normalize_entity with numbers and special characters."""
        result = normalize_entity("Company-123 Corp.")
        assert result == "company-123 corporation"

    def test_exact_match_resolution_with_properties(self):
        """Test exact match resolution with nodes that have properties."""
        nodes = [
            Node(id="ACME Corp.", properties={"type": "company", "sector": "tech"}),
            Node(id="acme corporation", properties={"type": "organization", "industry": "technology"}),
            Node(id="Different Co.", properties={"type": "company"})
        ]

        result = exact_match_resolution(nodes)

        # Should still match despite different properties
        canonical = "ACME Corp."
        assert result["ACME Corp."] == canonical
        assert result["acme corporation"] == canonical
        assert "Different Co." not in result

    def test_exact_match_resolution_case_sensitivity(self):
        """Test that exact match resolution is case insensitive."""
        nodes = [
            Node(id="MICROSOFT CORPORATION"),
            Node(id="Microsoft Corporation"),
            Node(id="microsoft corporation"),
            Node(id="Microsoft Corp."),
        ]

        result = exact_match_resolution(nodes)

        # All should map to the first one (canonical)
        canonical = "MICROSOFT CORPORATION"
        assert result["MICROSOFT CORPORATION"] == canonical
        assert result["Microsoft Corporation"] == canonical
        assert result["microsoft corporation"] == canonical
        assert result["Microsoft Corp."] == canonical

    def test_exact_match_resolution_whitespace_variations(self):
        """Test exact match resolution with various whitespace patterns."""
        nodes = [
            Node(id="Tech   Company"),
            Node(id="tech company"),
            Node(id="Tech\tCompany"),  # Tab character
            Node(id="  Tech Company  "),  # Leading/trailing spaces
        ]

        result = exact_match_resolution(nodes)

        canonical = "Tech   Company"
        assert result["Tech   Company"] == canonical
        assert result["tech company"] == canonical
        assert result["Tech\tCompany"] == canonical
        assert result["  Tech Company  "] == canonical

    def test_update_relationships_self_referencing(self):
        """Test updating relationships where source and target are the same."""
        node = Node(id="Company A")

        # Self-referencing relationship
        lc_rel = LCRelationship(
            source=node,
            target=node,
            type="subsidiary_of",
            properties={"note": "parent company"}
        )

        relationships = [lc_rel]
        node_mapping = {"Company A": "company_a_merged"}

        result = update_relationships(relationships, node_mapping)

        assert len(result) == 1
        assert result[0].source_id == "company_a_merged"
        assert result[0].target_id == "company_a_merged"
        assert result[0].type == "subsidiary_of"
        assert result[0].properties == {"note": "parent company"}

    def test_update_relationships_circular_mapping(self):
        """Test relationship updates with complex node mappings."""
        source_node = Node(id="Old Company A")
        target_node = Node(id="Old Company B")

        lc_rel = LCRelationship(
            source=source_node,
            target=target_node,
            type="partnership"
        )

        relationships = [lc_rel]
        # Both nodes map to different canonical forms
        node_mapping = {
            "Old Company A": "New Company Alpha",
            "Old Company B": "New Company Beta"
        }

        result = update_relationships(relationships, node_mapping)

        assert len(result) == 1
        assert result[0].source_id == "New Company Alpha"
        assert result[0].target_id == "New Company Beta"
        assert result[0].type == "partnership"

    def test_normalize_entity_all_abbreviations(self):
        """Test that all defined abbreviations are properly expanded."""
        abbreviation_tests = [
            ("Test Inc.", "test incorporated"),
            ("Example Corp.", "example corporation"),
            ("Sample Ltd.", "sample limited"),
            ("Demo Co.", "demo company"),
            ("Global Intl.", "global international"),
            ("Federal Govt.", "federal government"),
            ("Marketing Dept.", "marketing department"),
            ("State Univ.", "state university"),
            ("Professional Assoc.", "professional association"),
            ("Charity Org.", "charity organization"),
        ]

        for input_text, expected in abbreviation_tests:
            result = normalize_entity(input_text)
            assert result == expected, f"Failed for '{input_text}': expected '{expected}', got '{result}'"

    def test_exact_match_resolution_large_group(self):
        """Test exact match resolution with a large group of matching entities."""
        # Create many variations of the same entity
        base_names = [
            "ACME Corporation",
            "acme corporation",
            "ACME Corp.",
            "acme corp.",
            "Acme Corporation",
            "Acme Corp.",
            "ACME   Corporation",  # Extra spaces
            "acme   corp.",
        ]

        nodes = [Node(id=name) for name in base_names]

        result = exact_match_resolution(nodes)

        # All should map to the first one
        canonical = "ACME Corporation"
        for name in base_names:
            assert result[name] == canonical

    def test_exact_match_resolution_no_false_positives(self):
        """Test that exact match resolution doesn't create false positives."""
        nodes = [
            Node(id="Apple Inc."),
            Node(id="Apple Computer Inc."),  # Different entity
            Node(id="Microsoft Corp."),
            Node(id="Microsoft Corporation"),  # Same entity
            Node(id="Google LLC"),
            Node(id="Alphabet Inc."),  # Different entity despite relationship
        ]

        result = exact_match_resolution(nodes)

        # Only Microsoft variations should match
        assert "Microsoft Corp." in result
        assert "Microsoft Corporation" in result
        assert result["Microsoft Corp."] == result["Microsoft Corporation"]

        # These should NOT match each other
        assert "Apple Inc." not in result or result.get("Apple Inc.") != result.get("Apple Computer Inc.", "different")
        assert "Google LLC" not in result or result.get("Google LLC") != result.get("Alphabet Inc.", "different")


class TestAsyncEntityResolution:
    """Test cases for async entity resolution functions."""

    @pytest.mark.asyncio
    async def test_process_entity_batch_success(self):
        """Test successful processing of an entity batch."""
        # Create test nodes
        nodes = [
            Node(id="ACME Corp.", properties={"type": "company"}),
            Node(id="acme corporation", properties={"type": "organization"}),
            Node(id="Different Company")
        ]

        # Mock the dependencies
        mock_prompt = "mocked prompt"
        mock_llm_response = '{"acme corporation": "ACME Corp."}'

        with patch('src.persona_topic.knowledge_graph_system_prompt.entity_resolution.format_prompt_from_template', return_value=mock_prompt) as mock_format:
            with patch('src.persona_topic.knowledge_graph_system_prompt.entity_resolution.achat_completion', new_callable=AsyncMock, return_value=mock_llm_response) as mock_achat:

                result = await process_entity_batch(nodes, batch_num=0, metadata={"test": "data"})

                # Verify the mocks were called correctly
                mock_format.assert_called_once()
                mock_achat.assert_called_once()

                # Check the call arguments
                format_args = mock_format.call_args
                assert format_args[0][0] == "knowledge_graph/entity_resolution.jinja2"

                achat_args = mock_achat.call_args
                assert achat_args[1]["model"] == "gpt-4.1-mini"
                assert achat_args[1]["metadata"] == {"test": "data"}

                # Verify the result
                expected_result = {
                    "acme corporation": "ACME Corp.",
                    "ACME Corp.": "ACME Corp."  # Canonical maps to itself
                }
                assert result == expected_result

    @pytest.mark.asyncio
    async def test_process_entity_batch_with_markdown_json(self):
        """Test processing when LLM returns JSON wrapped in markdown."""
        nodes = [
            Node(id="Company A"),
            Node(id="Company B")
        ]

        # Mock LLM response with markdown code blocks
        mock_llm_response = '''```json
{"Company B": "Company A"}
```'''

        with patch('src.persona_topic.knowledge_graph_system_prompt.entity_resolution.format_prompt_from_template', return_value="prompt"):
            with patch('src.persona_topic.knowledge_graph_system_prompt.entity_resolution.achat_completion', new_callable=AsyncMock, return_value=mock_llm_response):

                result = await process_entity_batch(nodes, batch_num=0)

                expected_result = {
                    "Company B": "Company A",
                    "Company A": "Company A"
                }
                assert result == expected_result

    @pytest.mark.asyncio
    async def test_process_entity_batch_with_generic_markdown(self):
        """Test processing when LLM returns JSON wrapped in generic markdown."""
        nodes = [Node(id="Entity1"), Node(id="Entity2")]

        # Mock LLM response with generic markdown
        mock_llm_response = '''```
{"Entity2": "Entity1"}
```'''

        with patch('src.persona_topic.knowledge_graph_system_prompt.entity_resolution.format_prompt_from_template', return_value="prompt"):
            with patch('src.persona_topic.knowledge_graph_system_prompt.entity_resolution.achat_completion', new_callable=AsyncMock, return_value=mock_llm_response):

                result = await process_entity_batch(nodes, batch_num=0)

                expected_result = {
                    "Entity2": "Entity1",
                    "Entity1": "Entity1"
                }
                assert result == expected_result

    @pytest.mark.asyncio
    async def test_process_entity_batch_json_decode_error(self):
        """Test handling of JSON decode errors."""
        nodes = [Node(id="Test Entity")]

        # Mock invalid JSON response
        mock_llm_response = "invalid json response"

        with patch('src.persona_topic.knowledge_graph_system_prompt.entity_resolution.format_prompt_from_template', return_value="prompt"):
            with patch('src.persona_topic.knowledge_graph_system_prompt.entity_resolution.achat_completion', new_callable=AsyncMock, return_value=mock_llm_response):

                result = await process_entity_batch(nodes, batch_num=0)

                # Should return empty dict on JSON error
                assert result == {}

    @pytest.mark.asyncio
    async def test_process_entity_batch_exception_handling(self):
        """Test handling of general exceptions."""
        nodes = [Node(id="Test Entity")]

        with patch('src.persona_topic.knowledge_graph_system_prompt.entity_resolution.format_prompt_from_template', return_value="prompt"):
            with patch('src.persona_topic.knowledge_graph_system_prompt.entity_resolution.achat_completion', new_callable=AsyncMock, side_effect=Exception("API Error")):

                result = await process_entity_batch(nodes, batch_num=0)

                # Should return empty dict on exception
                assert result == {}

    @pytest.mark.asyncio
    async def test_process_entity_batch_empty_result(self):
        """Test processing when LLM returns empty result."""
        nodes = [Node(id="Entity1"), Node(id="Entity2")]

        # Mock empty JSON response
        mock_llm_response = "{}"

        with patch('src.persona_topic.knowledge_graph_system_prompt.entity_resolution.format_prompt_from_template', return_value="prompt"):
            with patch('src.persona_topic.knowledge_graph_system_prompt.entity_resolution.achat_completion', new_callable=AsyncMock, return_value=mock_llm_response):

                result = await process_entity_batch(nodes, batch_num=0)

                # Should return empty dict when no merges found
                assert result == {}

    @pytest.mark.asyncio
    async def test_process_entity_batch_canonical_not_in_batch(self):
        """Test when suggested canonical entity is not in the batch."""
        nodes = [
            Node(id="Entity A"),
            Node(id="Entity B")
        ]

        # Mock response suggesting a canonical that doesn't exist
        mock_llm_response = '{"Entity A": "NonExistent Entity", "Entity B": "NonExistent Entity"}'

        with patch('src.persona_topic.knowledge_graph_system_prompt.entity_resolution.format_prompt_from_template', return_value="prompt"):
            with patch('src.persona_topic.knowledge_graph_system_prompt.entity_resolution.achat_completion', new_callable=AsyncMock, return_value=mock_llm_response):

                result = await process_entity_batch(nodes, batch_num=0)

                # Should use first variant as canonical
                expected_result = {
                    "Entity B": "Entity A",
                    "Entity A": "Entity A"
                }
                assert result == expected_result

    @pytest.mark.asyncio
    async def test_batch_resolve_with_llm_success(self):
        """Test successful batch resolution with LLM."""
        nodes = [
            Node(id="Microsoft Corp.", properties={"type": "company"}),
            Node(id="Microsoft Corporation", properties={"type": "company"}),
            Node(id="Apple Inc.", properties={"type": "company"}),
            Node(id="Apple Computer Inc.", properties={"type": "company"})
        ]

        # Mock batch_gather to return results from process_entity_batch
        mock_batch_results = [
            {"Microsoft Corporation": "Microsoft Corp.", "Microsoft Corp.": "Microsoft Corp."},
            {"Apple Computer Inc.": "Apple Inc.", "Apple Inc.": "Apple Inc."}
        ]

        with patch('src.persona_topic.knowledge_graph_system_prompt.entity_resolution.batch_gather', new_callable=AsyncMock, return_value=mock_batch_results):

            result = await batch_resolve_with_llm(nodes, batch_size=2)

            expected_result = {
                "Microsoft Corporation": "Microsoft Corp.",
                "Microsoft Corp.": "Microsoft Corp.",
                "Apple Computer Inc.": "Apple Inc.",
                "Apple Inc.": "Apple Inc."
            }
            assert result == expected_result

    @pytest.mark.asyncio
    async def test_batch_resolve_with_llm_with_existing_mapping(self):
        """Test batch resolution with existing mapping."""
        nodes = [
            Node(id="Entity A"),
            Node(id="Entity B"),
            Node(id="Entity C"),
            Node(id="Entity D")
        ]

        # Existing mapping for some entities
        existing_mapping = {"Entity A": "canonical_a", "Entity B": "canonical_b"}

        # Mock batch_gather - should only process unmapped entities
        mock_batch_results = [
            {"Entity D": "Entity C", "Entity C": "Entity C"}
        ]

        with patch('src.persona_topic.knowledge_graph_system_prompt.entity_resolution.batch_gather', new_callable=AsyncMock, return_value=mock_batch_results):

            result = await batch_resolve_with_llm(nodes, existing_mapping=existing_mapping, batch_size=2)

            # Should only return mappings for unmapped entities
            expected_result = {
                "Entity D": "Entity C",
                "Entity C": "Entity C"
            }
            assert result == expected_result

    @pytest.mark.asyncio
    async def test_batch_resolve_with_llm_insufficient_nodes(self):
        """Test batch resolution with insufficient nodes."""
        # Only one unmapped node
        nodes = [Node(id="Single Entity")]
        existing_mapping = {}

        result = await batch_resolve_with_llm(nodes, existing_mapping=existing_mapping)

        # Should return empty dict when less than 2 unmapped nodes
        assert result == {}

    @pytest.mark.asyncio
    async def test_batch_resolve_with_llm_all_nodes_mapped(self):
        """Test batch resolution when all nodes are already mapped."""
        nodes = [Node(id="Entity A"), Node(id="Entity B")]
        existing_mapping = {"Entity A": "canonical_a", "Entity B": "canonical_b"}

        result = await batch_resolve_with_llm(nodes, existing_mapping=existing_mapping)

        # Should return empty dict when all nodes are already mapped
        assert result == {}

    @pytest.mark.asyncio
    async def test_batch_resolve_with_llm_entity_types_analysis(self):
        """Test that entity types are properly analyzed."""
        nodes = [
            Node(id="Company A", properties={"type": "corporation"}),
            Node(id="Company B", properties={"type": "corporation"}),
            Node(id="Person A", properties={"type": "individual"}),
            Node(id="Person B", properties={"type": "individual"}),
        ] + [Node(id=f"Entity {i}") for i in range(20)]  # Add more to test sampling

        mock_batch_results = [{}]  # Empty results for simplicity

        with patch('src.persona_topic.knowledge_graph_system_prompt.entity_resolution.batch_gather', new_callable=AsyncMock, return_value=mock_batch_results):

            result = await batch_resolve_with_llm(nodes, batch_size=25)

            # Function should complete without error
            assert result == {}

    @pytest.mark.asyncio
    async def test_batch_resolve_with_llm_none_existing_mapping(self):
        """Test batch resolution with None existing mapping."""
        nodes = [Node(id="Entity A"), Node(id="Entity B")]

        mock_batch_results = [{"Entity B": "Entity A", "Entity A": "Entity A"}]

        with patch('src.persona_topic.knowledge_graph_system_prompt.entity_resolution.batch_gather', new_callable=AsyncMock, return_value=mock_batch_results):

            result = await batch_resolve_with_llm(nodes, existing_mapping=None, batch_size=2)

            expected_result = {
                "Entity B": "Entity A",
                "Entity A": "Entity A"
            }
            assert result == expected_result

    @pytest.mark.asyncio
    async def test_merge_entities_integration(self):
        """Test the main merge_entities function integration."""
        nodes = [
            Node(id="ACME Corp.", properties={"type": "company", "sector": "tech"}),
            Node(id="acme corporation", properties={"type": "organization"}),
            Node(id="Microsoft Corp.", properties={"type": "company"}),
            Node(id="Microsoft Corporation", properties={"type": "company"}),
            Node(id="Unique Company", properties={"type": "company"})
        ]

        # Mock the LLM resolution to return some mappings
        mock_llm_mapping = {"Microsoft Corporation": "Microsoft Corp."}

        with patch('src.persona_topic.knowledge_graph_system_prompt.entity_resolution.batch_resolve_with_llm', new_callable=AsyncMock, return_value=mock_llm_mapping):

            merged_nodes, resolution_result = await merge_entities(nodes, metadata={"test": "integration"})

            # Verify the result structure
            assert isinstance(merged_nodes, list)
            assert isinstance(resolution_result, EntityResolutionResult)

            # Check resolution result
            assert resolution_result.original_count == 5
            assert resolution_result.merged_count == len(merged_nodes)
            assert resolution_result.merged_count < resolution_result.original_count  # Some merging should occur

            # Verify that exact matches were found (ACME variants)
            merged_ids = [node.id for node in merged_nodes]
            assert "ACME Corp." in merged_ids  # Canonical form should be kept
            assert "acme corporation" not in merged_ids  # Variant should be merged

            # Verify that LLM mapping was applied
            assert "Microsoft Corp." in merged_ids
            assert "Microsoft Corporation" not in merged_ids  # Should be merged

            # Verify unique company is preserved
            assert "Unique Company" in merged_ids

    @pytest.mark.asyncio
    async def test_merge_entities_no_duplicates(self):
        """Test merge_entities with no duplicates."""
        nodes = [
            Node(id="Company A", properties={"type": "company"}),
            Node(id="Company B", properties={"type": "company"}),
            Node(id="Company C", properties={"type": "company"})
        ]

        # Mock LLM to return no mappings
        with patch('src.persona_topic.knowledge_graph_system_prompt.entity_resolution.batch_resolve_with_llm', new_callable=AsyncMock, return_value={}):

            merged_nodes, resolution_result = await merge_entities(nodes)

            # No merging should occur
            assert len(merged_nodes) == 3
            assert resolution_result.original_count == 3
            assert resolution_result.merged_count == 3
            assert resolution_result.reduction_count == 0
            assert resolution_result.reduction_percentage == 0.0

    @pytest.mark.asyncio
    async def test_merge_entities_canonical_id_different(self):
        """Test merge_entities when canonical ID is different from original."""
        nodes = [
            Node(id="Original Entity", properties={"data": "test"}),
            Node(id="Variant Entity", properties={"data": "test2"})
        ]

        # Mock exact matching to create a mapping
        mock_llm_mapping = {"Variant Entity": "Original Entity"}

        with patch('src.persona_topic.knowledge_graph_system_prompt.entity_resolution.batch_resolve_with_llm', new_callable=AsyncMock, return_value=mock_llm_mapping):

            merged_nodes, resolution_result = await merge_entities(nodes)

            # Should have one merged node with canonical ID
            assert len(merged_nodes) == 1
            assert merged_nodes[0].id == "Original Entity"

            # Check resolution result
            assert resolution_result.original_count == 2
            assert resolution_result.merged_count == 1
            assert resolution_result.reduction_count == 1
            assert resolution_result.reduction_percentage == 50.0

    @pytest.mark.asyncio
    async def test_merge_entities_empty_nodes(self):
        """Test merge_entities with empty node list."""
        nodes = []

        with patch('src.persona_topic.knowledge_graph_system_prompt.entity_resolution.batch_resolve_with_llm', new_callable=AsyncMock, return_value={}):

            merged_nodes, resolution_result = await merge_entities(nodes)

            assert len(merged_nodes) == 0
            assert resolution_result.original_count == 0
            assert resolution_result.merged_count == 0
            assert resolution_result.reduction_count == 0
            assert resolution_result.reduction_percentage == 0.0

    @pytest.mark.asyncio
    async def test_merge_entities_single_node(self):
        """Test merge_entities with single node."""
        nodes = [Node(id="Single Entity", properties={"test": "data"})]

        with patch('src.persona_topic.knowledge_graph_system_prompt.entity_resolution.batch_resolve_with_llm', new_callable=AsyncMock, return_value={}):

            merged_nodes, resolution_result = await merge_entities(nodes)

            assert len(merged_nodes) == 1
            assert merged_nodes[0].id == "Single Entity"
            assert merged_nodes[0].properties == {"test": "data"}
            assert resolution_result.original_count == 1
            assert resolution_result.merged_count == 1
