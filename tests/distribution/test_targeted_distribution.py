from src.persona_topic.distribution import (
    KnowledgeBaseTargetedDistribution,
    HistoricalDataTargetedDistribution,
    AppDescriptionTargetedDistribution,
    RisksSelectedTargetedDistribution,
)

from src.persona_topic.models import DataGenSource


def test_knowledge_base_targeted_distribution():
    """Test that KnowledgeBaseTargetedDistribution allocates all personas to knowledge base."""
    strategy = KnowledgeBaseTargetedDistribution()
    max_personas = 20

    distribution = strategy.distribute(max_personas=max_personas)

    # Check that all personas are allocated to knowledge base
    assert distribution == {DataGenSource.KNOWLEDGE_BASE: max_personas}

    # Check that total allocation equals max_personas
    assert sum(distribution.values()) == max_personas


def test_historical_data_targeted_distribution():
    """Test that HistoricalDataTargetedDistribution allocates all personas to historical data."""
    strategy = HistoricalDataTargetedDistribution()
    max_personas = 15

    distribution = strategy.distribute(max_personas=max_personas)

    # Check that all personas are allocated to historical data
    assert distribution == {DataGenSource.HISTORICAL_DATA: max_personas}

    # Check that total allocation equals max_personas
    assert sum(distribution.values()) == max_personas


def test_app_description_targeted_distribution():
    """Test that AppDescriptionTargetedDistribution allocates all personas to app description."""
    strategy = AppDescriptionTargetedDistribution()
    max_personas = 10

    distribution = strategy.distribute(max_personas=max_personas)

    # Check that all personas are allocated to app description
    assert distribution == {DataGenSource.APP_DESCRIPTION: max_personas}

    # Check that total allocation equals max_personas
    assert sum(distribution.values()) == max_personas


def test_risks_selected_targeted_distribution():
    """Test that RisksSelectedTargetedDistribution allocates all personas to risks selected."""
    strategy = RisksSelectedTargetedDistribution()
    max_personas = 25

    distribution = strategy.distribute(max_personas=max_personas)

    # Check that all personas are allocated to risks selected
    assert distribution == {DataGenSource.RISKS_SELECTED: max_personas}

    # Check that total allocation equals max_personas
    assert sum(distribution.values()) == max_personas


def test_targeted_distributions_with_extra_parameters():
    """Test that targeted distributions ignore extra parameters."""
    strategy = KnowledgeBaseTargetedDistribution()
    max_personas = 20

    # Extra parameters should be ignored
    distribution = strategy.distribute(
        max_personas=max_personas,
        num_risks=5,
        historical_data_present=True,
        knowledge_base_present=True,
        data_gen_mode_majority_fraction=0.75,
    )

    # Check that all personas are still allocated to knowledge base
    assert distribution == {DataGenSource.KNOWLEDGE_BASE: max_personas}
